<script>
  window.onload = function () {
    const url = new URL(window.location.href);
    // 除了redirectUrl，其他参数都传递给登录页
    const params = url.searchParams;
    const redirectUrl = decodeURI(url.searchParams.get('redirectUrl'));

    params.delete('redirectUrl');
    const search = params.toString();
    // 如果有redirectUrl参数，登录成功后跳转  
    if (redirectUrl) {
      window.location.href = redirectUrl + (search ? (redirectUrl.indexOf('?') > -1 ? '&' : '?') + search : '');
    }
  }
</script>