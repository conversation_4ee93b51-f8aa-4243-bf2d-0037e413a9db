<template>
  <section class="content">
    <div v-show="selCompanyIndex==-1" style="text-align:center;width:100%;margin-top:50px;">暂无账户</div>
    <div v-show="selCompanyIndex!=-1" style="flex-grow:5;">
      <div class="bodd minh30 flexd flexdire  justaround mg30" >
        <div class="flexbet fcenter">
             <div >备用金账户：
              <el-select v-model="selCompanyIndex" @change="changeCompany" placeholder="请选择公司">
                <el-option v-for="(item,index) in fundList"
                  :key="index"
                  :label="item.companyName"
                  :value="index">
                </el-option>
              </el-select>
            </div>
            <div>余额：{{selComDetail.balance|moneyFmt}}</div>
            <div class="flexbet fcenter">
              <el-button type="primary" @click="loadApplyDialog">申请备用金</el-button>
              <!-- <br/>并上报本期账单 -->
             <el-button type="primary" plain @click="addRecord(undefined,undefined,true)">上报本期账单</el-button>
            </div>
        </div>
        <div class="flexbet fcenter font13">
          <div class="flexbet fcenter">
           本期账单支出：{{applyDetail.totalExpenditure}} &nbsp;&nbsp;<el-button @click="addRecord"  type="primary" plain >录入支出</el-button>
          </div>
          <div class="flexbet fcenter">
           本期账单收入：{{applyDetail.incomeBus}} &nbsp;&nbsp;<el-button plain @click="addRecord(undefined,'income')" >录入收入</el-button>
          </div>
        </div>

      </div>
      <div class="mg30">
        <div class="flexbet fcenter">
          <div>
            付款日期：
              <el-date-picker
            v-model="queryRecordFrom.dates"
            type="monthrange"
            @change="queryPageRecord"
            value-format="yyyy-MM"
            range-separator="-"
            start-placeholder="开始月份"
            end-placeholder="结束月份">
          </el-date-picker>
          </div>
            <div>
              <el-tag class="curpoi" :type="queryRecordFrom.isIncomeStatus==1?'success':'info'" @click="selStatus(1)">支出</el-tag>
              <el-tag class="curpoi" :type="queryRecordFrom.isIncomeStatus==2?'success':'info'" @click="selStatus(2)">收入</el-tag>
            </div>

          <!-- <div style="width:230px;text-align:right;">
            <div v-show="queryRecordFrom.isIncomeStatus==1">
            <el-tag class="curpoi"  :type="queryRecordFrom.isPriceIncome==1?'success':'info'"  @click="selPriInc(1)">日常消费</el-tag>
            <el-tag class="curpoi" :type="queryRecordFrom.isPriceIncome==2?'success':'info'"  @click="selPriInc(2)">招待费</el-tag>
             <el-tag class="curpoi" :type="queryRecordFrom.isPriceIncome==3?'success':'info'"  @click="selPriInc(3)">业务费用</el-tag>
             </div>
          </div> -->
          <div class="flexbet fcenter">
            <el-button type="success" plain @click="queryPageRecord">查询</el-button>
            <!-- <el-button type="primary" @click="addRecord(undefined,undefined,true)">上报账单</el-button> -->
          </div>


        </div>
        <div class="mt10">
          <table border="1" class="tablecls tabbord">
            <tr>
              <th>船期</th>
              <th>付款日期</th>
              <th>用途说明</th>
              <th v-show="queryRecordFrom.isIncomeStatus==null || queryRecordFrom.isIncomeStatus == 2 ">收入</th>
              <th v-show="queryRecordFrom.isIncomeStatus==null || queryRecordFrom.isIncomeStatus == 1 ">支出</th>

              <!-- <th>金额</th> -->
              <th>发票类型</th>
              <th>发票</th>
              <th>备注</th>
              <th>操作</th>
            </tr>

            <tr v-for="(item,index) in pageRecordList" :key="index">
              <td>{{shipLineFmt(item.departParam||item.param3) }}</td>
              <td>{{item.consumptionDate}}{{item.param2 && item.param2!=item.consumptionDate ? ' ~ '+item.param2 :''}}</td>
              <td>{{item.param1}}</td>
              <td v-show="queryRecordFrom.isIncomeStatus==null || queryRecordFrom.isIncomeStatus == 2 ">{{(item.state == 2 ? item.balance:'')|moneyFmt}}</td>
              <td v-show="queryRecordFrom.isIncomeStatus==null || queryRecordFrom.isIncomeStatus == 1 ">{{(item.state == 1 ? item.balance:'')|moneyFmt}}</td>
              <!-- <td>xxx</td> -->
              <td>{{ fmtNotesType(item.notesType) }}</td>
              <td>{{ fmtBill(item.isBill) }}</td>
              <td>{{ item.remarks }}</td>
              <td>
                  <el-popconfirm
                   v-show="item.status==0 || item.status==2"
                   title="确定要删除吗？"
                   icon-color="red"
                   @confirm="removePageRecordList(item.id)"
                  >
                  <el-button type="danger" plain slot="reference">删除</el-button>
                </el-popconfirm>
              </td>
            </tr>
          </table>
          <div style="text-align:center;padding:10px;width:100%;" v-show="!pageRecordList || pageRecordList.length==0">
          <el-empty description="暂无数据"></el-empty>
          </div>
          <el-pagination
            v-show="pageRecordList && pageRecordList.length>0"
            layout="prev, pager, next"
            :total="pageRecordTotal"
            background
            :page-size="pageRecordSize"
            :current-page="pageRecordNum"
            style="text-align: right;padding: 10px 0px;background-color: #fff"
            @current-change="eventPage"
            >
          </el-pagination>
        </div>
      </div>
    </div>
    <div v-show="selCompanyIndex!=-1"  style="flex-grow:1;" class="mtrb30">
    <el-button icon="el-icon-d-arrow-left" @click="drawerRight=true">审批列表</el-button>


    </div>
    <el-dialog
      title="申请备用金"
      :before-close="handlePriceClose"
      :visible.sync="drawerPrice"
      top="30px"
      width="500">
      <section>
        办事处：{{selComDetail.companyName}}
        <table border="1" class="tablecls" style="margin-top:5px">
          <tr>
            <td rowspan="3">上期备用金申请信息</td>
            <td>请款时间</td>
            <td>{{dateFmt(lastApplyDetail.applyCreateTime)}}</td>
          </tr>
          <tr>
            <td>请款金额</td>
            <td>{{lastApplyDetail.applyBalance}}</td>
          </tr>
          <tr>
            <td>请款后账上余额</td>
            <td>{{ lastApplyDetail.applyAmount }}</td>
          </tr>
          <!-- <tr>
            <td rowspan="3">支出</td>
            <td>日常花销</td>
            <td class="colclk" @click="addRecord(1)">{{applyDetail.expendDaily}}</td>
          </tr> -->
          <!-- <tr>
            <td>招待费</td>
            <td class="colclk" @click="addRecord(2)">{{applyDetail.expendHosp}}</td>
          </tr> -->
          <tr>
            <td>支出</td>
            <td>业务费用</td>
            <td class="colclk" @click="addRecord(3,'expend',false,true)">{{ applyDetail.expendBusAndStartEnd }}</td>
          </tr>
          <tr>
            <td>收入</td>
            <td>业务费用收入</td>
            <td class="colclk" @click="addRecord(undefined,'income',false,true)">
              {{ applyDetail.incomeBusAndStartEnd }}
            </td>
          </tr>
          <tr>
            <td colspan="2">支出合计</td>
            <td>{{ applyDetail.totalExpenditureAndStartEnd }}</td>
          </tr>
          <tr>
            <td colspan="2">现账上余额</td>
            <td>{{ applyDetail.accountAmount || selComDetail.balance }}</td>
          </tr>
          <tr>
            <td colspan="3">&nbsp;</td>
          </tr>
          <tr>
            <td rowspan="6">申请信息</td>
            <td>本次申请金额</td>
            <td>
              <el-input v-model="applyDetail.applyBalance"
                        oninput="value=value.replace(/[^\d\.]/g,'').replace('.','$#$').replace(/\./g,'').replace('$#$','.')"
                        placeholder="请输入申请金额"></el-input>
            </td>
          </tr>
          <tr>
            <td>申请后账上余额</td>
            <!-- || applyDetail.applyAmount -->
            <td>{{ tmpApplyAmount }}</td>
          </tr>
          <tr>
            <td>资金用途</td>
            <td>
              <el-input
                type="textarea"
                :rows="3"
                placeholder="请输入资金用途"
                v-model="applyDetail.useRemarks">
              </el-input>
            </td>
          </tr>
          <tr>
            <td>户名</td>
            <td>{{ selComDetail.param1|bankFmt(0) }}</td>
          </tr>
          <tr>
            <td>开户行</td>
            <td>{{ selComDetail.param1|bankFmt(1) }}</td>
          </tr>
          <tr>
            <td>账号</td>
            <td>{{ selComDetail.param1|bankFmt(2) }}</td>
          </tr>
          <tr>
            <td>申请人</td>
            <td>{{ applyDetail.applyPersonName || user.nickName }}</td>
            <td>{{ applyDetail.applyCreateTime }}</td>
          </tr>
        </table>
        <!-- <div style="margin-top:10px;">
             <el-steps :active="subType"  simple  finish-status="success">
              <el-step title="上报账单"></el-step>
              <el-step title="申请备用金"></el-step>
            </el-steps>
        </div> -->
        <div style="text-align:right;margin-top:10px;">
          <el-button type="primary" @click="subPrice">
            提交{{ applyDetail.totalExpenditure && subType == 0 ? '账单' : subType == 1 ? '备用金' : applyDetail.totalExpenditure ? '' : '备用金' }}
          </el-button>
        </div>

      </section>
    </el-dialog>
    <el-dialog
      :title="recordTitle"
      :before-close="recordClose"
      :visible.sync="recordDialog"
      top="30px"
      width="80%">
      <section class="recordDiaCls">
        <div>
          <el-tag class="curpoi" :type="!isIncomeStatus?'success':'info'" @click="setIncStatus(false)">支出</el-tag>
          <el-tag class="curpoi" :type="isIncomeStatus?'success':'info'" @click="setIncStatus(true)">收入</el-tag>
        </div>
        <!-- <div v-show="!isIncomeStatus" class="content">
           <div  class="mt10">
            类型 <el-tag class="curpoi" :type="isPriceIncome==1?'success':'info'"  @click="selPinc(1)">日常消费</el-tag>
              <el-tag class="curpoi" :type="isPriceIncome==2?'success':'info'"  @click="selPinc(2)">招待费</el-tag>
           <el-tag class="curpoi" :type="isPriceIncome==3?'success':'info'"  @click="selPinc(3)">业务费用</el-tag>
          </div>
          <div >
          <el-button v-show="isPriceIncome==3 && selComDetail.param1==1" @click="loadShipCostList(false)">导入船上费用</el-button>
          </div>
        </div> -->


        <div class="mt10 mb10 " style="text-align:center;">
          办事处:{{ selComDetail.companyName }}
        </div>

        <table border="1" class="tablecls">
          <tr>
            <th>序号</th>
            <td>船期</td>
            <th>付款日期</th>
            <th class="minwd30">用途说明</th>
            <th class="minwd30" v-show="isIncomeStatus">收入金额</th>
            <th class="minwd30" v-show="isPriceIncome==3 && !isIncomeStatus">支出金额</th>
            <th class="minwd30" v-show="isPriceIncome!=3 && !isIncomeStatus">金额</th>
            <th class="minwd20">发票类型</th>
            <th class="minwd20">发票(✓)</th>
            <th class="minwd30">备注</th>
            <th>操作</th>
          </tr>
          <tr v-for="(item,index) in recordList" :key="index">
            <td>{{ index + 1 }}</td>
            <td>{{ shipLineFmt(item.departParam || item.param3) }}</td>
            <td>
              {{ item.consumptionDate }}{{ item.param2 && item.param2 != item.consumptionDate ? ' ~ ' + item.param2 : '' }}
            </td>
            <td>{{ item.param1 }}</td>
            <td v-show="isIncomeStatus">{{ item.balance }}</td>
            <td v-show="isPriceIncome==3 && !isIncomeStatus">{{ item.balance }}</td>
            <td v-show="isPriceIncome!=3 && !isIncomeStatus">{{ item.balance }}</td>
            <td>{{ fmtNotesType(item.notesType) }}</td>
            <td>
              {{ fmtBill(item.isBill) }}
            </td>
            <td>{{ item.remarks }}</td>
            <td>

              <el-popconfirm
                v-show="item.status==0 || item.status==2"
                title="确定要删除吗？"
                icon-color="red"
                @confirm="removeNowRecordList(item.id)"
              >
                <el-button type="danger" plain slot="reference">删除</el-button>
              </el-popconfirm>
            </td>
          </tr>
          <tr v-for="(item,index) in tmpRecordList" :key="recordList.length+index">
            <td>{{ recordList.length + index + 1 }}</td>
            <td>
              <el-button v-show="payerSelList.length>0" round @click="openShipLine(item,index)">
                {{ item.shipLineTip || '选择船期' }}
              </el-button>
              <span v-show="payerSelList.length==0">--</span>
            </td>
            <td>
              <!-- <el-input v-model="item.consumptionDate" placeholder="请输入日期"></el-input> -->
              <!-- type="daterange" -->
              <!-- range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期" -->
              <el-date-picker
                v-model="item.consumptionDateRange"
                type="date"
                value-format="yyyy-MM-dd"
                placeholder="选择付款时间"
              >
              </el-date-picker>
            </td>
            <td>
              <el-input v-model="item.param1" placeholder="请输入用途"></el-input>
            </td>
            <td v-show="isIncomeStatus">
              <el-input v-model="item.balance"
                        oninput="value=value.replace(/[^\d\.]/g,'').replace('.','$#$').replace(/\./g,'').replace('$#$','.')"
                        placeholder="请输入金额"></el-input>
            </td>
            <td v-show="isPriceIncome==3 && !isIncomeStatus">
              <el-input v-model="item.balance"
                        oninput="value=value.replace(/[^\d\.]/g,'').replace('.','$#$').replace(/\./g,'').replace('$#$','.')"
                        placeholder="请输入金额"></el-input>
            </td>
            <td v-show="isPriceIncome!=3 && !isIncomeStatus">
              <el-input v-model="item.balance"
                        oninput="value=value.replace(/[^\d\.]/g,'').replace('.','$#$').replace(/\./g,'').replace('$#$','.')"
                        placeholder="请输入金额"></el-input>
            </td>
            <td>
              <el-select v-model="item.notesType" placeholder="请选择发票类型">
                <el-option
                  v-for="sitem in notesTypeList"
                  :key="sitem.value"
                  :label="sitem.label"
                  :value="sitem.value">
                </el-option>
              </el-select>
            </td>
            <td>

              <el-select v-model="item.isBill" placeholder="请选择发票">
                <el-option
                  v-for="sitem in billList"
                  :key="sitem.value"
                  :label="sitem.label"
                  :value="sitem.value">
                </el-option>
              </el-select>
            </td>
            <td>
              <el-input v-model="item.remarks" placeholder="请输入备注"></el-input>
            </td>
            <td>
              <el-popconfirm
                title="确定要删除吗？"
                icon-color="red"
                @confirm="removeRecordList(item.myidx)"
              >
                <el-button type="danger" plain slot="reference">删除</el-button>
              </el-popconfirm>
            </td>
          </tr>
        </table>
        <div style="text-align:center;margin-top:10px;">
          <el-button type="success" @click="addRecordTem">新增</el-button>
        </div>
        <div style="text-align:right">
          <!-- <span>日常消费：{{sumCtype1|moneyFmt}}</span>
           <el-divider direction="vertical"></el-divider>
          <span>招&nbsp;待&nbsp;费：{{sumCtype2|moneyFmt}}</span>
           <el-divider direction="vertical"></el-divider>
          <span>业务费用：{{sumCtype3|moneyFmt}}</span>
          <el-divider direction="vertical"></el-divider> -->
          <span style="margin-top:5px;">支出：{{ sumOut|moneyFmt }}</span>
          <div style="margin-top:5px;">收入：{{ sumIn|moneyFmt }}</div>
          <el-divider content-position="right">合计：{{ sumOutAndIn|moneyFmt }}</el-divider>
        </div>
        <div style="text-align:right;margin-top:10px;">
          <el-button type="primary" @click="saveRecord" :loading="recordLoading">保存{{ isProcessRecord ? '并上报' : '' }}
          </el-button>
        </div>
      </section>
    </el-dialog>
    <el-dialog
      :before-close="costClose"
      :visible.sync="costDialog"
      top="30px"
      width="60%">
      <section style="margin:0 auto;display: flex; justify-content:center;max-height:70vh;soverflow: scroll;">
        <section>
          <el-form ref="queryForm" :model="queryParams" :inline="true">
            <el-form-item prop="shipTime" label="船期">
              <el-date-picker
                v-model="queryParams.shipTime"
                @change="handleQueryShipTime"
                size="small"
                type="daterange"
                range-separator="至"
                value-format="yyyy-MM-dd"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
              />
            </el-form-item>
            <el-form-item prop="shipName" label="船名">
              <el-input v-model="queryParams.shipName" clearable></el-input>
            </el-form-item>
            <el-button @click="queryShipCost">查询</el-button>
          </el-form>
          <el-table
            ref="multipleShipCostTable"
            :data="showShipCostList"
            tooltip-effect="dark"
            height="60vh"
            style="width: 100%;"
          >
            <el-table-column
              type="selection"
              width="55">
            </el-table-column>
            <el-table-column
              label="船期"
              width="120">
              <template slot-scope="scope">
                {{ dateFmt(scope.row.shipTime) }}
              </template>

            </el-table-column>
            <el-table-column
              prop="shipName"
              label="船名"
              width="120">

            </el-table-column>
            <el-table-column
              prop="costProjectName"
              label="用途"
              width="120">

            </el-table-column>
            <el-table-column
              prop="costPriceNo"
              label="金额"
              width="120">
            </el-table-column>
            <el-table-column
              label="创建时间"
              width="120">
              <template slot-scope="scope">
                {{ dateFmt(scope.row.createDate) }}
              </template>
            </el-table-column>
          </el-table>
          <div style="margin-top:5px;text-align:right;">
            <el-button type="primary" @click="subCostFrm">确认</el-button>
          </div>

        </section>
      </section>

    </el-dialog>
    <el-drawer
      :title="selComDetail.companyName"
      :visible.sync="drawerRight"
      direction="rtl"
      size="50%"
      :before-close="handleRightClose">
      <section style="padding:10px;">
        <div style="text-align:center;margin-bottom:5px;">审批列表
          <el-button icon="el-icon-refresh" @click="loadProcessList" size="mini" circle></el-button>
        </div>
        <table border="1" class="tablecls">
          <tr>
            <th>申请日期</th>
            <th>类型</th>
            <th>金额</th>
            <th>状态</th>
          </tr>
          <tr v-if="!pageProcessList || pageProcessList.length==0">
            <td colspan="4">
              <el-empty description="暂无信息"></el-empty>
            </td>
          </tr>
          <tr v-for="(item,index) in pageProcessList" :key="index">
            <td>{{ item.sponsorTime }}</td>
            <td class="colclk" @click="showshenpi(item.id,item.flowName)">{{ item.flowName }}</td>
            <td>{{ item.globalParam3|moneyFmt }}</td>
            <td>
              <div
                v-if="item.status=== '1' "
                class="fontclass">审批中
              </div>
              <div
                v-if="item.status=== '2' "
                class="fontclass">已完成
              </div>
              <div
                v-if="item.status==='3' "
                class="fontclass">已驳回
              </div>
              <div
                v-if="item.status==='4' "
                class="fontclass">已撤回
              </div>
            </td>
          </tr>
        </table>
        <el-pagination
          v-show="pageProcessList && pageProcessList.length>0"
          layout="prev, pager, next"
          :total="pageProcessTotal"
          background
          :page-size="pageProcessSize"
          :current-page="pageProcessNum"
          style="text-align: right;padding: 10px 0px;background-color: #fff"
          @current-change="eventPageProcess"
        >
        </el-pagination>
      </section>
    </el-drawer>
    <el-drawer
      title="船期列表"
      :visible.sync="drawerShipLine"
      direction="ltr"
      size="600px"
      custom-class="shipLineDrawerCls"
      :before-close="handleCloseShipLine">
      <section style="margin:10px;padding:0px;">
        <el-form :inline="true" :model="shipLineParams" class="demo-form-inline">
          <el-form-item label="船期">
            <el-date-picker
              v-model="shipLineParams.shipChuanQiTime"
              value-format="yyyy-MM-dd"
              type="date"
              style="width:138px;"
              @change="onShipLineSubmit"
              clearable
              placeholder="选择船期">
            </el-date-picker>
          </el-form-item>
          <el-form-item label="船名">
            <el-input v-model="shipLineParams.shipName" clearable placeholder="船名"></el-input>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="onShipLineSubmit">查询</el-button>
          </el-form-item>
        </el-form>
        <!-- 多选 -->
        <div v-show="isIncomeStatus">
          <el-table
            ref="multipleShipLineTable"
            @row-click="(row, column, event)=>selCheckTabRowShipLine(row, column, event)"
            :data="shipLineList"
            style="width: 100%">
            <el-table-column
              type="selection"
              width="55">
            </el-table-column>
            <el-table-column
              prop="shipName"
              :formatter="shipNameFmt"
              label="船名"
              width="120">
            </el-table-column>
            <el-table-column

              label="船期"
              width="120">
              <template slot-scope="scope">
                <div>{{ dateFmt(scope.row.shipTime) }}</div>
              </template>
            </el-table-column>
            <el-table-column
              prop="voyageNumber"
              label="航次号"
              width="120">
            </el-table-column>
          </el-table>
        </div>
        <div v-show="!isIncomeStatus">
          <el-table
            :data="shipLineList"
            @row-click="(row, column, event)=>selTabRowShipLine(row, column, event)"
            style="width: 100%">
            <el-table-column
              label="选择"
              width="55">
              <template slot-scope="scope">
                <el-radio v-model="shipChuanQiIdRadio"
                          :label="scope.row.id+'|'+(dateFmt(scope.row.shipTime)||'--') + '/' + (scope.row.shipName||'--')">
                  &nbsp;
                </el-radio>
              </template>
            </el-table-column>
            <el-table-column
              prop="shipName"
              label="船名"
              width="120">
            </el-table-column>
            <el-table-column

              label="船期"
              width="120">
              <template slot-scope="scope">
                <div>{{ dateFmt(scope.row.shipTime) }}</div>
              </template>
            </el-table-column>
            <el-table-column
              prop="voyageNumber"
              label="航次号"
              width="120">
            </el-table-column>
          </el-table>
        </div>
        <el-pagination
          v-show="shipLineList && shipLineList.length>0"
          layout="prev, pager, next"
          :total="shipLineListTotal"
          background
          :page-size="shipLineParams.pageSize"
          :current-page="shipLineParams.pageNum"
          style="text-align: right;padding: 10px 0px;background-color: #fff"
          @current-change="eventPageShipLine"
        >
        </el-pagination>
        <div v-show="!isIncomeStatus" style="margin-bottom:5px;">
          <el-tag @click="setShipGoods(0)" :type="shipLineShipGoodsType == 0 ? 'success':'info'">船上费用</el-tag>&nbsp;&nbsp;<el-tag
          @click="setShipGoods(1)" :type="shipLineShipGoodsType == 1 ? 'success':'info'">货物费用
        </el-tag>
        </div>
        <div v-show="!isIncomeStatus" style="border:1px solid #d0f8ce;padding:10px;">

          <el-form :inline="true" label-width="88px" label-position="right" :model="shipLineInfo"
                   class="demo-form-inline">
            <div v-show="shipLineShipGoodsType==1">
              <div v-if="shipChuanQiIdRadio">
                <el-empty v-show="!shipLineGoodsList || shipLineGoodsList.length==0" description="暂无货物信息"></el-empty>
                <div v-show="shipLineGoodsList && shipLineGoodsList.length">
                  货物流向<span style="color:red">*</span>
                  <el-select v-model="shipLineGoodsInfo.customerGoodsCostId" @change="shipLineGoodsChange"
                             placeholder="请选择货物流向">
                    <el-option v-for="(sitem,sidx) in shipLineGoodsList"
                               :value="sitem.id"
                               :label="(sitem.cname||'--')+'/'+(sitem.tonnage||'--')"
                               :key="sidx"></el-option>
                  </el-select>
                  <el-divider></el-divider>
                  费用类型<span style="color:red">*</span>
                  <el-select v-model="shipLineGoodsInfo.costName" @change="setcostName" clearable placeholder="请选择费用类型">
                    <el-option
                      v-for="item in costNameList "
                      :key="item.value"
                      :label="item.value"
                      :value="item.code"
                    />
                  </el-select>
                  <el-divider></el-divider>
                  供应商名称<span style="color:red">*</span>
                  <el-select v-model="shipLineGoodsInfo.companyName" clearable placeholder="请选择供应商名称">
                    <el-option
                      v-for="item in supplierCostTypeList "
                      :key="item.id"
                      :label="item.simpleName"
                      :value="item.id"
                    />
                  </el-select>
                  <el-divider></el-divider>
                  <div v-show="payerSelList.length>1">
                    付款方<span style="color:red">*</span>
                    <el-radio-group v-model="shipLineGoodsInfo.payer" size="mini">
                      <el-tag style="margin-right: 10px;cursor: pointer;"
                              :type="shipLineGoodsInfo.payer==item.code?'success':'info'"
                              v-for="(item,index) in  payerSelList" :key="index" @click="setGoodsPayer(item.code)"
                      >{{ item.value }}
                      </el-tag>
                    </el-radio-group>
                  </div>
                  <el-divider></el-divider>
                  价格<span style="color:red">*</span>
                  <el-input
                    placeholder="请输入价格"
                    v-model="shipLineGoodsInfo.priceNo"
                    style="width:30%;"
                    @input="iptPriceChange"
                    clearable>
                  </el-input>
                  总价<span style="color:red">*</span>
                  <el-input
                    placeholder="请输入总价"
                    v-model="shipLineGoodsInfo.totalPriceNo"
                    style="width:30%;"
                    clearable>
                  </el-input>
                </div>
              </div>
              <div v-else>
                请选择船期
              </div>
            </div>
            <div v-show="shipLineShipGoodsType==0">
              <el-empty v-show="shipCostStatus!=1" :description="shipCostStatus==''?'':'船上费用已确认'"></el-empty>
              <div v-show="shipCostStatus==1">
                <el-form-item label="收款公司" :show-message="false">
                  <PanelSelection @setVal="setSuppliershipCost" :all-data="supplierList"
                                  modeName="收款公司" dataKey="common_supplier" keyStr="id" valStr="simpleName"
                                  ref="supplier"
                  ></PanelSelection>
                </el-form-item>
                <el-form-item label="对应吨数" prop="tonnesNum">
                  <el-input placeholder="请输入吨数" v-model="shipLineInfo.tonnesNum"></el-input>
                </el-form-item>
                <el-form-item label="单价" prop="danjia" :show-message="false">
                  <el-input placeholder="请输入单价" v-model="shipLineInfo.danjia" @input="taxConversion()"></el-input>
                </el-form-item>
                <el-form-item label="总额" prop="costPriceNo" required :show-message="false">
                  <el-input placeholder="请输入价格" v-model="shipLineInfo.costPriceNo"></el-input>
                </el-form-item>
                <!-- <el-form-item  prop="" label="收票公司"  style="display: inline-block;text-align: left;" >
                  <el-radio-group v-model="shipLineInfo.incomeCompany" size="mini">
                    <el-tag style="margin-right: 10px;cursor: pointer;" :type="shipLineInfo.incomeCompany==item.code?'success':'info'"
                            v-for="(item,index) in  contractCompany" v-if ="item.code === 10 || item.code === 20 ":key="index" @click="incomecompany(item.code)"
                    >{{item.value}}</el-tag>
                  </el-radio-group>
                </el-form-item> -->
                <el-form-item label="付款方" prop="payer" v-show="payerSelList.length>1" required :show-message="false">
                  <el-radio-group v-model="shipLineInfo.payer" size="mini">
                    <el-tag style="margin-right: 10px;cursor: pointer;"
                            :type="shipLineInfo.payer==item.code?'success':'info'"
                            v-for="(item,index) in  payerSelList" :key="index" @click="setPayer(item.code)"
                    >{{ item.value }}
                    </el-tag>
                  </el-radio-group>
                </el-form-item>
                <el-form-item label="发生项目" prop="costProject" style="text-align: left" required :show-message="false">
                  <el-radio-group v-model="shipLineInfo.costProject" size="mini">
                    <el-tag style="margin-right: 10px;margin-top:5px;cursor: pointer;"
                            :type="shipLineInfo.costProject==item.code?'success':'info'"
                            v-for="(item,index) in  itemsOccurredList" :key="index"
                            @click="leixingaddtaxCP(item.code,item.value)"
                    >{{ item.value }}
                    </el-tag>
                  </el-radio-group>
                </el-form-item>
              </div>
              <!-- <el-form-item  label="承担方" prop="costBear" style="text-align: left" >
                <el-radio-group v-model="shipLineInfo.costBear" size="medium">
                  <el-tag style="margin-right: 10px;cursor: pointer;" :type="shipLineInfo.costBear==item.code?'success':'info'"
                          v-for="(item,index) in  dictionaryLists['undertaker']" :key="index" @click="leixingaddtaxcb(item.code)"
                  >{{item.value}}</el-tag>
                </el-radio-group>
              </el-form-item> -->
              <!-- <el-form-item label="税率" prop="tax" style="text-align: left" >
                <el-radio-group v-model="shipLineInfo.tax" size="medium">
                  <el-tag style="margin-right: 10px;cursor: pointer;" :type="shipLineInfo.tax==item.code?'success':'info'"
                          v-for="(item,index) in  dictionaryLists['bill_tax']" :key="index" @click="leixingaddtax2(item.code)"
                  >{{item.value}}</el-tag>
                </el-radio-group>
              </el-form-item> -->
            </div>

          </el-form>
        </div>
        <div style="text-align:right;margin-top:5px;">
          <el-button type="success" @click="subShipLine">确认</el-button>
          <!-- <el-button type="success" @click="subShipLineGoods">确认货物</el-button> -->
        </div>

      </section>
    </el-drawer>
    <WorkFlowComponent ref="processDrawerIndex" componentsWidth="50%" @onCallback="onCallback"
                       @onBeforeSubmit="onBeforeSubmit"></WorkFlowComponent>
    <ProgressComponent ref="processDrawerqingjia" componentsWidth="50%" @refresh="refresh"
                       @onCallback="handlerGlobalParams"/>
  </section>
</template>

<script>
import PanelSelection from '@/components/PanelSelection/panelSelection'
import {
  fundList,
  fundRecordList,
  delRecord,
  queryRecordSum,
  queryRecordSumByStartAndEnd,
  fundDetail,
  fundRecordListPage,
  addRecordList,
  lastApply,
  addApply,
  addBill,
  updateShipLineShipCostIsDeductById,
  queryShipCostByCompanyId,
  fundMonthStartAndEndMoney,
  getMonthMoneyByFundIdAndMonth,
  changeRecordUnsubByFunId,
  changeListUpdateStatusByIds
} from '@/api/system/imprestFund'
import {mapGetters} from 'vuex'
import {imprestFundProcessList} from '@/api/business/processapi'
import {
  shipLinePageByStatus, getYewuCostomerCostNoComfirm, getSupplierDictionaryList,
  getShipCostNoComfirm, getGoodsCostNoComfirmByShipLineIds, getShipCostNoComfirmByShipLineIds
} from '@/api/system/baseInit'
import dayjs from 'dayjs'
import currency from 'currency.js'
import WorkFlowComponent from "@/components/workflow/index";
import ProgressComponent from '@/components/workflow/process'
import {getDictionaryList} from '@/api/system/baseInit'
import {add} from '@/api/mnt/deploy';

export default {
  components: {
    WorkFlowComponent, ProgressComponent, PanelSelection
  },
  data() {
    return {
      shipLineGoodsInfo: {
        tonnage: 0, // 吨位 计算总价
        invoiceType: 10, // 费率 默认不开票
        payer: '', // 付款方
        customerGoodsCostId: '', // 货物id
        companyName: '', // 供应商id
        costName: '', // 费用类型
        totalPriceNo: 0, // 总价
        priceNo: 0, // 单价
      },
      shipLineShipGoodsType: 0, // 0 船上费用 1 货物费用
      drawerShipLine: false,
      queryParams: {
        shipTime: null,
        shipName: ''
      },
      contractCompany: [{
        value: '海南成功',
        code: 10
      }, {
        value: '海南和盛',
        code: 20
      }],
      billList: [
        {label: '无票', value: 0},
        {label: '✓', value: 1},
        {label: '收据', value: 2},
        {label: '1%', value: 20},
        {label: '3%', value: 25},
        {label: '6%', value: 30},
        {label: '9%', value: 40},
        {label: '13%', value: 50},
      ],
      notesTypeList: [
        {label: '专票', value: 10},
        {label: '普票', value: 20},
        {label: '无票', value: 30}
      ],
      selCompanyIndex: -1,
      selComDetail: {},
      drawerRight: false,
      costDialog: false,
      recordLoading: false,
      drawerPrice: false,
      recordDialog: false,
      isPriceIncome: 3, // 1 日常 2  招待 3 业务费用
      isIncomeStatus: false, // true 收入 false 支出
      queryRecordFrom: {
        dates: null,
        isIncomeStatus: 1,
        isPriceIncome: null,

      },
      isProcessRecord: false,
      fundList: [],
      recordList: [],
      stagRecordList: [],
      lastApplyDetail: {},
      applyDetail: {
        expendDaily: 0, // 支出日常
        expendHosp: 0, // 支出招待费
        expendBus: 0, // 支出业务费用
        expendBusAndStartEnd: 0, // 支出 按时间
        incomeBus: 0, // 收入业务费用
        totalExpenditure: 0, // 支出合计
        incomeBusAndStartEnd: 0, // 收入业务费用
        totalExpenditureAndStartEnd: 0, // 支出合计
        accountAmount: 0, // 现账上余额
        applyBalance: 0, // 申请金额
        applyAmount: 0, // 申请后余额
        applyPersonName: '', // 申请人
        useRemarks: '', // 资金用途
        applyCreateTime: dayjs().format('YYYY-MM-DD'), // 申请时间
        lastApplyCreateTime: '', // 上次请款时间
        lastApplyBalance: 0, // 上次请款金额
        lastApplyAmount: 0, // 上次请款后余额
      },
      pageRecordList: [],
      pageRecordTotal: 0,
      pageRecordNum: 1,
      pageRecordSize: 20,
      subType: 0, // 1 账单 2 申请备用金
      pageProcessList: [],
      pageProcessTotal: 0,
      pageProcessNum: 1,
      pageProcessSize: 15,
      pageProcessStatus: null,
      shipLineListTotal: 0,
      shipCostList: [],
      showShipCostList: [],
      shipCostShipName: '',
      shipCostStartShipTime: '',
      shipCostEndShipTime: '',
      selShipCostList: [],
      isBillOpen: false,
      shipLineParams: {
        shipChuanQiTime: null, shipName: '', status: '1', pageNum: 1, pageSize: 10
      },
      shipLineList: [],
      shipChuanQiId: '',
      shipChuanQiIdRadio: '',
      supplierList: [],
      itemsOccurredList: [],
      dictionaryLists: {},
      shipLineInfo: {
        sysSupplierId: '',
        costPriceNo: '',
        tonnesNum: '',
        danjia: '',
        incomeCompany: '10',
        costProject: '',
        costBear: '',
        tax: '10',
        payer: '',
        notesType: '30'
      },
      payerList: [],
      costNameList: [],
      shipLineGoodsList: [],
      supplierCostTypeList: [],
      tmpLastMonthMoney: 0,
      tmpTotalExpenditureAndStartEnd: 0,
      shipCostStatus: ''
    }
  },
  computed: {
    payerSelList() {
      if (this.payerList.length > 0) {
        return this.payerList.filter(item => item.spare1 && item.spare1.split(',').indexOf(this.selComDetail.companyDepartId) >= 0)
      }
      return []
    },
    recordTitle() {
      if (this.isIncomeStatus) {
        return '业务费用明细'
      }
      if (this.isPriceIncome) {
        let msg = ''
        switch (this.isPriceIncome) {
          case 1:
            msg = '日常费用报销明细'
            break
          case 2:
            msg = '招待费报销明细'
            break
          case 3:
            msg = '业务费用明细'
            break
        }
        return msg
      }
      return ''
    },
    tmpRecordList() {
      if (this.stagRecordList && this.stagRecordList.length > 0) {
        return this.stagRecordList.map((ele, idx) => {
          ele['myidx'] = idx
          return ele
        }).filter(item => (this.isIncomeStatus && item.state == 2) || (!this.isIncomeStatus && item.state == 1 && item.consumptionType == this.isPriceIncome))
      }
      return []
    },
    tmpApplyAmount() {
      if (this.applyDetail) {
        // return currency(this.applyDetail.applyBalance).add(this.selComDetail.balance).value
        // return currency(this.applyDetail.applyBalance).add(this.tmpLastMonthMoney).subtract(this.tmpTotalExpenditureAndStartEnd).value
        // console.log('tmpApplyAmount',this.applyDetail.applyBalance,this.applyDetail.applyAmount,this.tmpLastMonthMoney,this.tmpTotalExpenditureAndStartEnd,currency(this.tmpLastMonthMoney).subtract(this.tmpTotalExpenditureAndStartEnd).value)
        let aa = this.applyDetail.accountAmount || this.selComDetail.balance
        // return currency(this.applyDetail.applyBalance).add(this.tmpLastMonthMoney).subtract(this.tmpTotalExpenditureAndStartEnd).value
        return currency(this.applyDetail.applyBalance).add(aa).value
      }
      return 0
    },
    sumCtype1() {
      let sum = this.applyDetail.expendDaily || 0
      if (this.stagRecordList && this.stagRecordList.length > 0) {
        this.stagRecordList.forEach(item => {
          if (item.state == 1 && item.consumptionType == 1) {
            const b = item.balance || 0
            sum = currency(sum).add(b).value
          }
        })
      }
      return sum
    },
    sumCtype2() {
      let sum = this.applyDetail.expendHosp || 0
      if (this.stagRecordList && this.stagRecordList.length > 0) {
        this.stagRecordList.forEach(item => {
          if (item.state == 1 && item.consumptionType == 2) {
            const b = item.balance || 0
            sum = currency(sum).add(b).value
          }
        })
      }
      return sum
    },
    sumCtype3() {
      let sum = this.applyDetail.expendBus || 0
      if (this.stagRecordList && this.stagRecordList.length > 0) {
        this.stagRecordList.forEach(item => {
          if (item.state == 1 && item.consumptionType == 3) {
            const b = item.balance || 0
            sum = currency(sum).add(b).value
          }
        })
      }
      return sum
    },
    sumOut() {
      let sum = this.applyDetail.totalExpenditure || 0
      if (this.stagRecordList && this.stagRecordList.length > 0) {
        this.stagRecordList.forEach(item => {
          if (item.state == 1) {
            const b = item.balance || 0
            sum = currency(sum).add(b).value
          }
        })
      }
      return sum
    },
    sumIn() {
      let sum = this.applyDetail.incomeBus || 0
      if (this.stagRecordList && this.stagRecordList.length > 0) {
        this.stagRecordList.forEach(item => {
          if (item.state == 2) {
            const b = item.balance || 0
            sum = currency(sum).add(b).value
          }
        })
      }
      return sum
    },
    sumOutAndIn() {
      let sum = this.applyDetail.totalExpenditure || 0
      const sumInc = this.applyDetail.incomeBus ? this.applyDetail.incomeBus * -1 : 0
      sum = currency(sum).add(sumInc).value
      if (this.stagRecordList && this.stagRecordList.length > 0) {
        this.stagRecordList.forEach(item => {
          const px = item.state == 2 ? -1 : 1
          const b = item.balance ? item.balance * px : 0
          sum = currency(sum).add(b).value
        })
      }
      return sum
    },
    ...mapGetters(['user'])
  },
  created() {
    this.loadData()
    var getSupplier = this.$store.dispatch('data/getSupplierListSaveInVuex')
    Promise.all([getSupplier]).then(res => {
      let v = res[0]
      if (v) {
        this.supplierList = v.supplierList
      }
    })
    this.loadSysDictsList()
  },
  filters: {
    moneyFmt(v) {
      if (v) {
        return currency(v, {symbol: '', precision: 2}).format()
      }
      return 0
    },
    bankFmt(v, idx) {
      if (v) {
        return v.split(',')[idx]
      }
      return '--'
    }
  },
  methods: {
    iptPriceChange() {
      this.shipLineGoodsInfo.totalPriceNo = parseFloat((Math.abs(Number(this.shipLineGoodsInfo.priceNo) * this.shipLineGoodsInfo.tonnage).toFixed(2)))
    },
    shipLineGoodsChange(obj) {
      console.log('cc', obj, this.shipLineGoodsList)
      const idx = this.shipLineGoodsList.findIndex(item => item.id == obj)
      console.log('ccidx', idx)
      if (idx >= 0) {
        this.shipLineGoodsInfo.tonnage = this.shipLineGoodsList[idx].tonnage
      } else {
        this.shipLineGoodsInfo.tonnage = 0
      }
      console.log('ccc', this.shipLineGoodsInfo.tonnage)
      // this.shipLineGoodsInfo.tonnage
    },
    loadSupplierList(code) {
      const data = {
        Key: 'cost_type', Code: code
      }
      getSupplierDictionaryList(data).then(res => {
        console.log('supper', res)
        if (res.resultCode === '0') {
          this.supplierCostTypeList = res.list
        }
      })
    },
    setcostName(obj) {
      console.log('cname', obj)
      this.loadSupplierList(this.shipLineGoodsInfo.costName)
      if (this.shipLineGoodsInfo.costName) {
        // 设置名称
        this.shipLineGoodsInfo.costNameStr = this.costNameList.findIndex(item => item.code == this.shipLineGoodsInfo.costName) >= 0 ? this.costNameList[this.costNameList.findIndex(item => item.code == this.shipLineGoodsInfo.costName)].value : ''
      }
      // this.getsu.Code = obj.code
      // this.selectsu()
    },
    loadGoodsListByShipLineId() {
      this.shipLineGoodsList = []
      if (this.shipLineShipGoodsType != 1) {
        return
      }
      if (!this.shipChuanQiIdRadio) {
        return
      }
      console.log('shiplineid', this.shipChuanQiIdRadio)
      getYewuCostomerCostNoComfirm({shipLineId: this.shipChuanQiIdRadio.split('|')[0]}).then(res => {
        console.log('goodslist', res)
        if (res.resultCode === '0') {
          this.shipLineGoodsList = res.data
        }
      })
    },
    loadShipCostStatusByShipLineId() {
      this.shipCostStatus = ''
      if (this.shipLineShipGoodsType == 1) {
        return
      }
      if (!this.shipChuanQiIdRadio) {
        return
      }
      getShipCostNoComfirm({shipLineId: this.shipChuanQiIdRadio.split('|')[0]}).then(res => {
        console.log('shipcoststatus', res)
        if (res.resultCode === '0') {
          this.shipCostStatus = res.data
        }
      })
    },
    setShipGoods(type) {
      this.shipLineShipGoodsType = type
      if (type == 1) {
        this.loadGoodsListByShipLineId()
      } else {
        // 查询船上费用是否确认
        this.loadShipCostStatusByShipLineId()
      }
    },
    initShipLineInfo() {
      this.shipLineInfo = {
        sysSupplierId: '',
        costPriceNo: '',
        tonnesNum: '',
        danjia: '',
        incomeCompany: '10',
        costProject: '',
        costBear: '',
        notesType: '30',
        tax: '10',
        payer: ''
      }
      this.shipLineGoodsInfo = {
        tonnage: 0, // 吨位 计算总价
        notesType: '30',
        invoiceType: 10, // 费率 默认不开票
        payer: '', // 付款方
        customerGoodsCostId: '', // 货物id
        companyName: '', // 供应商id
        costName: '', // 费用类型
        totalPriceNo: 0, // 总价
        priceNo: 0, // 单价
      }
      this.shipChuanQiId = ''
      this.shipChuanQiIdRadio = ''
      this.shipChuanQiTip = ''
      this.shipCostStatus = ''
      this.initShipLineParams()
      this.selShipLineMultiList = []
      this.$refs.multipleShipLineTable.clearSelection()
      this.$refs.supplier.clean()
    },
    initShipLineParams() {
      this.shipLineParams.shipName = ''
      this.shipLineParams.shipChuanQiTime = null
      this.shipLineParams.pageNum = 1
    },
    shipNameFmt(row, column, cellValue, index) {
      if (cellValue) {
        return cellValue
      }
      return '--'
    },
    toggleSelection(rows) {
      if (rows) {
        rows.forEach(row => {
          this.$refs.multipleShipLineTable.toggleRowSelection(row);
        });
      } else {
        this.$refs.multipleShipLineTable.clearSelection();
      }
    },
    setPayer(id) {
      if (this.shipLineInfo.payer == id) {
        this.shipLineInfo.payer = null
      } else {
        this.shipLineInfo.payer = id
      }
    },
    setNotesType(obj) {
      this.shipLineInfo.notesType = obj
    },
    setGoodsPayer(id) {
      if (this.shipLineGoodsInfo.payer == id) {
        this.shipLineGoodsInfo.payer = null
      } else {
        this.shipLineGoodsInfo.payer = id
      }
    },
    leixingaddtax2(id) {
      if (this.shipLineInfo.tax == id) {
        this.shipLineInfo.tax = null
      } else {
        this.shipLineInfo.tax = id
      }
    },
    leixingaddtaxcb(id) {
      if (this.shipLineInfo.costBear == id) {
        this.shipLineInfo.costBear = null
      } else {
        this.shipLineInfo.costBear = id
      }
    },
    leixingaddtaxCP(id, value) {
      if (this.shipLineInfo.costProject == id) {
        this.shipLineInfo.costProject = null
        this.shipLineInfo.costProjectName = ''
      } else {
        this.shipLineInfo.costProject = id
        this.shipLineInfo.costProjectName = value
      }
    },
    incomecompany(id) {
      if (this.shipLineInfo.incomeCompany == id) {
        this.shipLineInfo.incomeCompany = null
      } else {
        this.shipLineInfo.incomeCompany = id
      }
    },
    taxConversion() {
      if (!this.shipLineInfo.danjia || !this.shipLineInfo.tonnesNum) {
        this.shipLineInfo.costPriceNo = 0
        return
      }
      this.shipLineInfo.costPriceNo = (Number(this.shipLineInfo.danjia) * Number(this.shipLineInfo.tonnesNum)).toFixed(2)
    },
    setSuppliershipCost(obj) {
      this.shipLineInfo.sysSupplierId = obj.id
    },
    loadSysDictsList() {
      getDictionaryList("items_occurred,bill_tax,undertaker,Payer,cost_type").then(res => {
        if (res != undefined && res.resultCode === '0') {
          this.dictionaryLists = res.map
          this.payerList = res.map['Payer']
          this.costNameList = res.map['cost_type']
          this.itemsOccurredList = res.map['items_occurred'].sort(function (a, b) {
            return a.spare1 - b.spare1
          })
          console.log('sysdict', this.dictionaryLists, this.itemsOccurredList)
        }
      })
    },
    shipLineFmt(str) {
      if (!str) {
        return '--'
      }
      const arr = str.split(',')
      let s = ''
      for (let i = 0; i < arr.length; i++) {
        if (s) {
          s += ','
        }
        s += arr[i].split('|')[1]
      }
      return s
    },
    subShipLineGoods() {
      this.emShipLine()
      console.log('goods', this.shipLineGoodsInfo)
      this.handleCloseShipLine()
    },
    subShipLine() {
      console.log('状态', this.isIncomeStatus)
      if (this.isIncomeStatus) { // false 支出 true 收入
        if (this.$refs.multipleShipLineTable.selection.length == 0) {
          this.$message.error('请选择船期')
          return
        }


        //   设置
        this.shipChuanQiTip = ''
        this.shipChuanQiId = ''
        // const selShipLineMultiListConst=[]
        this.$refs.multipleShipLineTable.selection.forEach(item => {
          if (this.shipChuanQiId) {
            this.shipChuanQiId += ','
            this.shipChuanQiTip += ','
          }
          if (!this.shipLineTime && item.shipTime) {
            this.shipLineTime = this.dateFmt((item.shipTime || ''))
          }
          this.shipChuanQiId += item.id + '|' + (this.dateFmt(item.shipTime) || '--') + '/' + (item.shipName || '--')
          this.shipChuanQiTip += (this.dateFmt(item.shipTime) || '--') + '/' + (item.shipName || '--')
          // selShipLineMultiListConst.push(item)
        })
        // this.selShipLineMultiList = selShipLineMultiListConst
        // 多选
        // console.log('sel',this.$refs.multipleShipLineTable.selection)
        // return
      }
      console.log('cqid', this.shipChuanQiId)
      // 确认船期
      if (!this.shipChuanQiIdRadio && !this.shipChuanQiId) {
        this.$message.error('请选择船期')
        return
      }
      // 必填
      console.log('类型', this.shipLineShipGoodsType)
      // 根据类型判断
      if (this.shipLineShipGoodsType == 0) {
        // 验证船上费用
        // if (!this.shipLineInfo.sysSupplierId) {
        //   this.$message.error('请选择收款公司')
        //   return
        // }
        // if (!this.shipLineInfo.danjia) {
        //   this.$message.error('请输入单价')
        //   return
        // }
        if (!this.shipLineInfo.costPriceNo) {
          this.$message.error('请输入总额')
          return
        }
        if (this.payerSelList && this.payerSelList.length > 1 && !this.shipLineInfo.payer) {
          this.$message.error('请选择付款方')
          return
        }
        if (!this.shipLineInfo.costProject) {
          this.$message.error('请选择费用项目')
          return
        }

      } else {
        // 验证货物费用
        if (!this.shipLineGoodsInfo.customerGoodsCostId) {
          this.$message.error('请选择货物流向')
          return
        }
        if (!this.shipLineGoodsInfo.costName) {
          this.$message.error('请选择费用类型')
          return
        }
        if (!this.shipLineGoodsInfo.companyName) {
          this.$message.error('请选择供应商')
          return
        }
        if (this.payerSelList && this.payerSelList.length > 1 && !this.shipLineGoodsInfo.payer) {
          this.$message.error('请选择付款方')
          return
        }
        if (!this.shipLineGoodsInfo.priceNo) {
          this.$message.error('请输入单价')
          return
        }
        if (!this.shipLineGoodsInfo.totalPriceNo) {
          this.$message.error('请输入总价')
          return
        }
      }
      // 船期id传递
      this.emShipLine()
      this.handleCloseShipLine()
    },
    selCheckTabRowShipLine(row, column, event) {
      console.log('event', row, column, event)
      this.$refs.multipleShipLineTable.toggleRowSelection(row);
    },
    selTabRowShipLine(row, column, event) {
      this.initShipLineInfo()
      // if (this.isIncomeStatus) {
      //   if (this.shipChuanQiId) {
      //     this.shipChuanQiId.push(row.id)
      //     this.shipChuanQiTip.push(this.dateFmt((row.shipTime||'--')) + '/' + (row.shipName||'--'))
      //   } else {
      //     this.shipChuanQiId = [row.id]
      //     this.shipChuanQiTip = [this.dateFmt((row.shipTime||'--')) + '/' + (row.shipName||'--')]
      //   }
      //   return
      // }
      // 设置单选
      console.log('event', row, column, event)
      // this.$refs.shipLineTable.clearSelection()
      // this.$refs.shipLineTable.toggleRowSelection(row)
      this.shipChuanQiIdRadio = row.id + '|' + (this.dateFmt(row.shipTime) || '--') + '/' + (row.shipName || '--')
      this.shipChuanQiTip = (this.dateFmt(row.shipTime) || '--') + '/' + (row.shipName || '--')
      this.shipLineTime = this.dateFmt((row.shipTime || ''))
      this.loadGoodsListByShipLineId()
      this.loadShipCostStatusByShipLineId()

    },
    onShipLineSubmit() {
      this.shipLineParams.pageNum = 1
      this.loadShipLineList()
    },
    handleCloseShipLine() {
      // 关闭弹框
      this.drawerShipLine = false
    },
    emShipLine() {
      // 设置id
      if (!this.isIncomeStatus) {
        // 判断货物、船上费用
        if (this.shipLineShipGoodsType == 1) {
          // 货物
          if (!this.shipLineGoodsInfo.payer && this.payerSelList.length > 0) {
            this.shipLineGoodsInfo.payer = this.payerSelList[0].code
          }
          this.tmpRecordList[this.shipLineListIdx].shipLineInfo = Object.assign({fundType: 'goodsDetail'}, this.shipLineGoodsInfo)
          if (!this.tmpRecordList[this.shipLineListIdx].param1) {
            this.tmpRecordList[this.shipLineListIdx].param1 = this.shipLineGoodsInfo.costNameStr
          }
          this.tmpRecordList[this.shipLineListIdx].balance = this.shipLineGoodsInfo.totalPriceNo

        } else {
          if (!this.shipLineInfo.payer && this.payerSelList.length > 0) {
            this.shipLineInfo.payer = this.payerSelList[0].code
          }
          this.tmpRecordList[this.shipLineListIdx].shipLineInfo = Object.assign({fundType: 'shipCost'}, this.shipLineInfo)
          console.log('this.tmpRecordList=======>', this.tmpRecordList)
          if (!this.tmpRecordList[this.shipLineListIdx].param1) {
            this.tmpRecordList[this.shipLineListIdx].param1 = this.shipLineInfo.costProjectName
          }
          this.tmpRecordList[this.shipLineListIdx].balance = this.shipLineInfo.costPriceNo
        }

      }

      if (!this.tmpRecordList[this.shipLineListIdx].consumptionDateRange && this.shipLineTime) {
        // this.tmpRecordList[this.shipLineListIdx].consumptionDateRange = [this.shipLineTime,this.shipLineTime]
        this.tmpRecordList[this.shipLineListIdx].consumptionDateRange = this.shipLineTime
      }
      this.tmpRecordList[this.shipLineListIdx].shipLineId = this.shipChuanQiId || this.shipChuanQiIdRadio
      this.tmpRecordList[this.shipLineListIdx].shipLineTip = this.shipChuanQiTip
      this.initShipLineInfo()


    },
    openShipLine(row, idx) {
      this.drawerShipLine = true
      this.shipLineListIdx = idx
      if (row.shipLineId) {
        if (this.isIncomeStatus) {
          this.shipChuanQiId = row.shipLineId
          this.shipChuanQiIdRadio = ''
        } else {
          this.shipChuanQiIdRadio = row.shipLineId
          this.shipChuanQiId = ''
        }
        this.shipChuanQiTip = row.shipLineTip
      } else {
        this.shipChuanQiId = ''
        this.shipChuanQiTip = ''
      }
      this.shipLineTime = ''

      // 打开船期列表
      this.loadShipLineList()
    },
    loadShipLineList() {
      this.shipLineList = []
      this.shipLineParams.status = this.isIncomeStatus ? null : '1'
      shipLinePageByStatus(this.shipLineParams).then(res => {
        // console.log('shipline', res)
        if (res.resultCode == '0' && res.list) {
          this.shipLineList = res.list
          this.shipLineListTotal = res.total
          // if (this.selShipLineMultiList && this.selShipLineMultiList.length>0 && !this.isIncomeStatus && this.$refs.multipleShipLineTable) {
          //   this.toggleSelection(this.selShipLineMultiList)
          // }
        }
      })
    },
    handleRightClose() {
      this.drawerRight = false
    },
    getSumOutAndIn() {
      let sum = this.applyDetail.totalExpenditure || 0
      const sumInc = this.applyDetail.incomeBus ? this.applyDetail.incomeBus * -1 : 0
      sum = currency(sum).add(sumInc).value
      if (this.stagRecordList && this.stagRecordList.length > 0) {
        this.stagRecordList.forEach(item => {
          const px = item.state == 2 ? -1 : 1
          const b = item.balance ? item.balance * px : 0
          sum = currency(sum).add(b).value
        })
      }
      return sum
    },
    subCostFrm() {
      if (this.$refs.multipleShipCostTable.selection && this.$refs.multipleShipCostTable.selection.length) {
        if (this.selShipCostList && this.selShipCostList.length == 0) {
          this.selShipCostList = this.$refs.multipleShipCostTable.selection
        } else {
          this.selShipCostList = this.selShipCostList.concat(this.$refs.multipleShipCostTable.selection)
        }
        // 从show 删除

      }

      // console.log('sel', this.$refs.multipleShipCostTable.selection)
      // 选择
      // this.stagRecordList.push({
      //   balance: '', consumptionDate: '', isBill: '', param1: '', remarks: '',
      //   param2: '',
      //   consumptionDateRange: '',
      //   imprestFundId: this.selComDetail.id,
      //   consumptionType: this.isIncomeStatus ? 3 : this.isPriceIncome,
      //   state: this.isIncomeStatus ? 2 : 1
      // })
      const tmpShipCostList = this.showShipCostList.concat()
      console.log('shipscost', tmpShipCostList)
      for (let i = 0; i < this.$refs.multipleShipCostTable.selection.length; i++) {
        const item = this.$refs.multipleShipCostTable.selection[i]
        console.log('itemid', item.id)
        const idx = tmpShipCostList.findIndex(it => it.id == item.id)
        console.log('idx', idx)
        if (idx >= 0) {
          tmpShipCostList.splice(idx, 1)
        }
        const data = {
          selShipCostId: item.id,
          balance: item.costPriceNo,
          consumptionDate: this.dateFmt(item.shipTime),
          isBill: 0,
          param1: item.costProjectName,
          remarks: '',
          param2: this.dateFmt(item.shipTime),
          // consumptionDateRange: [this.dateFmt(item.shipTime),this.dateFmt(item.shipTime)],
          consumptionDateRange: this.dateFmt(item.shipTime),
          imprestFundId: this.selComDetail.id,
          consumptionType: this.isIncomeStatus ? 3 : this.isPriceIncome,
          state: this.isIncomeStatus ? 2 : 1
        }
        this.stagRecordList.push(data)
      }
      this.showShipCostList = tmpShipCostList
      this.costClose()
    },
    queryShipCost() {
      this.loadShipCostList(true)
    },
    handleQueryShipTime() {
      this.loadShipCostList(true)
    },
    costClose() {
      this.costDialog = false
      console.log('costclose')
    },
    loadShipCostList(boo = false) {
      this.costDialog = true
      if (!boo) {
        if (this.showShipCostList && this.showShipCostList.length) {
          return
        }
      }
      let startTime, endTime
      if (this.queryParams.shipTime) {
        startTime = this.queryParams.shipTime[0]
        endTime = this.queryParams.shipTime[1]
      }
      queryShipCostByCompanyId(this.selComDetail.companyDepartId, this.queryParams.shipName, startTime, endTime).then(res => {
        console.log('costlist', res)
        if (res.resultCode === '0' && res.list) {
          this.shipCostList = res.list
          this.showShipCostList = this.shipCostList.concat()
          this.upRemShipCost()
        }
      })
    },
    upRemShipCost() {
      for (let i = 0; i < this.selShipCostList.length; i++) {
        const item = this.selShipCostList[i]
        const idx = this.showShipCostList.findIndex(it => it.id == item.id)
        if (idx >= 0) {
          this.showShipCostList.splice(idx, 1)
        }
      }
    },
    refresh() {
    },
    handlerGlobalParams() {

    },
    showshenpi(id, name) {
      this.$refs.processDrawerqingjia.drawer = true
      this.$refs.processDrawerqingjia.processId = id // 参考 本组件 142 行代码
      this.$refs.processDrawerqingjia.title = name
      this.$refs.processDrawerqingjia.doInit()
    },
    loadProcessList() {
      const params = {
        pageSize: this.pageProcessSize,
        pageNum: this.pageProcessNum,
        status: this.pageProcessStatus,
        fundId: this.selComDetail.id
      }
      imprestFundProcessList(params).then(res => {
        console.log('processlist', res)
        if (res.resultCode === '0' && res.data) {
          this.pageProcessList = res.data
          this.pageProcessTotal = res.total
        }
      })
    },
    onCallback(processId) {
      console.log('callback', processId)
      if (this.subType == 0) {
        this.subType = 1
        // 保存 账单id  回写到流程
        const data = {
          processId,
          imprestFundId: this.selComDetail.id,
          amount: this.selComDetail.balance,
          imprestFundApplyId: undefined,
          balance: this.getSumOutAndIn(),
          companyId: this.selComDetail.companyId,
          companyName: this.selComDetail.companyName,
          expendDaily: this.sumCtype1,
          expendHosp: this.sumCtype2,
          expendBus: this.sumCtype3,
          incomeBus: this.sumIn,
          param1: this.sumOut,
          param2: this.startMonthMoney + '|' + this.endMonthMoney, //  期初余额|期末余额
          param3: this.tmpBillRecordList,
        }
        changeListUpdateStatusByIds(this.tmpChangeRecordList.map(item => item.id).join(','), '1')
        addBill(data).then(res => {
          console.log('res', res)
          this.stagRecordList = []
        })
        this.closeRecordDialog()
        if (this.isBillOpen) {
          this.subPrice()
        }
      } else {
        this.subType = 2
        const data = {
          processId,
          imprestFundId: this.selComDetail.id,
          companyId: this.selComDetail.companyId,
          companyName: this.selComDetail.companyName,
          applyBalance: this.applyDetail.applyBalance,
          applyAmount: this.tmpApplyAmount,
          applyCreateTime: this.applyDetail.applyCreateTime,
          accountAmount: this.selComDetail.balance,
          totalExpenditure: this.applyDetail.totalExpenditureAndStartEnd,
          lastApplyCreateTime: this.lastApplyDetail.applyCreateTime,
          lastApplyBalance: this.lastApplyDetail.applyBalance,
          lastApplyAmount: this.lastApplyDetail.applyAmount,
          useRemarks: this.applyDetail.useRemarks,
          expendDaily: this.applyDetail.expendDaily,
          expendHosp: this.applyDetail.expendHosp,
          expendBus: this.applyDetail.expendBusAndStartEnd,
          incomeBus: this.applyDetail.incomeBusAndStartEnd,
          param1: this.selComDetail.param1
        }
        addApply(data).then(res => {
          console.log('subapp', res)
          // this.drawerPrice = false
        })
        this.closeApplyDialog()
      }

    },
    dateFmt(v, fmt = 'YYYY-MM-DD') {
      if (!v) {
        return ''
      }
      return dayjs(v).format(fmt)
    },
    eventPageProcess(e) {
      this.pageProcessNum = e
      this.loadProcessList()
    },
    eventPageShipLine(e) {
      // this.setMultiSelShipLine()
      this.shipLineParams.pageNum = e
      this.loadShipLineList()
    },
    // setMultiSelShipLine() {
    //   const selShipLineMultiListConst=[]
    //   this.$refs.multipleShipLineTable.selection.forEach(item => {
    //     selShipLineMultiListConst.push(item)
    //   })
    //   this.selShipLineMultiList = selShipLineMultiListConst
    // },
    eventPage(e) {
      this.pageRecordNum = e
      this.loadRecordListPage()
    },
    selStatus(value) {
      if (this.queryRecordFrom.isIncomeStatus == value) {
        this.queryRecordFrom.isIncomeStatus = null
      } else {
        this.queryRecordFrom.isIncomeStatus = value
      }
      this.queryPageRecord()
    },
    selPriInc(value) {
      if (this.queryRecordFrom.isPriceIncome == value) {
        this.queryRecordFrom.isPriceIncome = null
      } else {
        this.queryRecordFrom.isPriceIncome = value
      }
      this.queryPageRecord()
    },
    queryPageRecord() {
      this.pageRecordNum = 1
      this.loadRecordListPage()
    },
    loadRecordListPage() {
      console.log('p', this.queryRecordFrom.dates)
      const data = {
        startMonth: this.queryRecordFrom.dates ? this.queryRecordFrom.dates[0] : '',
        endMonth: this.queryRecordFrom.dates ? this.queryRecordFrom.dates[1] : '',
        fundId: this.selComDetail.id,
        state: this.queryRecordFrom.isIncomeStatus,
        consumptionType: this.queryRecordFrom.isIncomeStatus == 2 ? 3 : this.queryRecordFrom.isPriceIncome,
        status: '0,2',
        pageNum: this.pageRecordNum,
        pageSize: this.pageRecordSize
      }
      this.pageRecordList = []
      fundRecordListPage(data).then(res => {
        console.log('res', res)
        if (res.resultCode === '0' && res.data) {
          this.pageRecordList = res.data
          this.pageRecordTotal = res.total
        }
      })
    },
    loadApplyDialog() {
      // 打开备用金申请
      // this.subType = 0
      this.subType = 1
      this.drawerPrice = true
      this.lastApplyDetail = {}
      // 申请信息重置
      this.applyDetail.applyBalance = ''
      this.applyDetail.useRemarks = ''
      // 显示支出 收入 总支出相关
      this.applyDetail.expendBusAndStartEnd = 0
      this.applyDetail.incomeBusAndStartEnd = 0
      this.applyDetail.totalExpenditureAndStartEnd = 0
      // 计算余额相关
      this.tmpLastMonthMoney = 0
      this.tmpTotalExpenditureAndStartEnd = 0
      this.tmpIncomeBusAndStartEnd = 0
      this.tmpExpendBusAndStartEnd = 0

      lastApply(this.selComDetail.id).then(res => {
        if (res.resultCode === '0' && res.data) {
          this.lastApplyDetail = res.data
        }
      }).finally(() => {
        this.loadLastApplySumRecord()
      })
      this.loadRecordAllSum()
    },
    loadLastApplySumRecord() {
      // 获取上一次时间
      // this.lastApplyDetail.applyCreateTime
      if (!this.lastApplyDetail.createTime) {
        this.lastApplyDetail.createTime = this.dateFmt(new Date(), 'YYYY-MM') + '-01 00:00:00'
      }
      // 当前时间
      // 支出、收入
      // 状态（0 创建， 1提交中，3审批成功，2 审批失败）
      this.loadRecordAllSumAndStartEnd(this.lastApplyDetail.createTime,
        outn => this.applyDetail.expendBusAndStartEnd = outn,
        inn => this.applyDetail.incomeBusAndStartEnd = inn,
        () => {
          this.applyDetail.totalExpenditureAndStartEnd = currency(this.applyDetail.expendBusAndStartEnd).subtract(this.applyDetail.incomeBusAndStartEnd).value
        })
      // 请款余额相关数据
      this.loadMoneyLastAndTime(this.lastApplyDetail.createTime)
    },
    loadMoneyLastAndTime(lastTime) {
      if (!lastTime) {
        // 月初
        lastTime = this.dateFmt(new Date(), 'YYYY-MM') + '-01 00:00:00'
      }
      const lastday = this.dateFmt(lastTime, 'YYYY-MM')
      // 期初数
      getMonthMoneyByFundIdAndMonth({fundId: this.selComDetail.id, yearMonth: lastday}).then(res => {
        if (res.resultCode === '0' && res.money) {
          this.tmpLastMonthMoney = res.money
        }
      })
      // 中间 支出、收入 合计
      this.loadRecordAllSumAndStartEnd(lastday + '-01 00:00:00',
        outn => this.tmpExpendBusAndStartEnd = outn,
        inn => this.tmpIncomeBusAndStartEnd = inn,
        () => {
          console.log('中间 支出、收入 合计', this.tmpExpendBusAndStartEnd, this.tmpIncomeBusAndStartEnd)
          this.tmpTotalExpenditureAndStartEnd = currency(this.tmpExpendBusAndStartEnd).subtract(this.tmpIncomeBusAndStartEnd).value
          console.log('中间 支出、收入 合计', this.tmpTotalExpenditureAndStartEnd)
        }, true)
      // 申请数
    },
    loadRecordAllSumAndStartEnd(lastCreateTime, outFun, inFun, sumFun, isApplyBalance = false) {
      console.log('lastCreateTime 1675', lastCreateTime)
      const daynow = this.dateFmt(new Date(), 'YYYY-MM-DD')
      if (!lastCreateTime) {
        // 月初
        lastCreateTime = this.dateFmt(new Date(), 'YYYY-MM') + '-01 00:00:00'
      }
      const lastday = this.dateFmt(lastCreateTime, 'YYYY-MM-DD HH:mm:ss')
      // 改造
      Promise.all([
        this.loadRecordSumByStartAndEnd(1, lastday, daynow, undefined),
        this.loadRecordSumByStartAndEnd(2, lastday, daynow, undefined, isApplyBalance)
      ]).then(res => {
        if (res[0].resultCode === '0') {
          // this.applyDetail.expendBusAndStartEnd = res.data || 0
          outFun && outFun(res[0].data || 0)
        }
        if (res[1].resultCode === '0') {
          // this.applyDetail.incomeBusAndStartEnd = res.data || 0
          inFun && inFun(res[1].data || 0)
        }
        console.log('loadRecordAllSumAndStartEnd 1692', lastday, res)
        sumFun && sumFun()
      })
    },
    loadRecordAllSum() {
      this.loadRecordSum(1, 1, undefined, '0,2', res => {
        if (res.resultCode === '0') {
          this.applyDetail.expendDaily = res.data || 0
        }
      })
      this.loadRecordSum(1, 2, undefined, '0,2', res => {
        if (res.resultCode === '0') {
          this.applyDetail.expendHosp = res.data || 0
        }
      })
      this.loadRecordSum(1, 3, undefined, '0,2', res => {
        if (res.resultCode === '0') {
          this.applyDetail.expendBus = res.data || 0
        }
      })
      this.loadRecordSum(1, undefined, undefined, '0,2', res => {
        if (res.resultCode === '0') {
          this.applyDetail.totalExpenditure = res.data || 0
        }
      })
      this.loadRecordSum(2, 3, undefined, '0,2', res => {
        if (res.resultCode === '0') {
          this.applyDetail.incomeBus = res.data || 0
        }
      })
    },
    loadRecordSum(state, consumptionType, monthRec, status, func) {
      // state  1支出、2收入
      // consumptionType, 1日常、2招待、3业务
      // monthRec,年-月
      // status 0 创建， 1提交中，3审批成功，2 审批失败
      const params = {
        fundId: this.selComDetail.id, state, consumptionType, monthRec, status
      }
      queryRecordSum(params).then(res => {
        func && func(res)
      })
    },
    loadRecordSumByStartAndEnd(state, start, end, status, isApplyBalance = false) {
      const params = {
        fundId: this.selComDetail.id, state, start, end, status, isApplyBalance
      }
      return queryRecordSumByStartAndEnd(params)

    },
    removePageRecordList(rid) {
      delRecord(rid).then(res => {
        if (res.resultCode == '0' && res.data) {
          this.$message('删除成功')
          this.pageRecordList = this.pageRecordList.filter(item => item.id != rid)
          this.updateFundDetail()
        } else {
          this.$message(res.msg || '删除失败，请稍后重试')
        }
      }).catch(() => {
        this.$message(res.msg || '删除失败，请稍后重试')
      })
    },
    removeNowRecordList(rid) {
      delRecord(rid).then(res => {
        if (res.resultCode == '0' && res.data) {
          this.$message('删除成功')
          this.recordList = this.recordList.filter(item => item.id != rid)
          this.loadRecordAllSum()
        } else {
          this.$message(res.msg || '删除失败，请稍后重试')
        }
      }).catch(() => {
        this.$message(res.msg || '删除失败，请稍后重试')
      })
    },
    setIncStatus(st) {
      this.isIncomeStatus = st
      this.initShipLineParams()
      this.loadRecordList()
    },
    selPinc(idx) {
      this.isPriceIncome = idx
      this.loadRecordList()
    },
    fmtNotesType(idx) {
      const b = this.notesTypeList.filter(item => item.value == idx)
      if (b && b.length > 0) {
        return b[0].label
      }
      return ''
    },
    fmtBill(idx) {
      const b = this.billList.filter(item => item.value == idx)
      if (b && b.length > 0) {
        return b[0].label
      }
      return ''
    },
    removeRecordList(index) {
      const item = this.stagRecordList[index]
      if (item.selShipCostId) {
        const sidx = this.selShipCostList.findIndex(it => it.id == item.selShipCostId)
        if (sidx >= 0) {
          const sitem = this.selShipCostList[sidx]
          this.showShipCostList.unshift(sitem)
          this.selShipCostList.splice(sidx, 1)
        }
      }
      this.stagRecordList.splice(index, 1)
    },
    addRecordTem() {
      let nowTime = this.dateFmt(new Date())
      this.stagRecordList.push({
        balance: '', consumptionDate: '', notesType: 30, isBill: 0, param1: '', remarks: '',
        param2: '',
        // consumptionDateRange: [nowTime,nowTime],
        consumptionDateRange: nowTime,
        imprestFundId: this.selComDetail.id,
        consumptionType: this.isIncomeStatus ? 3 : this.isPriceIncome,
        state: this.isIncomeStatus ? 2 : 1
      })
    },
    loadRecordList() {
      // 根据时间 状态 加载数据
      this.recordList = []
      fundRecordList({
        fundId: this.selComDetail.id,
        state: this.isIncomeStatus ? 2 : 1,
        consumptionType: this.isIncomeStatus ? 3 : this.isPriceIncome,
        status: this.isStartEnd ? undefined : '0,2',
        start: this.isStartEnd ? this.lastApplyDetail.createTime : undefined,
        end: this.isStartEnd ? this.dateFmt(new Date(), 'YYYY-MM-DD') : undefined
      }).then(res => {
        if (res.resultCode == '0' && res.data) {
          this.recordList = res.data
        }
      })
    },
    changeCompany(idx) {
      this.selCompanyIndex = idx
      this.selComDetail = this.fundList[this.selCompanyIndex]

      this.updateComDetail()

    },
    loadData() {
      fundList().then(res => {
        if (res.resultCode == '0' && res.list && res.list.length > 0) {
          this.fundList = res.list
          this.selCompanyIndex = 0
          this.selComDetail = this.fundList[this.selCompanyIndex]
          this.loadRecordListPage()
          this.loadProcessList()
          this.loadRecordAllSum()
        }
      })
    },
    handlePriceClose() {
      this.drawerPrice = false
    },
    recordClose() {
      this.updateComDetail()
      this.recordDialog = false
    },
    closeApplyDialog() {
      this.drawerPrice = false
    },
    subPrice() {
      // 验证
      if (!this.applyDetail.applyBalance) {
        this.$message.error('请输入申请金额')
        return
      }

      // 支出合计
      this.subType = 3
      // 取消账单
      // if (this.applyDetail.totalExpenditure) {
      //   // if (this.subType == 3) {
      //   //   this.subType = 0
      //   // }
      // } else {
      //   // if (this.subType == 0) {
      //   if (this.subType == 1) {
      //     this.subType = 3
      //   }
      // }
      // if (this.applyDetail.totalExpenditure==0) {
      //   this.subType = 3
      // }
      if (this.subType == 0) {
        this.showProcessBill()
        return
      }
      // this.subType = 2
      this.applyDetail.applyPersonName = this.user.nickName
      this.applyDetail.accountAmount = this.selComDetail.balance
      this.applyDetail.applyAmount = this.tmpApplyAmount
      // 上报
      this.$refs.processDrawerIndex.drawer = true
      this.$refs.processDrawerIndex.title = '备用金申请'
      this.$refs.processDrawerIndex.datas = {
        fundId: this.selComDetail.id,
        applyDetail: {
          ...this.applyDetail,
          companyName: this.selComDetail.companyName,
          lastApplyCreateTime: this.lastApplyDetail.applyCreateTime,
          lastApplyBalance: this.lastApplyDetail.applyBalance,
          lastApplyAmount: this.lastApplyDetail.applyAmount,
          param1: this.selComDetail.param1
        }
      },

        this.$refs.processDrawerIndex.globalParams = {
          params4: this.selComDetail.companyParentId,
          params1: this.selComDetail.id,
          params3: this.applyDetail.applyBalance,
          params7: 1
        }
      this.$refs.processDrawerIndex.processCode = 'reserveApply'
      this.$refs.processDrawerIndex.briefContent = '公司：' + this.selComDetail.companyName + '，备用金申请：' + this.applyDetail.applyBalance
      this.$refs.processDrawerIndex.doInit()
      // return
      // const data = {
      //   imprestFundId: this.selComDetail.id,
      //   companyId: this.selComDetail.companyId,
      //   companyName: this.selComDetail.companyName,
      //   applyBalance: this.applyDetail.applyBalance,
      //   applyAmount: this.tmpApplyAmount,
      //   applyCreateTime: this.applyDetail.applyCreateTime,
      //   accountAmount: this.selComDetail.balance,
      //   totalExpenditure: this.applyDetail.totalExpenditure,
      //   lastApplyCreateTime: this.lastApplyDetail.applyCreateTime,
      //   lastApplyBalance: this.lastApplyDetail.applyBalance,
      //   lastApplyAmount: this.lastApplyDetail.applyAmount,
      //   useRemarks: this.applyDetail.useRemarks,
      //   expendDaily: this.applyDetail.expendDaily,
      //   expendHosp: this.applyDetail.expendHosp,
      //   expendBus: this.applyDetail.expendBus,
      //   incomeBus: this.applyDetail.incomeBus
      // }
      // addApply(data).then(res => {
      //   console.log('subapp', res)
      //   // this.drawerPrice = false
      // })

    },
    onBeforeSubmit(cb) {
      let flag = true
      this.$refs.processDrawerIndex.formValue = {
        company: this.selComDetail.companyDepartId
      }
      cb(flag)
    },
    addRecord(model, type = 'expend', isProcess = false, isStartEnd = false) {
      this.isStartEnd = isStartEnd
      this.loadRecordAllSum()
      this.isProcessRecord = isProcess
      // 录入消费记录
      this.isIncomeStatus = false
      if (type == 'income') {
        // 类型
        this.isIncomeStatus = true
      }
      if (model) {
        switch (model) {
          case 1:
            this.isPriceIncome = 1
            break
          case 2:
            this.isPriceIncome = 2
            break
          case 3:
            this.isPriceIncome = 3
            break
        }
      }
      this.recordDialog = true
      this.loadRecordList()
    },
    showProcessBill(isBill = false) {
      if (isBill) {
        this.isBillOpen = false
      } else {
        this.isBillOpen = true
      }
      // 获取期初、期末余额
      const loading = this.$loading({
        lock: true,
        text: '加载中',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      })
      this.tmpBillRecordList = []
      this.tmpChangeRecordList = []
      Promise.all([fundMonthStartAndEndMoney(this.selComDetail.id), fundRecordList({
        fundId: this.selComDetail.id,
        status: '0,2'
      }), changeRecordUnsubByFunId(this.selComDetail.id)]).then((values) => {
        console.log(values)
        const res = values[0]
        const res2 = values[1]
        const res3 = values[2]
        if (res2.resultCode == '0' && res2.data) {
          this.tmpBillRecordList = res2.data
        }
        if (res3.resultCode == '0' && res3.data) {
          this.tmpChangeRecordList = res3.data
        }
        let start = 0;
        let end = 0;
        if (res.resultCode === '0') {
          start = res.start
          end = res.end
        }
        this.startMonthMoney = start
        this.endMonthMoney = end

        this.$refs.processDrawerIndex.drawer = true
        this.$refs.processDrawerIndex.title = '备用金账单'
        this.$refs.processDrawerIndex.datas = {
          fundId: this.selComDetail.id,
          start: start,
          end: end,
          recordList: this.tmpBillRecordList,
          changeRecordList: this.tmpChangeRecordList
        },
          this.$refs.processDrawerIndex.globalParams = {
            params4: this.selComDetail.companyParentId,
            params1: this.selComDetail.id,
            params3: this.getSumOutAndIn(),
            params6: this.tmpChangeRecordList.map(item => item.id).join(','),
          }
        this.$refs.processDrawerIndex.processCode = 'reserveBill'
        this.$refs.processDrawerIndex.briefContent = '公司：' + this.selComDetail.companyName + '，备用金账单：' + this.getSumOutAndIn()
        this.$refs.processDrawerIndex.doInit()

      }).finally(() => {
        loading.close()
      })

      // fundMonthStartAndEndMoney(this.selComDetail.id).then(res => {
      //   // 加载 日志记录
      //   // this.loadRecordListByBill()
      //   let start=0;
      //   let end=0;
      //   if (res.resultCode === '0') {
      //     start = res.start
      //     end = res.end
      //   }
      //   this.startMonthMoney=start
      //   this.endMonthMoney=end

      //   this.$refs.processDrawerIndex.drawer = true
      //   this.$refs.processDrawerIndex.title = '备用金账单'
      //   this.$refs.processDrawerIndex.datas = { fundId: this.selComDetail.id,start:start,end:end },
      //   this.$refs.processDrawerIndex.globalParams = {
      //     params4: this.selComDetail.companyParentId,
      //     params1: this.selComDetail.id,
      //     params3: this.getSumOutAndIn()
      //   }
      //   this.$refs.processDrawerIndex.processCode = 'reserveBill'
      //   this.$refs.processDrawerIndex.briefContent = '公司：'+this.selComDetail.companyName+ '，备用金账单：'+this.getSumOutAndIn()
      //   this.$refs.processDrawerIndex.doInit()

      // }).finally(()=>{
      //   loading.close()
      // })


    },
    upFindByRecordItem(item, index, shipCostArr, goodsCostArr) {
      if (!item.shipLineInfo) {
        // 判断 类型
        return
      }
      const info = item.shipLineInfo
      if (!info.fundType) {
        return
      }
      if (info.fundType == 'shipCost') {
        const data = {
          id: item.shipLineId.split('|')[0],
          idx: index
        }
        shipCostArr.push(data)
        return
      }
      if (info.fundType == 'goodsDetail') {
        const data = {
          id: info.customerGoodsCostId,
          idx: index
        }
        goodsCostArr.push(data)
        return
      }

    },
    async isCheckShipAndGoods(shipCostRecs, goodsCostRecs) {
      console.log('ischeckdata', shipCostRecs, goodsCostRecs)
      let isCheck = true
      // let msg=''
      const idxAllArr = []
      //  判断是否有需要验证数据
      if (shipCostRecs.length > 0 || goodsCostRecs.length > 0) {
        // 去重复
        const shipCostArr = shipCostRecs.filter((item, index, self) => {
          return self.findIndex(t => t.id === item.id) === index
        }) || []
        const goodsCostArr = goodsCostRecs.filter((item, index, self) => {
          return self.findIndex(t => t.id === item.id) === index
        }) || []

        if (shipCostRecs.length == 0) {
          // 验证货物费用
          const res = await getGoodsCostNoComfirmByShipLineIds({goodsCostIds: goodsCostRecs.map(item => item.id).join(',')})
          console.log('res-2119', res)
          if (res.resultCode == '0') {
            // 未确认id
            const data = (res.data && res.data.split(',')) || []
            if (data.length != goodsCostArr.length) {
              isCheck = false
              // msg='货物费用已经确认，不能增加费用 请联系单证人员'
              // 已确认id 下标
              const idxArr = goodsCostRecs.filter(item => data.indexOf(item.id) == -1).map(item => item.idx)
              idxAllArr.push(...idxArr)
            }
          } else {
            isCheck = false
            // msg='货物费用验证失败'
          }
        } else if (goodsCostRecs.length == 0) {
          // 验证船上费用
          const res = await getShipCostNoComfirmByShipLineIds({shipLineIds: shipCostRecs.map(item => item.id).join(',')})
          console.log('res-2137', res)
          if (res.resultCode == '0') {
            // 未确认id
            const data = (res.data && res.data.split(',')) || []
            console.log('2149---', shipCostArr, data)
            if (data.length != shipCostArr.length) {
              console.log('2151--')
              isCheck = false
              // msg='船上费用已经确认，不能增加费用 请联系单证人员'
              // 已确认id 下标
              const idxArr = shipCostRecs.filter(item => data.indexOf(item.id) == -1).map(item => item.idx)
              idxAllArr.push(...idxArr)
            }
          } else {
            isCheck = false
            // msg='船上费用验证失败'
          }
        } else {
          // 全部验证
          const isRes = await Promise.all([getShipCostNoComfirmByShipLineIds({shipLineIds: shipCostRecs.map(item => item.id).join(',')}), getGoodsCostNoComfirmByShipLineIds({goodsCostIds: goodsCostRecs.map(item => item.id).join(',')})])
          console.log('res-2155', isRes)
          if (isRes[0].resultCode == '0' && isRes[1].resultCode == '0') {
            // 未确认id
            const data1 = (isRes[0].data && isRes[0].data.split(',')) || []
            const data2 = (isRes[1].data && isRes[1].data.split(',')) || []
            console.log('2169---', shipCostArr)
            if (data1.length != shipCostArr.length || data2.length != goodsCostArr.length) {
              isCheck = false
              // msg='船上费用或者货物费用已经确认，不能增加费用 请联系单证人员'
              // 已确认id 下标
              const idxArr1 = shipCostRecs.filter(item => data1.indexOf(item.id) == -1).map(item => item.idx)
              const idxArr2 = goodsCostRecs.filter(item => data2.indexOf(item.id) == -1).map(item => item.idx)
              idxAllArr.push(...idxArr1, ...idxArr2)
            }
          }
        }

        console.log('idxAllArr', idxAllArr)
        console.log('isCheck', isCheck)
        // 错误提示
        if (!isCheck) {
          // param1 明细 balance 金额 consumption_date 时间  this.stagRecordList
          let msg = ''
          idxAllArr.forEach(item => {
            const data = this.stagRecordList[item]
            msg += '费用类型：' + data.param1 + '，金额：' + data.balance + '，时间：' + data.consumptionDateRange + '；'
          })
          this.$message.error('费用已经确认，不能增加费用 请联系单证人员' + msg)
          // this.$message.error('船上费用或者货物费用已经确认，不能增加费用 请联系单证人员 明细：')

        }
      }
      return isCheck
    },
    async saveRecord() {
      if (this.recordLoading) {
        return
      }
      let stagShipCostIds = []
      this.recordLoading = true
      // 根据船期id ，获取货物及船上费用
      // 支出 state 1
      // 船上费用 1 or 货物费用 2  1,2
      // 船期id fundType=‘shipCost’ 货物id fundType=‘goodsDetail‘ customerGoodsCostId

      // 记录类型 相应下标， 相关id
      // 船上费用 0  船期id
      // 货物费用 1  货物id
      const shipCostRecs = []
      const goodsCostRecs = []

      const data = this.stagRecordList.map((item, index) => {
        if (item.selShipCostId) { // 是否有船上费用id
          stagShipCostIds.push(item.selShipCostId)
        }
        this.upFindByRecordItem(item, index, shipCostRecs, goodsCostRecs)
        if (item.shipLineInfo) {
          item.shipLineInfo.notesType = item.notesType
        }
        return {
          ...item,
          // consumptionDate: item.consumptionDateRange[0],
          // param2: item.consumptionDateRange[1],
          consumptionDate: item.consumptionDateRange,
          param2: item.consumptionDateRange,
          // param3: item.selShipCostId
          param3: item.shipLineId
        }
      })
      // 验证 船上费用 货物费用是否确认
      const isCheck = await this.isCheckShipAndGoods(shipCostRecs, goodsCostRecs);
      if (!isCheck) {
        this.recordLoading = false
        return
      }
      if (data && data.length == 0) {

        if (this.isProcessRecord) {
          this.showProcessBill(true)
        }
        if (!this.isProcessRecord) {
          this.closeRecordDialog()
        }
        this.recordLoading = false
        return
      }
      // await getShipCostNoComfirmByShipLineIds()
      // await getGoodsCostNoComfirmByShipLineIds()
      addRecordList(data).then(res => {
        if (res.resultCode === '0' && res.data != '0') {
          // 删除状态
          this.removeShowShipCost(stagShipCostIds)
          // 保存后清空集合

          if (this.isProcessRecord) {
            this.showProcessBill(true)
          }
          if (!this.isProcessRecord) {
            this.closeRecordDialog()
          }
          this.stagRecordList = []
        } else {
          this.$message.error(res.msg)
        }
      }).finally(() => {
        this.recordLoading = false
      })

    },
    removeShowShipCost(ids) {
      if (ids && ids.length) {
        updateShipLineShipCostIsDeductById(ids.join(','))
      }
    },
    closeRecordDialog() {
      this.recordDialog = false
      this.updateComDetail()
    },
    updateComDetail() {
      // 更新账户余额
      this.updateFundDetail()
      // 更新 各支出合计
      this.loadRecordAllSum()
      // 更新第一页 列表
      this.queryPageRecord()
      // 更新流程列表
      this.loadProcessList()
      // this.loadRecordListPage()
      this.loadLastApplySumRecord()
    },
    updateFundDetail() {
      fundDetail(this.selComDetail.id).then(res => {
        if (res.resultCode === '0' && res.data) {
          this.selComDetail.balance = res.data.balance
        }
      })
    }
  }
}
</script>

<style scoped>
.content {
  margin: 10px;
  display: flex;
  justify-content: space-between;
}

.mg30 {
  margin: 30px 15px 30px 10px;
}

.flexd {
  display: flex;
}

.flexdire {
  flex-direction: column;
}

.flexbet {
  display: flex;
  justify-content: space-around;
}

.fcenter {
  align-items: center;
}

.justcenter {
  justify-content: center;
}

.justaround {
  justify-content: space-around;
}

.contcenter {
  align-content: center;
}

.bodd {
  border: 1px solid #e0e0e0;
}

.minh30 {
  min-height: 150px;
}

.tablerightcls {
  width: 100%;
  border: 1px solid #e0e0e0;
  border-collapse: collapse;
  text-align: center;
}

.tablerightcls td, .tablerightcls th {
  padding: 3px;
}

.tablecls {
  width: 100%;
  border: 1px solid #e0e0e0;
  border-collapse: collapse;
  text-align: center;
}

.tablecls td, .tablecls th {
  padding: 4px;
}

.colclk {
  color: #409EFF;
  cursor: pointer;
}

.mt10 {
  margin-top: 10px;
}

.mb10 {
  margin-bottom: 5px;
}

.mtrb30 {
  margin: 30px 10px 30px 0;
}

.curpoi {
  cursor: pointer;
}

.font13 {
  font-size: 13px;
}

.shipLineDrawerCls {
  padding: 0px;
}

.recordDiaCls {
  width: 100%;
  /* 滚动条 自动 */
  overflow-y: auto;
}

.minwd30 {
  min-width: 120px;
}

.minwd20 {
  min-width: 100px;
}

.tabbord {
  border: 1px solid #e0e0e0;
  /* border-collapse: collapse; */
}
</style>
