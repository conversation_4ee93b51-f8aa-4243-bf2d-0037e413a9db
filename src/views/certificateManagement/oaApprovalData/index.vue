<template>
  <el-tabs v-model="activeName" @tab-click="handleClick">
    <!-- <el-tab-pane label="oa审批流程" name="first"> -->
      <businessPayment></businessPayment>
    <!-- </el-tab-pane>
    <el-tab-pane label="凭证模板" name="second">
      <CertTempl></CertTempl>
    </el-tab-pane> -->

  </el-tabs>

  <!-- <businessPayment></businessPayment> -->
</template>
<script>
import businessPayment from './businessPayment.vue'
import CertTempl from '@/components/CertTempl/index.vue'
export default{
  name: 'OaApprovalData',
  components: { businessPayment,CertTempl },
  data (){
    return {
      activeName: 'first'
    }
  },
  methods: {
    handleClick(tab, event) {
      console.log(tab, event);
    }
  }
}
</script>
<style scoped>
</style>
