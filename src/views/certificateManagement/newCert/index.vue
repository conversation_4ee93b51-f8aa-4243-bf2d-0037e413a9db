<template>
  <el-tabs v-model="activeName" @tab-click="handleClick" type="border-card" >
    <el-tab-pane label="新增流程" name="first">
      <!-- <el-button @click="openPz">add</el-button> -->
      <new-cert></new-cert>
    </el-tab-pane>
    <el-tab-pane label="凭证模板" name="second">
      <CertTempl></CertTempl>
    </el-tab-pane>

  </el-tabs>

</template>
<script>

import CertTempl from '@/components/CertTempl/index.vue'
import CertificateSave from '@/components/certificate/save.vue'
import NewCert from './newCert.vue'
import dayjs from 'dayjs';

export default{
  name: 'NewCertIndex',
  components: { CertTempl,CertificateSave,NewCert },
  data (){
    return {
      activeName: 'first'
    }
  },
  methods: {
    generateTimeBasedRandom() {
      // 1. 获取当前时间戳（毫秒）
      const timestamp = Date.now();

      // 2. 映射到4-8位范围（1000-99999999）
      const minRange = 1000;
      const maxRange = 99999999;
      // 时间戳取模并映射到目标范围
      const baseValue = timestamp % (maxRange - minRange + 1) + minRange;

      // 3. 加入1-999的随机扰动（避免连续时间生成相同值）
      const randomOffset = Math.floor(Math.random() * 999) + 1;
      return baseValue + randomOffset;
    }
    ,
    handleClick(tab, event) {
      console.log(tab, event);
    },
    openPz() {
      // 随机数
      const data = {

      }
      this.$refs.certificateSave.open('newCert'+dayjs().format('YYYYMMDD')+this.generateTimeBasedRandom(), 'newCert',data,true)
    }
  }
}
</script>
<style scoped>
</style>
