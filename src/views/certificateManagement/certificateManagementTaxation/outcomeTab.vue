<template>
  <el-tabs v-model="activeName" @tab-click="handleClick" type="card">
    <!-- todo -->
    <el-tab-pane label="操作页面" name="first"><summary-outcome></summary-outcome></el-tab-pane>
    <el-tab-pane label="销项明细" name="second"><outcome></outcome></el-tab-pane>
  </el-tabs>
</template>
<script>
import outcome from './outcome.vue';
import summaryOutcome from './summaryOutcome.vue';
export default{
  name: 'CertificateManagementTaxationIncome',
  components: { outcome,summaryOutcome },
  data (){
    return {
      activeName: 'first'
    }
  },
  methods: {
    handleClick(tab, event) {
    }
  }
}
</script>
<style scoped>
</style>
