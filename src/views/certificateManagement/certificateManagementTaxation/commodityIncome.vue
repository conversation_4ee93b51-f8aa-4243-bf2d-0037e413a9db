<template>
  <div class="app-container">
    <!-- 筛选条件 -->
    <!-- 筛选条件 -->
    <div style="text-align: left;">
      <el-input style="width: unset" v-model="queryParams.sellerName" size="medium" placeholder="请输入开票公司"></el-input>
      <el-input style="width: unset" v-model="queryParams.shipName" size="medium" placeholder="请输入船舶名称"></el-input>
      <el-input style="width: unset" v-model="queryParams.invoiceNo" size="medium" placeholder="请输入发票号"></el-input>
      <el-input style="margin: 0 10px;width: unset;" v-model="queryParams.costTypeName" size="medium" placeholder="请输入费用类型"></el-input>
      <el-date-picker
        v-model="queryParams.billMonth"
        type="month"
        value-format="yyyy/M"
        format="yyyy/M"
        placeholder="挂帐月">
      </el-date-picker>
      <el-button @click="getList" type="primary" size="small" :loading="tableLoading">{{tableLoading?'loading...':'搜索'}}</el-button>
    </div>

    <div style="text-align: left;">

    </div>

    <!-- 工具栏 -->
    <vxe-toolbar ref="xToolbar1" custom refresh print>
      <template #buttons>
        <vxe-button @click="openExportEvent">导出</vxe-button>
      </template>
    </vxe-toolbar>

    <!-- 表格内容 -->
    <vxe-table
      border
      max-height="66%"
      ref="xTable1"
      show-footer
      :print-config="tablePrint"
      :loading="tableLoading"
      :export-config="{}"
      :footer-method="footerMethod"
      :data="list"
      auto-resize
      >
      <vxe-column type="seq" width="60" title="序号"></vxe-column>
      <vxe-column field="recordAccountMonth" title="月份">
        <template #default="{ row }">
          <span>{{row.recordAccountMonth ? row.recordAccountMonth.split('/')[1] : ''}}</span>
        </template>
      </vxe-column>
      <vxe-column field="invoiceCode" title="发票代码"></vxe-column>
      <vxe-column field="invoiceDate" title="发票日期"></vxe-column>
      <vxe-column field="sellerName" title="公司名称"></vxe-column>
      <vxe-column field="invoiceNo" title="发票号"></vxe-column>
      <vxe-column field="invoicePriceTaxSum" title="发票金额"></vxe-column>
      <vxe-column field="invoicePriceTax" title="进项税"></vxe-column>
      <vxe-column field="invoicePrice" title="不含税金额"></vxe-column>
      <vxe-column field="invoiceTax" title="税率"></vxe-column>
      <vxe-column field="serviceName" title="服务名称"></vxe-column>
      <vxe-column field="invoiceRemark" title="备注" show-overflow></vxe-column>
      <vxe-column title="操作">
        <template #default="{ row }">
          <el-tooltip class="item" effect="dark" content="点击可查看发票文件" placement="left">
            <vxe-button :disabled="!row.originFile" status="warning" type="text" content="发票" @click="showInvoicePictureHandle(row)"></vxe-button>
            <!-- 应付 -->
          <!-- <el-button v-if="row[codeListKey] && row[codeListCount]==0"  class="btn" type="text"  @click="showYacc(row,codeKeyYf)">生成应付凭证</el-button>
          <el-button v-if="row[codeListKey] && row[codeListCount]>0"  class="btn" type="text"  @click="showTranByCode(row,codeKeyYf)">查看应付凭证</el-button> -->
          </el-tooltip>
        </template>
      </vxe-column>
    </vxe-table>

    <!--分页-->
    <div style="text-align: right;">
      <pagination
        v-show="total>0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        :page-sizes="[10, 100, 200, 300, 500,1000]"
        layout="total,sizes,prev, pager, next"
        @pagination="getList"
      />
    </div>

    <!-- 查看发票 -->
    <el-dialog
      title="查看发票"
      :visible.sync="showInvoicePicture"
      width="60%"
      :close-on-click-modal=false
    >
      <el-image
        class="preview-image"
        :src="invoicePictureUrl"
        :preview-src-list="[invoicePictureUrl]">
      </el-image>
    </el-dialog>
    <certificate-list ref="certificateList"></certificate-list>
    <certificate-save ref="certificateSave" @refreshSave="refreshSave"></certificate-save>
  </div>
</template>

<script>
import 'vxe-table/lib/style.css'
import VXETablePluginExportXLSX from 'vxe-table-plugin-export-xlsx';
import VXETable from 'vxe-table'
VXETable.use(VXETablePluginExportXLSX)
import Pagination from "@/components/Pagination";
import {getStatistical} from "@/api/system/invoice";
import dayjs from 'dayjs'
import CertificateList from '@/components/certificate/list.vue'
import CertificateSave from '@/components/certificate/save.vue'
import { getIsCodeByPidAndCode } from '@/api/business/processapi'
import { mapGetters } from 'vuex'

export default {
  name: "InvoiceStatistical",
  components: { Pagination, CertificateList, CertificateSave },
  computed: {
    ...mapGetters(['taxationCertificateMonth'])
  },
  watch: {
    taxationCertificateMonth: function(newval) {
      console.log('taxationCertificateMonth-commodityIncome', newval)
      if (newval === this.queryParams.billMonth) {
        return
      }
      this.queryParams.billMonth = newval
      this.getList()
    },
  },
  data() {
    return {
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        status: 3, // 3 = 已完成
        sellerName: null,
        shipName: null,
        invoiceDate: null,
        invoiceNo: null,
        costTypeName: null,
        billMonth: this.taxationCertificateMonth || dayjs().format('YYYY/M')
      },
      list: [],
      total: 0,
      tableLoading: false,
      tablePrint: {
        // 自定义打印的样式示例
        style: ``
      },
     invoicePictureUrl: null,
     showInvoicePicture: false,
      dataType:'invoiceStatisticalPyf',
      codeKeyYf:'receivableVouchers_receipt',
      codeListKey: 'rKey',
      codeListCount:'rCount',
    }
  },
  created() {
  },
  mounted() {
    this.getList()
  },
  methods: {
     refreshSave(){
      this.getList();
    },
     uplistByListPz(){
      // pids this.dataList
      const pids = this.list && this.list.map(item => item.id).join(',')
      if (!pids) {
        return
      }
      this.loadIsCodeByPidAndCode(pids, this.codeKeyYf, (res) => {
          this.upPzCodeToList(res,this.codeListKey,this.codeListCount)
      })
    },
    loadIsCodeByPidAndCode(pids,code,func=undefined) {
      getIsCodeByPidAndCode(pids,code).then(res => {
        func && func(res)
      })
    },
    upPzCodeToList(res,key,countKey) {
      if (!res) {
          return
      }
      for (let i = 0; i < this.list.length; i++) {
          const item = this.list[i]
          if (res[item.id]) {
              item[key] = res[item.id].code
              item[countKey] = res[item.id].count
          }
      }
      this.list = this.list.slice()
    },
    showYacc(data,code){
      this.$refs.certificateSave.open(data.id, code,this.dataType)
    },
    showTranByCode(data,code){
      this.$refs.certificateList.open(data.id, code)
    },
    getList() {
      this.tableLoading = true
      // this.queryParams.billMonth = this.taxationCertificateMonth || this.queryParams.billMonth
      this.$store.commit('data/setTaxationCertificateMonth',this.queryParams.billMonth)
      getStatistical(this.queryParams).then(res => {
        this.list = res.data.rows
        this.total = res.data.total
        this.uplistByListPz()
      }).finally(() => {
        this.tableLoading = false
      })
    },
    openExportEvent () {
      this.$refs.xTable1.openExport()
    },
    sumNum (list, field) {
      let count = 0
      list.forEach(item => {
        count += Number(item[field])
      })
      return count.toFixed(2)
    },
    showInvoicePictureHandle(row) {
      this.invoicePictureUrl = row.originFile
      this.showInvoicePicture = true
    },
    footerMethod ({ columns, data }) {
      return [
        columns.map((column, columnIndex) => {
          if (columnIndex === 0) {
            return '合计'
          }
          if (['invoicePriceTaxSum', 'invoicePriceTax','invoicePrice'].includes(column.property)) {
            return this.sumNum(data, column.property)
          }
          return null
        })
      ]
    },
  }
}
</script>

<style scoped>

</style>
