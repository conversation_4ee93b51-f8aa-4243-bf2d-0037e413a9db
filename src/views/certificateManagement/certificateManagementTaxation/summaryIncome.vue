<template>
  <div>
    <!-- <h3>发票汇总</h3> -->
    <el-date-picker
      v-model="queryParams.billMonth"
      type="month"
      format="yyyy/M"
      value-format="yyyy/M"
      placeholder="选择月份"
      style="margin-bottom: 16px;"
      @change="changeMonth"
    />
    <el-table :data="tableData" border style="width: 100%" v-loading="loading">
      <el-table-column prop="type" label="类型" align="center" />
      <el-table-column prop="count" label="发票数" align="center" />
      <el-table-column prop="invoice_price" label="金额" align="center" :formatter="moneyFmt" />
      <el-table-column prop="invoice_price_tax" label="税额" align="center" :formatter="moneyFmt"/>
      <el-table-column prop="invoice_price_tax_sum" label="价税合计" align="center"  :formatter="moneyFmt"/>
      <el-table-column label="操作" align="center">
        <template slot-scope="scope">
           <el-button type="text" size="small" v-if="scope.row[codeListKey] && scope.row[codeListCount]===0" @click="showSavePz(scope.row.id,codeKey,scope.row)">生成凭证</el-button>
           <el-button type="text" size="small" v-if="scope.row[codeListKey] && scope.row[codeListCount] > 0" @click="showPzByCode(scope.row.id,codeKey)">查看凭证</el-button>
        </template>

      </el-table-column>
    </el-table>
    <certificate-list ref="certificateList" ></certificate-list>
    <certificate-save ref="certificateSave" @refreshSave="refreshSave"></certificate-save>
  </div>
</template>
<script>
import {purchaseInvoiceSummary} from "@/api/system/invoice";
import dayjs from 'dayjs'
import currency from 'currency.js'
import CertificateList from '@/components/certificate/list.vue'
import CertificateSave from '@/components/certificate/save.vue'
import { getIsCodeByPidAndCode } from '@/api/business/processapi'
import { mapGetters } from 'vuex'
export default {
  components: {
    CertificateList,CertificateSave
  },
  computed: {
    ...mapGetters(['taxationCertificateMonth'])
  },
  watch: {
    taxationCertificateMonth: function(newval,oldval) {
      console.log('new: %s, old: %s',newval,oldval)
      if (newval === this.queryParams.billMonth) {
        return
      }
      this.queryParams.billMonth = newval
      this.loadData()
    },
  },
  data() {
    return {
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        status: 3, // 3 = 已完成
        sellerName: null,
        serviceName:null,
        shipName: null,
        invoiceDate: null,
        invoiceNo: null,
        costTypeName: null,
        billMonth: this.taxationCertificateMonth || dayjs().format('YYYY/M'), // 默认当前月
        modelType:'ship',
        type:'oil'
      },
      tableData: [],
      codeKey:'taxAndFeeTransferVoucher',
      codeListKey: 'rKey',
      codeListCount: 'rCount',
      loading: false
    };
  },
  created() {
    this.loadData()
  },
  methods: {
    changeMonth() {
      // this.$store.dispatch('data/setTaxationCertificateMonth',this.queryParams.billMonth)
      this.loadData()
    },
    refreshSave(){
      this.loadData()
    },
    uplistByListPz(){
      const pids = this.tableData && this.tableData.map(item => item.id).join(',')
      if (!pids) {
        return
      }
      // 异步加载数据并更新
      Promise.all([this.loadIsCodeByPidAndCode(pids, this.codeKey)]).then(([res1]) => {
        this.upPzCodeToList(res1, this.codeListKey, this.codeListCount)
      })

    },
    loadIsCodeByPidAndCode(pids,code) {
     return getIsCodeByPidAndCode(pids,code)
    },
    upPzCodeToList(res,key,countKey) {
      if (!res) {
          return
      }
      this.tableData = this.tableData.map(item => {
      if (res[item.id]) {
          return {
            ...item,
            [key]: res[item.id].code,
            [countKey]: res[item.id].count,
          };
        }
        return item;
      });
    },
    showSavePz(id,code,data){
      if(!data.invoice_price_tax){
        this.$message.error('税额为空')
        return
      }
      this.$refs.certificateSave.open(id, code,data,true)
    },
    showPzByCode(id,code){
      this.$refs.certificateList.open(id, code)
    },
    moneyFmt(row, column, v, index) {
      if (v) {
       return  currency(v, { symbol: '', precision: 2 }).format()
      }
      return 0
    },
    loadData(){
      this.loading = true;
      this.$store.commit('data/setTaxationCertificateMonth', this.queryParams.billMonth)
      console.log('month',this.taxationCertificateMonth)
      // this.queryParams.billMonth = this.taxationCertificateMonth || this.queryParams.billMonth
      purchaseInvoiceSummary(this.queryParams).then(res=>{
        // 期望返回结构见注释
        // 解析数据
        const data = res.data || {};
        this.tableData = [
          {
            type: '业务',
            model_code:'business',
            model_name:'待抵扣进项税额',
            id:'business'+dayjs(this.queryParams.billMonth).format('YYYYM'),
            count: data.invoice.data.count || 0,
            invoice_price: data.invoice.data.invoice_price || 0,
            invoice_price_tax: data.invoice.data.invoice_price_tax || 0,
            invoice_price_tax_sum: data.invoice.data.invoice_price_tax_sum || 0
          },
          {
            type: '船舶',
            model_code:'ship',
            model_name:'待抵扣进项税额（船舶配载）',
            id:'ship'+dayjs(this.queryParams.billMonth).format('YYYYM'),
            count: data.shipInvoice.data.count || 0,
            invoice_price: data.shipInvoice.data.invoice_price || 0,
            invoice_price_tax: data.shipInvoice.data.invoice_price_tax || 0,
            invoice_price_tax_sum: data.shipInvoice.data.invoice_price_tax_sum || 0
          },
          {
            type: '油品',
            model_code:'oil',
            model_name:'待抵扣进项税额',
            id:'oil'+dayjs(this.queryParams.billMonth).format('YYYYM'),
            count: data.oilInvoice.data.count || 0,
            invoice_price: data.oilInvoice.data.invoice_price || 0,
            invoice_price_tax: data.oilInvoice.data.invoice_price_tax || 0,
            invoice_price_tax_sum: data.oilInvoice.data.invoice_price_tax_sum || 0
          },
          {
            type: '油品运费',
            model_code:'oilCost',
            model_name:'待抵扣进项税额',
            id:'oilCost'+dayjs(this.queryParams.billMonth).format('YYYYM'),
            count: data.oilCostInvoice.data.count || 0,
            invoice_price: data.oilCostInvoice.data.invoice_price || 0,
            invoice_price_tax: data.oilCostInvoice.data.invoice_price_tax || 0,
            invoice_price_tax_sum: data.oilCostInvoice.data.invoice_price_tax_sum || 0
          }
        ];
        this.uplistByListPz()
      }).finally(()=>{
        this.loading = false;
      })
    }
  }
}
</script>
<style scoped>
</style>
