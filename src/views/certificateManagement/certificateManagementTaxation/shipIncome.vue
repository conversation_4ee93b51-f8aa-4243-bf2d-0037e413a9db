<template>
  <section>
     <el-tabs v-model="activeName" @tab-click="handleClick" >
      <!-- todo -->
      <el-tab-pane label="船舶" name="first"><ship-invoice-income mcode="ship"></ship-invoice-income></el-tab-pane>
      <el-tab-pane label="油品" name="second"><ship-invoice-income mcode="oil"></ship-invoice-income></el-tab-pane>
      <el-tab-pane label="油品运费" name="third"><ship-invoice-income mcode="oilCost"></ship-invoice-income></el-tab-pane>

    </el-tabs>
  </section>
</template>
<script>
import shipInvoiceIncome from './ship/shipInvoiceIncome.vue';

export default{
  components: { shipInvoiceIncome },
  data (){
    return {
      activeName: 'first'
    }
  },
  methods: {
    handleClick(tab, event) {
      console.log(tab, event);
    }
  }
}
</script>
<style scoped>
</style>
