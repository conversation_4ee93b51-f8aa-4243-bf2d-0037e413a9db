<template>
  <el-tabs v-model="activeName"  @tab-click="handleClick" type="card">
    <el-tab-pane label="物流配载"  name="enterTicketfirst">
      <statistical></statistical>
    </el-tab-pane>
    <el-tab-pane label="船舶配载"  name="shipIncome">
      <ship-income></ship-income>
    </el-tab-pane>
     <el-tab-pane label="库存消耗-油品"  name="oilIncome">
      <oil-income></oil-income>
    </el-tab-pane>
  </el-tabs>
</template>
<script>
import statistical from './statistical.vue';
import shipIncome from './shipIncome.vue';
import oilIncome from './oilIncome.vue';
export default{
  name: 'enterTicket',
  components: { statistical,shipIncome,oilIncome },
  data (){
    return {
      activeName: 'enterTicketfirst'
    }
  },
  methods: {
    handleClick(tab, event) {
      console.log(tab, event);
    }
  }
}
</script>
<style scoped>
</style>
