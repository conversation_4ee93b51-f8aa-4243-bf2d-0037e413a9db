<template>
  <section style="margin-top: 20px;margin-left: 20px;margin-right: 20px;">
    <div class="sp-title" style="width: 100%;text-align: center;font-size: 25px">
      {{ $route.query.title }}
    </div>
    <div v-for="(item,index) in formSchema" v-if="formSchema.length > 0">
      <div v-if="item.key === 'form'">
        <el-divider content-position="left">{{ item.showT }}</el-divider>
        <avue-form
          ref="form"
          v-model="obj"
          :option="item.option"
          @reset-change="emptytChange"
          @submit="submit"
          @error="error"
        >
        </avue-form>
        <div v-show="index==0 && obj.shebao && obj.shebao=='是'" style="display: flex;justify-content: center;">
              <el-button @click="downUrl('gz')"   size="small" type="success">点击下载工资模版</el-button>
        </div>
      </div>
      <div v-if="item.key === 'contract'">
        <el-divider content-position="left">{{ item.showT }}
        </el-divider>
        <div>
          <el-switch
            v-model="contractHistroy"
            style="display: block"
            active-color="#00a0e9"
            inactive-color="#00a0e9"
            v-if="item.itemType != 'foreignContract'"
            :active-text="'上传新的合同'"
            :inactive-text="'选择历史合同'"
          />
        </div>
        <div style=" width: 100%; display: inline-flex">
          <avue-form
            ref="form"
            v-model="obj"
            :option="item.option"
            @reset-change="emptytChange"
            @submit="submit"
          />
          <el-upload
            class="upload-demo"
            :action="upload"
            style="width: 100%;"
            :on-preview="handlePreview"
            :on-remove="handleRemove"
            :before-remove="beforeRemove"
            :on-success="savefileListconrtact"
            :before-upload="beforeUpload"
            :on-error="handleError"
            multiple
            :on-exceed="handleExceed"
            :file-list="contractimageList"
          >
            <el-button v-if="contractHistroy || item.itemType == 'foreignContract'  " size="small" type="primary">点击上传</el-button>
          </el-upload>
          <el-dialog :visible.sync="dialogVisible" append-to-body>
            <img width="100%" :src="dialogImageUrl" alt="">
          </el-dialog>
        </div>
      </div>
      <div v-if="item.key === 'image'">
        <el-divider content-position="left">文件上传</el-divider>
        <el-upload
          class="upload-demo"
          :action="upload"
          :on-preview="handlePreview"
          :on-remove="handleRemove"
          :before-remove="beforeRemove"
          :on-success="savefileList"
          :before-upload="beforeUpload"
          :on-error="handleError"
          multiple
          :on-exceed="handleExceed"
          :file-list="fileList"
        >
          <el-button size="small" type="primary">点击上传</el-button>
        </el-upload>
        <el-dialog :visible.sync="dialogVisible" append-to-body>
          <img width="100%" :src="dialogImageUrl" alt="">
        </el-dialog>
      </div>
      <div v-if="item.key === 'crud'" @click="checkoutindex(index)">
        <el-divider content-position="left">{{ item.showT }}</el-divider>
        <avue-crud
          ref="crud"
          v-model="crubform"
          :data="listq[`data${index}`]"
          :option="!isOrcChaiLv ? Object.assign({},item.option,{editBtn: false,delBtn: false}) : item.option"
          @row-del="rowDel"
          @row-save="handleRowSave"
          @row-update="handleRowUpdate"
        >
        <template #menu="{ size, row, index }">
              <el-button  :size="size"
                  v-if="!isOrcChaiLv"
                  type="text"
                  :disabled="row.taxRate && row.taxRate!=5"
                 icon="el-icon-edit"
                 @click="rowEditCrud(row, index,'crud')">编辑</el-button>
              <el-button  :size="size"
                  v-if="!isOrcChaiLv"
                  type="text"
                   icon="el-icon-delete"
                 @click="rowDelCrud(row, index,'crud')">删除</el-button>
        </template>
      </avue-crud>
          <el-upload
            class="upload-demo"
            style="margin-bottom: 20px;margin-top:10px;"
            :action="upload"
            :on-preview="handlePreview"
            :on-remove="handleRemove"
            :before-remove="beforeRemove"
            :on-success="savefileList"
            :before-upload="beforeUpload"
            :on-error="handleError"
            multiple
            :on-exceed="handleExceed"
            :file-list="fileList"
          >
              <el-button size="small" type="primary">点击上传文件 <span v-if="!isOrcChaiLv">发票上传 系统自动识别；无需手动填写。</span></el-button>
            </el-upload>
            <el-dialog :visible.sync="dialogVisible" append-to-body>
              <img width="100%" :src="dialogImageUrl" alt="">
            </el-dialog>
      </div>
      <div v-if="item.key === 'group-crud'" @click="checkoutindex(index)">
        <el-button size="small" type="primary" @click="addsize(item.crubtype)">新增费用</el-button>
        <el-select v-if="item.crubtype" v-model="curbtypename" placeholder="请选择">
            <el-option
              v-for="items in item.crubtype"
              :key="items.name"
              :label="items.name"
              :value="items.option"
            />
        </el-select>
        <div v-for="(items,indexs) in groupCrubSize" @click="checkoutgroupindex(indexs)">
          <div v-show="groupCrubTypeList[indexs] !== 'noshow'">
            <!-- <el-divider v-if="groupCrubSize >0" style="margin: 0;" content-position="left">{{ crubtypeFmt(groupCrubTypeList[indexs]) }}{{ indexs+1 }}</el-divider> -->
            <!-- <hr/> -->
            <el-button style="position:relative;top: 32px;left: 90px;z-index: 2" type="primary" @click="delcrud(index,indexs)">删除明细{{ indexs+1 }}</el-button>
            <span v-if="groupCrubSize >0" style="position:relative;top: 32px;left:45%;z-index: 2">{{ crubtypeFmt(groupCrubTypeList[indexs]) }}{{ indexs+1 }}</span>
            <avue-crud
              ref="crud"
              v-model="groupcrubform"
              :data="listq[`data${index}-${indexs}`]"
              :option="item[`${groupCrubTypeList[indexs]}`]"
              @row-del="rowDelg"
              @row-save="handleRowSaveg"
              @row-update="handleRowUpdateg"
            />
            <!-- <el-divider content-position="left">文件上传</el-divider> -->
            <el-upload
              class="upload-demo"
              style="margin-bottom: 20px;margin-top:10px;"
              :action="upload"
              :on-preview="handlePreviewg"
              :on-remove="handleRemove"
              :before-remove="beforeRemove"
              :on-success="savefileListg"
              :before-upload="beforeUpload"
              :on-error="handleError"
              multiple
              :on-exceed="handleExceed"
              :file-list="fileListg[indexs]"
            >
              <el-button size="small" type="primary">点击上传文件</el-button>
            </el-upload>
          </div>
        </div>
        <el-dialog :visible.sync="dialogVisibleg" append-to-body>
          <img width="100%" :src="dialogImageUrlg" alt="">
        </el-dialog>
      </div>
      <!-- && '有' == obj.invoice -->
      <!-- <div v-if="item.key === 'shui-crud'">
        <el-divider content-position="left">{{ item.showT }}</el-divider>
        <avue-crud
        :data="totalShuiList"
        :option="item.option"
        ></avue-crud>
      </div> -->
    </div>
    <!-- <el-divider content-position="left">票据税额</el-divider>
    <section class="section-grid-cls">
        <span >类型</span>
        <span >金额</span>
        <span >税额</span>
        <span >税率</span>
        <template v-for="item in totalShuiList">
          <span>{{ item.subTypeName }}</span>
          <span>{{ item.total }}</span>
          <span>{{ item.tax }}</span>
          <span>{{ item.taxRate }}</span>
        </template>
        <span>合计</span>
        <span>{{ sumAdd(totalShuiList, 'total')}}</span>
        <span>{{ sumAdd(totalShuiList, 'tax')}}</span>
        <span></span>
    </section> -->
    <el-divider content-position="left">审批流程</el-divider>
    <div style="margin-bottom: 40px">
      <el-timeline id="timelineBody">
        <el-timeline-item
          :timestamp="activities.timestamp"
          placement="top"
        >
          <div>
            <div style="color: #909399;font-size: 10px">
              {{ activities.peoples.length }}人审批
            </div>
            <el-row class="timelineContent">
              <div v-for="people in activities.peoples" style="margin-left:10px">
                <el-tooltip
                  class="item"
                  effect="light"
                  :content="people.sp_type=='0'?people.name:people.avatar"
                  placement="top-start"
                >
                  <el-avatar v-if="people.sp_type=='0'" shape="square" size="large" :src="people.avatar" />
                  <span v-if="people.sp_type=='1'" class="diy-avatar">{{ (people.avatar || '').substring(0, 1) }}</span>
                </el-tooltip>
                <!--                  <el-tooltip class="item" effect="light" :content="people.sp_type=='1'?people.avatar:people.name" placement="top-start">-->
                <!--                    <span class="diy-avatar" v-if="people.sp_type=='1'">{{people.avatar.substring(0,1)}}</span>-->
                <!--                  </el-tooltip>-->
              </div>
            </el-row>
          </div>
        </el-timeline-item>
        <el-timeline-item
          :timestamp="ccs.timestamp"
          placement="top"
        >
          <div>
            <div v-if="ccs.peoples.length > 0" style="color: #909399;font-size: 10px">
              抄送给{{ ccs.peoples.length }}人
            </div>
            <div v-else style="color: #909399;font-size: 10px">
              请选择抄送人
            </div>
            <el-row class="timelineContent">
              <div v-for="people in ccs.peoples" style="margin-left:10px">
                <div style="position: relative">
                  <el-avatar shape="square" size="large" :src="people.avatar" />
                  <div
                    class="el-icon-error"
                    style="color:red;position: absolute;top:0;right: 10px;transform: translateY(-50%) translateX(100%);"
                    @click="removeCc(people.id)"
                  />
                </div>
              </div>
              <div style="margin-left:10px;padding-bottom: 3px">
                <el-button class="el-icon-plus wh40" @click="addCc" />
              </div>
            </el-row>
            <div v-if="ccs.roles.length > 0" style="color: #909399;font-size: 10px">
              抄送角色：
            </div>
            <el-row class="timelineContent">
              <div v-for="tag in ccs.roles" style="margin-left: 10px;">
                <el-tag
                  closable
                  @close="removeRole(tag.id)"
                >
                  {{ tag.name }}
                </el-tag>
              </div>
            </el-row>
          </div>
        </el-timeline-item>
      </el-timeline>
      <div class="timelineContent">
        <el-button size="small" type="primary" :loading="uploadSt" @click="saveContractOrNot()">{{
          loading ? '提交中 ...' : beforeUploadStatus>0?'上传中..': '确 定'
        }}
        </el-button>
      </div>
    </div>
    <depart-component ref="depart" @child-cc="childCheckedCc" />
  </section>
</template>

<script>
import esForm from 'vue-easy-form'

import store from '@/store'
import { getuserxinxi, getprocessnum } from '@/api/business/wxDepartmentapi'
import { saveContract } from '@/api/business/sysContractapi'
import DepartComponent from '@/components/workflow/depart'
import { getAllCompany } from '@/api/business/wxDepartmentapi'
import { processVal } from '@/api/system/process'
const processApi = require('@/api/system/process')
import Avue from '@smallwei/avue'
import { UPLOAD_URL } from '@/utils/config'
import Vue from 'vue'
import '@smallwei/avue/lib/index.css'
import currency from 'currency.js'
import Yaml from '@/components/YamlEdit/index';
import { GZ_EXCEL_URL } from '@/utils/config'
import { getWageTemplate } from '@/api/business/processapi'
import { recognizeOcrByImgurl } from '@/api/system/ocr'
import {checkAcceptance} from '@/api/system/receiveWater'
import dayjs from 'dayjs'
Vue.use(Avue)
Vue.use(esForm)

var DIC = {
  VAILD: [{
    label: '真',
    value: 'true'
  }, {
    label: '假',
    value: 'false'
  }],
  SEX: [{
    label: '男',
    value: 0
  }, {
    label: '女',
    value: 1
  }]
}
export default {
  name: 'SubmitProcess',
  components: { DepartComponent },
  data() {
    return {
      beforeUploadStatus:0, // 是否上传中
      crubtype: [],
      contractHistroy: false,
      contractimageList: [],
      fileListg: {},
      dialogImageUrl: '',
      dialogVisible: false,
      dialogImageUrlg: '',
      dialogVisibleg: false,
      upload: UPLOAD_URL,
      radio1: 'Form',
      form: {},
      list: [],
      obj: {},
      sizeValue: 'small',
      groupindex: 0,
      listq: {},
      groupCrubTypeList: [],
      groupCrubSize: 0,
      data: [
        {
          name: '张三',
          sex: '男'
        }, {
          name: '李四',
          sex: '女'
        }, {
          name: '王五',
          sex: '女'
        }, {
          name: '赵六',
          sex: '男'
        }
      ],
      option: {
        'menuBtn': false
      },
      fileList: [],
      drawer: false,
      loading: false,
      loadingComponent: false,
      activities: {
        timestamp: '审批人',
        peoples: [
          // {
          //   id: 1,
          //   name: 'sunzs',
          //   avatar: 'https://wework.qpic.cn/wwhead/duc2TvpEgSQO4BpE0WZSZ8UQwKs1xRibCJLiaWayXITKUe6aRaruiacDy32HKIwTnlR6BBzWiaVfq6E/0'
          // },
          // {
          //   id: 2,
          //   name: 'luoyh',
          //   avatar: 'http://wework.qpic.cn/bizmail/PqYXeYnKUc9icFFUvxBE8tyUt0sqmdTNg6eW4jicicX9dic1yqsWW0XBJQ/0'
          // }
        ]
      },
      ccs: {
        timestamp: '抄送人',
        peoples: [
          // {
          //   id: 1,
          //   name: 'sunzs',
          //   avatar: 'https://wework.qpic.cn/wwhead/duc2TvpEgSQO4BpE0WZSZ8UQwKs1xRibCJLiaWayXITKUe6aRaruiacDy32HKIwTnlR6BBzWiaVfq6E/0'
          // },
          // {
          //   id: 2,
          //   name: 'luoyh',
          //   avatar: 'http://wework.qpic.cn/bizmail/PqYXeYnKUc9icFFUvxBE8tyUt0sqmdTNg6eW4jicicX9dic1yqsWW0XBJQ/0'
          // }
        ],
        roles: [
          // {
          //   id: '1',
          //   name: 'CEO'
          // }
        ]
      },
      contractlist: [],
      groupcrubform: {},
      crubform: {},
      formValue: [],
      curbtypename: 'option',
      formSchema: [],
      cruddataindex: 99,
      companylist: [],

      // 摘要
      briefContent: undefined,
      processCode: undefined,
      // 全局参数，请传入map
      // {
      //   params1: '',
      // ...
      //   params10: ''
      // }
      globalParams: {},
      totalGzSum: 0,
      totalShuiList: [],
      // invoiceData: [],
      totalShuiLoading: false
    }
  },
  computed: {
    // 上传中
    uploadSt() {
      return this.loading || this.beforeUploadStatus>0
    },
    isYeWuZhaoDai() {
      return (this.processCode == 'Reimbursement' && this.$route.query.processJian == 'YWZD') || (this.processCode == 'YeWuReimbursement' && this.$route.query.processJian == 'YWZD')
    },
    isFeiYongBX() {
      return this.processCode == 'Reimbursement' && this.$route.query.processJian == 'BX'
    },
    isChaiLvBX() {
      return this.processCode == 'TravelReimbursement' && this.$route.query.processJian == 'CL'
    },
    isOrcChaiLv(){
      return this.processCode != 'TravelReimbursement' && this.processCode != 'Reimbursement' && this.processCode != 'YeWuReimbursement'
    }
  },
  watch: {
    'crubform.costType': function (val) {
      // {
      //         "label": "交通费-火车",
      //         "value": 0
      //       },
      //       {
      //         "label": "交通费-飞机票",
      //         "value": 1
      //       },
      //       {
      //         "label": "交通费-轮船票",
      //         "value": 2
      //       },
      //       {
      //         "label": "交通费-市内交通",
      //         "value": 3
      //       },
      //       {
      //         "label": "住宿费",
      //         "value": 4
      //       },
      //       {
      //         "label": "车辆费",
      //         "value": 5
      //       },
      //       {
      //         "label": "伙食补助费",
      //         "value": 6
      //       },
      this.crubform.taxRate = 5
      // if (this.isChaiLvBX) {
      //   if (val == 1) {
      //     this.crubform.taxRate = 3
      //   }
      //   if (val == 2) {
      //     this.crubform.taxRate = 3
      //   }
      //   if (val == 3) {
      //     this.crubform.taxRate = 5
      //   }
      //   if (val == 4) {
      //     this.crubform.taxRate = 5
      //   }
      //   if (val == 5) {
      //     this.crubform.taxRate = 3
      //   }
      //   if (val == 6) {
      //     this.crubform.taxRate = 5
      //   }
      //   if (val == 7) {
      //     this.crubform.taxRate = 5
      //   }
      // }

    },
    'crubform.taxRate': function (faxId) {
      if (!this.crubform.sum) {
        this.crubform.tax = 0
        this.crubform.pretaxAmount = 0
        return
      }
      let fax = 0;
      if (faxId == 1) {
        fax = 0.03
      }
      if (faxId == 2) {
        fax = 0.06
      }
      if (faxId == 3) {
        fax = 0.09
      }
      if (faxId == 4) {
        fax = 0.13
      }
      if (fax != 0) {
         // sum 3%, 金额÷（1+3%）×3%
        // this.groupcrubform.shui = (val / 1.03) * 0.03
        // currency
        // this.groupcrubform.shuono = currency(this.groupcrubform.sum / (1+fax) * fax).value
        this.crubform.tax = this.shuiFax(this.crubform.sum,fax)
        // this.groupcrubform.shuono = val - this.groupcrubform.shui
        this.crubform.pretaxAmount = currency(this.crubform.sum - this.crubform.tax).value
      } else {
        this.crubform.tax = 0
        this.crubform.pretaxAmount = 0
      }
    },
    'crubform.sum': function (val) {
      this.crubform.author = this.obj.name
      if (!this.crubform.taxRate) {
        this.crubform.tax = 0
        this.crubform.pretaxAmount = 0
        return
      }
      let fax = 0
      if (this.crubform.taxRate == 1) {
        fax = 0.03
      }
      if (this.crubform.taxRate == 2) {
        fax = 0.06
      }
      if (this.crubform.taxRate == 3) {
        fax = 0.09
      }
      if (this.crubform.taxRate == 4) {
        fax = 0.13
      }
      if (fax != 0) {
        this.crubform.tax = this.shuiFax(val, fax)
        this.crubform.pretaxAmount = currency(val - this.crubform.tax).value
      } else {
        this.crubform.tax = 0
        this.crubform.pretaxAmount = 0
      }
    },
    'groupcrubform.purpose': function(val) {
      // console.log(val)
      for (var a = 0; a < this.formSchema.length; a++) {
        var item = this.formSchema[a]
        if (item.key === 'crud' || item.key === 'group-crud') {
          for (var b = 0; b < item.option.column.length; b++) {
            var items = item.option.column[b]
            if (items.prop === 'cash') {
              if (val === 99) {
                items.display = true
              } else {
                items.display = false
              }
            }
          }
        }
      }
    },
    'groupcrubform.sum': function (val) {
      // 根据发票类型 计算 税额
      if(val=='' || val==undefined) return;
      let faxIdx = undefined;
      if (this.groupcrubform.invoiceType == 1 || this.groupcrubform.invoiceType == 5) {
        faxIdx = 1
      }
      if (this.groupcrubform.invoiceType == 2 || this.groupcrubform.invoiceType == 3 || this.groupcrubform.invoiceType == 4) {
        faxIdx = 2
      }
      if (faxIdx != undefined) {
        this.groupcrubform.fax = faxIdx
      } else {
        this.loadShuiFaxAndSum(this.groupcrubform.fax)
      }
    },
    'groupcrubform.fax': function (val) {
      if (!this.groupcrubform.sum) {
        return
      }
      this.loadShuiFaxAndSum(val)
    },
    'groupcrubform.invoiceType': function(val) {
      for (var a = 0; a < this.formSchema.length; a++) {
        var item = this.formSchema[a]
        if (item.key === 'crud' || item.key === 'group-crud') {
          for (var b = 0; b < item.option.column.length; b++) {
            var items = item.option.column[b]
              // items.prop === 'shui' || items.prop === 'shuono' ||
              if ( items.prop === 'fax') {
              if (val === 0) {
                if (!this.groupcrubform.sum) {
                  this.groupcrubform.shui = 0
                  this.groupcrubform.shuono = 0
                }
                items.display = true
              } else {
                this.groupcrubform.shui = 0
                this.groupcrubform.shuono = 0
                items.display = false
              }
            }
            // if (items.prop === 'sum') {
            //   if (val === 0) {
            //     items.disabled = true
            //   } else {
            //     items.disabled = false
            //   }
            // }
          }
          for (var i = 0; i < 10; i++) {
            if (!item[`option${i}`]) {
              continue
            }
            for (var c = 0; c < item[`option${i}`].column.length; c++) {
              var itemz = item[`option${i}`].column[c]
               // itemz.prop === 'shui' || itemz.prop === 'shuono' ||
               if ( itemz.prop === 'fax') {
                if (val === 0) {
                  if (!this.groupcrubform.sum) {
                    this.groupcrubform.shui = 0
                    this.groupcrubform.shuono = 0
                  }
                  itemz.display = true
                } else {
                  this.groupcrubform.shui = 0
                  this.groupcrubform.shuono = 0
                  itemz.display = false
                }
              }
              // if (itemz.prop === 'sum') {
              //   if (val === 0) {
              //     itemz.disabled = true
              //   } else {
              //     itemz.disabled = false
              //   }
              // }
            }
          }
        }
      }
    }
  },
  created() {
    this.processCode = this.$route.query.code
    this.globalParams.params7 = this.$route.query.type
    // 获取旧数据
    // if (this.$route.query.relyProcessId) {
    //   this.getOldData(this.$route.query.relyProcessId)
    // }
    // this.doInit()
    this.sortFun()
  },
  mounted() {
    // var a = JSON.stringify(this.formSchema)
    // console.log(a)
    // console.log(this.upload)

    getuserxinxi().then(res => {
      this.obj.name = res.data[0].username
      let comId = ''
      let deptId = ''
      if (res.data[0].cid) {
        comId = res.data[0].cid
        deptId = res.data[0].deid
      } else {
        comId = res.data[0].deid
      }
      // this.obj.company = comId
      // this.obj.Department = deptId
      this.$nextTick(() => {
        // this.obj.company = comId
        this.$set(this.obj, 'company', comId)
        if (deptId) {
          // this.obj.Department = deptId
          this.$set(this.obj, 'Department', deptId)
        }
      })
      getprocessnum(comId, this.$route.query.processJian).then(ress => {
        this.obj.Numbers = ress.num
      })
    })
    getAllCompany().then(res => {
      this.companylist = res.data
    })
  },
  methods: {
    changeForm({opositeUnit}) {
      // console.log('changeForm', opositeUnit)
      if (opositeUnit) {
        const res = this.formSchemaDicUrl('opositeUnit')
        console.log('res',res)
        // this.obj.opositeUnitName_ = ''
      } else {
        // this.obj.opositeUnitName_ = ''
      }
    },
    formSchemaDicUrl(key) {
      console.log('formSchemaDicUrl', key)
      for (let a = 0; a < this.formSchema.length; a++) {
        const columns = this.formSchema[a].option.column
        if (!columns) {
          continue
        }
        for (let i = 0; i < columns.length; i++){
          if (columns[i].prop === key) {

            const res = { url: columns[i].dicUrl, method: columns[i].dicMethod }
            console.log(columns[i],res)
            return res
          }
        }
      }
    },
    checkAcceptanceCd() {
      if (this.processCode != 'AcceptanceDiscount') {
        return
      }
      // 承兑状态加载中
      this.loadCdStats = true
      // 承兑是否通过
      this.isCheckAcceptanceByCdStatus = false
      this.isCheckAcceptanceByCdStatusErrMsg = ''
      if (this.listq == null) {
        this.loadCdStats = false
        return
      }
      const numberAndMoneys = []
      Object.keys(this.listq).forEach(item => {
        const list = this.listq[item]
        list.forEach(item => {
          if (item.bill_number) {
            numberAndMoneys.push(item.bill_number + ',' + item.sum);
          }
        })
      })
      if (numberAndMoneys.length == 0) {
        this.loadCdStats = false
        return
      }
      checkAcceptance(numberAndMoneys).then(res => {
        if (res && res.data && res.data.length) {
          // 有错误
          const errMsg = res.data.map(item => {
            const n = item.split(',')[0]
            const m = item.split(',')[1]
            return '承兑号：'+n+',金额：'+m+'，已使用'
          }).join('\n')
          this.isCheckAcceptanceByCdStatusErrMsg=errMsg
        } else {
          this.isCheckAcceptanceByCdStatus = true
        }
        this.loadCdStats = false
      })
    },
    isCheckAcceptanceByCd() {
      // 验证承兑号，金额 是否未使用
      if (this.processCode != 'AcceptanceDiscount') {
        return false
      }
      if (this.loadCdStats) {
        this.$message.error('承兑数据加载中')
        return true
      }
      if (!this.isCheckAcceptanceByCdStatus) {
        if (this.isCheckAcceptanceByCdStatusErrMsg) {
          this.$message.error(this.isCheckAcceptanceByCdStatusErrMsg)
        }
        return true
      }
      return false
    },
    sumAdd(list,key) {
      let sum = currency(0)
      list.forEach(item => {
        // sum += item[key]
        sum = currency(item[key]).add(sum)
      })
      return sum.value
    },
    loadShuiFaxAndSum(faxId) {
      if (!this.groupcrubform.sum ) {
        return
      }
      let fax = 0;
      if (faxId == 1) {
        fax = 0.03
      }
      if (faxId == 2) {
        fax = 0.09
      }
      if (faxId == 3) {
        fax = 0.13
      }
      if (faxId === 0) {
        fax = 0.06
      }
      if (fax != 0) {
         // sum 3%, 金额÷（1+3%）×3%
        // this.groupcrubform.shui = (val / 1.03) * 0.03
        // currency
        // this.groupcrubform.shuono = currency(this.groupcrubform.sum / (1+fax) * fax).value
        this.groupcrubform.shuino = this.shuiFax(this.groupcrubform.sum,fax)
        // this.groupcrubform.shuono = val - this.groupcrubform.shui
        this.groupcrubform.shui = currency(this.groupcrubform.sum - this.groupcrubform.shuono).value
      }
    },
    shuiFax(price,fax) {
      return currency(price / (1+fax) * fax).value
    },
    loadGzSum(fileList) {
      this.totalGzSum=0
      if (!fileList || this.processCode != 'ExternalPaymentApplication') {
        // this.loadChaiLv()
        return
      }
      getWageTemplate(fileList).then(res => {
        if (res && res.data) {
          this.totalGzSum = res.data
        }
      })
    },
    loadFeiYong() {
      // Reimbursement
      // this.$route.query.processJian
      return this.processCode == 'Reimbursement' && this.$route.query.processJian=='YWZD'
    },
    loadChaiLv(mfile) {
      // this.fileListg
      // if (this.processCode != 'TravelReimbursement' && this.processCode != 'Reimbursement' && this.processCode != 'YeWuReimbursement') {
      //   return
      // }
      if(this.isOrcChaiLv){
        return
      }
      // 差旅报销
      const bxObj = mfile ? this.totalShuiList : []
      // const invoiceObj = mfile ? this.invoiceData : []
      let loadCount = 0
      // console.log('chailv', this.fileListg)
      if (this.fileList && Object.keys(this.fileList).length > 0) {
        this.totalShuiLoading = true
        if (mfile && mfile.response && mfile.response.url) {
            loadCount++
            recognizeOcrByImgurl(mfile.response.url).then(res => {
              if (res) {
                // if (!bxObj[key]) {
                //   bxObj[key]={}
                // }
                // bxObj[key][j] = JSON.parse(res)
                bxObj.push(...this.ppResNe(JSON.parse(res), mfile.response.url,mfile.name))
              }
            }).finally(() => {
              loadCount--
              this.totalShuiLoading = false
              if (loadCount == 0) {
                this.loadShui(bxObj)
              }
            })
        } else {
          const files = this.fileList
            for (let j = 0; j < files.length; j++) {
              const file = files[j]
              if (file.response && file.response.url) {
                loadCount++
                recognizeOcrByImgurl(file.response.url).then(res => {
                  if (res) {
                    // if (!bxObj[key]) {
                    //   bxObj[key]={}
                    // }
                    // bxObj[key][j] = JSON.parse(res)
                    bxObj.push(...this.ppResNe(JSON.parse(res),file.response.url,file.name))
                  }
                }).finally(() => {
                  loadCount--
                  this.totalShuiLoading = false
                  if (loadCount == 0) {
                    this.loadShui(bxObj)
                  }
                })
              }
            }
        }
        // Object.entries(this.fileListg).forEach(([key, value]) => {

        //  })
      }


    },
    invoiceOcr(item,fileUrl,invoiceType,fileName) {
      // 所需信息 ， 相关key，多个|分割

    // 购买方 BuyerName|Buyer|
    // 购买方纳税人识别号 BuyerTaxID
    // 发票名称 Title
    // 发票号码 Number|ReceiptNumber
    // 开票日期 Date
    // 价税合计 Total|Fare
    // 税额 Tax|TaxAmount
    // 税率 TaxRate
    // 备注 Remark
    // 销售方 Seller
    // 销售方纳税人识别号 SellerTaxID
    // 开票人 Issuer
    // 复核人 Reviewer
    // 发票消费类型|业务类型标志|种类  Kind|ServiceTypeLabel|Category
      // 税前金额 PretaxAmount
    const subs = item.VatInvoiceItemInfos || item.VatElectronicItems || item.GeneralMachineItems || item.Items
      const obj = {}
      obj.Buyer = item.BuyerName || item.Buyer
      obj.BuyerTaxID = item.BuyerTaxID
      obj.Title = item.Title
      obj.Number = item.Number || item.ReceiptNumber
      obj.Date = item.Date
      obj.Total = item.Total || item.Fare
      obj.Tax = item.Tax || item.TaxAmount
      obj.TaxRate = item.TaxRate || (subs && subs.map(i=>i.TaxRate||'').join(','))
      obj.Remark = item.Remark
      obj.Seller = item.Seller
      obj.SellerTaxID = item.SellerTaxID
      obj.Issuer = item.Issuer
      obj.Reviewer = item.Reviewer
      obj.Kind = item.Kind || item.ServiceTypeLabel || item.Category
      obj.PretaxAmount = item.PretaxAmount
      obj.FileUrl = fileUrl
      obj.InvoiceType= invoiceType
      // Reimbursement
      if(this.isChaiLvBX){
        console.log('939',fileName,item.Name)
        const iname = item['TypeOfVoucher'] + (item['VatElectronicItems'] && item['VatElectronicItems'].map(i=>i.Name).join(',') || '') + (subs && subs.map(i=>i.Name).join(','))

        if( fileName.indexOf('火车')>-1 || iname.indexOf('火车')>-1 || iname.indexOf('铁路')>-1 ){
          obj.costType = 1
        }
       else if(fileName.indexOf('机票')>-1  || iname.indexOf('机票')>-1){
          obj.costType = 2
        }
        else if(fileName.indexOf('船票')>-1  || iname.indexOf('船票')>-1){
          obj.costType = 3
        }
        else if(fileName.indexOf('交通')>-1  || iname.indexOf('交通')>-1 || fileName.indexOf('打车')>-1  || iname.indexOf('打车')>-1 || iname.indexOf('客运')>-1){
          obj.costType = 4
        }
        else if(fileName.indexOf('住宿')>-1  || iname.indexOf('住宿')>-1){
          obj.costType = 5
        }
        else if(fileName.indexOf('车辆')>-1  || iname.indexOf('车辆')>-1 ){
          obj.costType = 6
        }
        else if(fileName.indexOf('补助')>-1  || iname.indexOf('补助')>-1){
          obj.costType = 7
        } else if(fileName.indexOf('餐饮')>-1  || iname.indexOf('餐饮')>-1 || fileName.indexOf('餐费')>-1  || iname.indexOf('餐费')>-1){
          obj.costType = 10
        }else{
          obj.costType = 8
        }
      }
      // console.log(obj,item,subs)
      // // 中文
      // const chinese = {
      //   Buyer: '购买方',
      //   BuyerTaxID: '购买方税号',
      //   Title: '发票抬头',
      //   Number: '发票号码',
      //   Date: '开票日期',
      //   Total: '发票金额',
      //   Tax: '税额',
      //   TaxRate: '税率',
      //   Remark: '备注',
      //   Seller: '销售方',
      //   SellerTaxID: '销售方税号',
      //   Issuer: '开票人',
      //   Reviewer: '复核人',
      //   Kind: '发票种类',
      //   PretaxAmount: '税前金额'
      // }
      // // 打印
      // const print = (o) => {
      //   const keys = Object.keys(o)
      //   keys.forEach(key => {
      //     console.log(chinese[key] + ':' + o[key])
      //   })
      // }
      // print(obj)
      return obj
    },
    ppResNe(res, fileUrl,fileName) {
      const data = []
      // this.invoiceData = []
      const day = dayjs().format('YYYY-MM-DD')
      if (res.MixedInvoiceItems) {
        res.MixedInvoiceItems.forEach(item => {
          const obj = {}
          const invoice = this.invoiceOcr(item.SingleInvoiceInfos[item.SubType],fileUrl,item.SubType,fileName)
          // if (invoice.Number) {
          //   this.invoiceData.push(invoice)
          // }
          // type item.TypeDescription
          // 铁路电子客票 9%
          const v9 = ['TrainTicket','ElectronicTrainTicketFull','ElectronicFlightTicketFull','AirTransport']
          // if (item.SingleInvoiceInfos.TrainTicket) {
          // obj.type = 'TrainTicket'
          if (v9.includes(item.SubType)) {
            obj.type = item.SubType
            obj.typeName = item.TypeDescription
            obj.author = this.obj.name
            obj.subTypeName = item.SubTypeDescription
            obj.total = item.SingleInvoiceInfos[item.SubType].Total || item.SingleInvoiceInfos[item.SubType].Fare
            obj.sum = obj.total
            obj.documentDate = day
            obj.invoiceIssuer = item.SingleInvoiceInfos[item.SubType].Buyer
            obj.invoiceQuantity = item.Page
            obj.totalCn = item.SingleInvoiceInfos[item.SubType].TotalCn
            obj.userId = item.SingleInvoiceInfos[item.SubType].UserId
            obj.userName = item.SingleInvoiceInfos[item.SubType].UserName || item.SingleInvoiceInfos[item.SubType].Name || ''
            obj.code = item.SingleInvoiceInfos[item.SubType].Number
            invoice.costType = 1
            if("AirTransport" == item.SubType){
              invoice.costType = 2
            }
            // 0.09
            if (obj.userName && this.isChaiLvBX) {
              if (item.SingleInvoiceInfos[item.SubType].Tax) {
                obj.tax = item.SingleInvoiceInfos[item.SubType].Tax
                obj.taxRate = item.SingleInvoiceInfos[item.SubType].TaxRate
                obj.pretaxAmount = currency(obj.total - obj.tax).value
              } else {
                obj.tax = this.shuiFax(obj.total, 0.09)
                obj.taxRate = '9%'
                obj.pretaxAmount = currency(obj.total - obj.tax).value
              }

            }

          }
          // 公路、水运客票 3%
          const v3 = ['TollInvoice', 'ShippingInvoice', 'BusInvoice', 'TaxiTicket']
          if (v3.includes(item.SubType)) {
            obj.type = item.SubType
            obj.typeName = item.TypeDescription
            obj.subTypeName = item.SubTypeDescription
            obj.total = item.SingleInvoiceInfos[item.SubType].Total
            obj.sum = obj.total
            obj.author = this.obj.name
            obj.documentDate = day
            obj.invoiceQuantity = item.Page
            obj.invoiceIssuer = item.SingleInvoiceInfos[item.SubType].Buyer
            obj.totalCn = item.SingleInvoiceInfos[item.SubType].TotalCn
            obj.userName = item.SingleInvoiceInfos[item.SubType].UserName
            obj.userId = item.SingleInvoiceInfos[item.SubType].UserId
            obj.code = item.SingleInvoiceInfos[item.SubType].Number
            invoice.costType = 4
            if (obj.userName && this.isChaiLvBX) {
              if (item.SingleInvoiceInfos[item.SubType].Tax) {
                obj.tax = item.SingleInvoiceInfos[item.SubType].Tax
                obj.taxRate = item.SingleInvoiceInfos[item.SubType].TaxRate
                obj.pretaxAmount = currency(obj.total - obj.tax).value
              } else {
                obj.tax = this.shuiFax(obj.total, 0.03)
                obj.taxRate = '3%'
                obj.pretaxAmount = currency(obj.total - obj.tax).value
              }
            }
          }
          const vs = ['VatSpecialInvoice', 'VatCommonInvoice', 'VatElectronicCommonInvoice', 'VatElectronicSpecialInvoice', 'VatElectronicInvoiceToll'
            ,'VatElectronicSpecialInvoiceFull', 'VatElectronicInvoiceFull', 'VatInvoiceRoll'
            ,'MachinePrintedInvoice','NonTaxIncomeGeneralBill','QuotaInvoice'
          ]
          const zp = ['VatSpecialInvoice', 'VatElectronicSpecialInvoice', 'VatElectronicSpecialInvoiceFull']
          const pp = ['VatCommonInvoice','VatElectronicCommonInvoice','VatElectronicInvoiceToll','VatElectronicInvoiceFull']
          if (vs.includes(item.SubType)) {
            obj.type = item.SubType
            obj.typeName = item.TypeDescription
            obj.subTypeName = item.SubTypeDescription
            obj.userId = item.SingleInvoiceInfos[item.SubType].UserId
            obj.userName = item.SingleInvoiceInfos[item.SubType].UserName || item.SingleInvoiceInfos[item.SubType].Name || ''
            obj.total = item.SingleInvoiceInfos[item.SubType].Total
            obj.sum = obj.total
            obj.author = this.obj.name
            if (zp.includes(item.SubType)) {
              obj.invoiceType = '专票'
            }
            if (pp.includes(item.SubType)) {
              obj.invoiceType = '普票'
            }
            obj.documentDate = day
            obj.invoiceQuantity = item.Page
            obj.invoiceIssuer = item.SingleInvoiceInfos[item.SubType].Buyer
            obj.totalCn = item.SingleInvoiceInfos[item.SubType].TotalCn
            if ('专票' == obj.invoiceType || obj.userName) {
              obj.tax = item.SingleInvoiceInfos[item.SubType].Tax
              const arr = item.SingleInvoiceInfos[item.SubType].VatInvoiceItemInfos || item.SingleInvoiceInfos[item.SubType].VatElectronicItems || item.SingleInvoiceInfos[item.SubType].GeneralMachineItems || []
              const uniqueTaxRates = [...new Set(arr.map(obj => obj.TaxRate))];
              obj.taxRate = uniqueTaxRates.join(',')
              if (!obj.taxRate && item.SingleInvoiceInfos[item.SubType].TaxRate) {
                obj.taxRate = item.SingleInvoiceInfos[item.SubType].TaxRate
              }
              obj.pretaxAmount = item.SingleInvoiceInfos[item.SubType].PretaxAmount
            }


            obj.code = item.SingleInvoiceInfos[item.SubType].Code || item.SingleInvoiceInfos[item.SubType].Number
          }
          if (Object.keys(obj).length > 0) {
            if (this.isYeWuZhaoDai) {
              obj.costType = '业务招待费'
              if (obj.tax) {
                obj.taxOut = obj.tax
              }
            }
            obj.fileUrl = fileUrl
            // data.push(obj)
            if (invoice && invoice.Number) {
              data.push(Object.assign({},obj,invoice))
            } else {
              data.push(obj)
            }
          }
        })
      }
      return data
    },
    loadShui(bxObj) {
      // this.invoiceData = invoiceObj
      this.totalShuiList = bxObj
      this.listq[`data${this.cruddataindex}`] = bxObj
      this.listq = Object.assign({}, this.listq)
      this.didxKey = `data${this.cruddataindex}`
      this.sumcrud()
      // this.listq[`data${this.cruddataindex}`].push(...bxObj)
      // this.$set(this.listq, `data${this.cruddataindex}`, bxObj)
    },
    downUrl(type){
      if (type == 'gz') {
        // GZ_EXCEL_URL
        window.open(GZ_EXCEL_URL)
      }
    },
    beforeUpload(file) {
      // 不能上传压缩包
      if (file.name.indexOf('.zip') > -1 || file.name.indexOf('.rar') > -1 || file.name.indexOf('.7z') > -1) {
        this.$message.error('不能上传压缩包')
        return false
      }
      // console.log('beforeUpload', file, this.groupindex)
      if (!this.tmpFileMap) {
        this.tmpFileMap = {}
      }
      this.tmpFileMap[file.uid] = this.groupindex
      this.beforeUploadStatus++
    },
    handleError() {
      this.handleUpImgEnd()
    },
    handleUpImgEnd() {
      this.beforeUploadStatus--
    },
    sortFun() {
      if (this.$route.query.relyProcessId) {
        this.getOldData(this.$route.query.relyProcessId).then(() => {
          this.doInit(false)
        })
        return
      }
      this.doInit()
    },
    getOldData(oldprocessid) {
      if (!oldprocessid) {
        return Promise.resolve()
      }
      return new Promise((resolve, reject) => {
        processVal(oldprocessid).then(res => {
          if (res && res.data && Object.keys(res.data).length > 0 &&
          res.data.formValue &&
          res.data.formValue.fromvalue
          ) {
            this.obj = res.data.formValue.fromvalue || {}
            this.ccs.peoples = res.data.ccs || []
            // this.listq =  res.data.formValue.fromvalue.cruddata || {}
            this.listq = JSON.parse(JSON.stringify(res.data.formValue.fromvalue.cruddata || []))
            this.totalShuiList = JSON.parse(JSON.stringify(res.data.formValue.fromvalue.totalShuiList || []))
            // this.invoiceData = JSON.parse(JSON.stringify(res.data.formValue.fromvalue.invoiceData || []))
            // 复杂表格显示，根据index 显示，grouplist名称
            this.groupCrubTypeList = res.data.formValue.fromvalue.grouptypelist
            this.groupCrubSize = res.data.formValue.fromvalue.groupsize
            this.contractimageList = this.obj.contractimageList
            this.fileListg = this.obj.fileListg
            this.fileList = this.obj.fileList
            resolve()
          // this.showGroupCrub(this.listq)
          }
        }).catch(() => {
          resolve()
        })
      })
    },

    CompanyNameFmt2(id) {
      for (var a = 0; a < this.companylist.length; a++) {
        var item = this.companylist[a]
        if (item.id === id) {
          return item.label
        }
      }
      return ''
    },
    crubtypeFmt(id) {
      if (!this.crubtype) {
        return '费用明细'
      }
      for (var a = 0; a < this.crubtype.length; a++) {
        var item = this.crubtype[a]
        if (item.option === id) {
          return item.name
        }
      }
      return '费用明细'
    },
    checkoutHistory() {
      this.contractHistroy = !this.contractHistroy
    },
    addsize(item) {
      this.crubtype = item
      this.groupCrubSize += 1
      this.groupCrubTypeList.push(this.curbtypename)

      // console.log(this.groupCrubTypeList)
    },
    delcrud(a, b) {
      this.$confirm('是否删除该条明细？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // console.log(b)
        // console.log(this.groupCrubTypeList)
        this.$set(this.groupCrubTypeList, b, 'noshow')
        this.listq[`data${a}-${b}`] = []
        this.fileListg[b] = []
        this.loadGzSum(null)
        // this.obj.sumNum = 1
        this.sumcrud()
      })
    },
    handleRemove(file, fileList) {
    },
    savefileList(response, file, fileList) {
      // console.log('success-savefileList', response, file, fileList)
      this.fileList = fileList
      this.handleUpImgEnd()
      if (!response || response.resultCode != 0 || !response.url) {
        // if (!this.fileListg[this.groupindex]) {
        //   this.fileListg[this.groupindex] = []
        // }
        // this.fileListg[this.groupindex].push(file)
        this.fileList.pop()
        this.$message.error('上传失败，请重新上传')
      }
      this.loadChaiLv(file)
      this.loadGzSum(this.fileList)
    },
    savefileListconrtact(response, file, fileList) {
      this.contractimageList = fileList
      this.handleUpImgEnd()
      if (!response || response.resultCode != 0 || !response.url) {
        // if (!this.fileListg[this.groupindex]) {
        //   this.fileListg[this.groupindex] = []
        // }
        // this.fileListg[this.groupindex].push(file)
        this.contractimageList.pop()
        this.$message.error('上传失败，请重新上传')
      }
      // console.log(this.contractimageList)
    },
    savefileListg(response, file, fileList) {
      // console.log('success-savefileListg', file)
      // console.log('success-savefileListg', response, file, fileList)
      const groupindex = this.tmpFileMap[file.uid]
      this.fileListg[groupindex] = fileList
      this.handleUpImgEnd()
      if (!response || response.resultCode != 0 || !response.url) {
        // if (!this.fileListg[groupindex]) {
        //   this.fileListg[groupindex] = []
        // }
        // this.fileListg[groupindex].push(file)
        this.fileListg[groupindex].pop()
        this.$message.error('上传失败，请重新上传')
      }
      this.loadGzSum(this.fileListg[groupindex])
    },
    downloadFileByUrl(url, name) {
      if (name.indexOf('.pdf') !== -1) {
        window.openWindow(url, name, 800, 600)
        return
      }
      var that = this
      this.getBlob(url, function(blob) {
        that.saveAs(blob, name)
      })
    },
    getBlob(url, cb) {
      var xhr = new XMLHttpRequest()
      xhr.open('GET', url, true)
      xhr.responseType = 'blob'
      xhr.onload = function() {
        if (xhr.status === 200) {
          cb(xhr.response)
        }
      }
      xhr.send()
    },
    saveAs(blob, filename) {
      if (window.navigator.msSaveOrOpenBlob) {
        navigator.msSaveBlob(blob, filename)
      } else {
        var link = document.createElement('a')
        var body = document.querySelector('body')

        link.href = window.URL.createObjectURL(blob)
        link.download = filename

        // fix Firefox
        link.style.display = 'none'
        body.appendChild(link)

        link.click()
        body.removeChild(link)

        window.URL.revokeObjectURL(link.href)
      }
    },
    handlePreview(file) {
      // let { href } = this.$router.resolve({ path: file.response.url })
      if (file.response.url.indexOf('.png') !== -1 || file.response.url.indexOf('.jpeg') !== -1 || file.response.url.indexOf('.jpg') !== -1) {
        this.dialogImageUrl = file.response.url
        this.dialogVisible = true
      } else {
        this.downloadFileByUrl(file.response.url, file.name)
      }
    },

    handlePreviewg(file) {
      // let { href } = this.$router.resolve({ path: file.response.url })
      if (file.response.url.indexOf('.png') !== -1 || file.response.url.indexOf('.jpeg') !== -1 || file.response.url.indexOf('.jpg') !== -1) {
        this.dialogImageUrlg = file.response.url
        this.dialogVisibleg = true
      } else {
        this.downloadFileByUrl(file.response.url, file.name)
      }
    },
    handleExceed(files, fileList) {
      this.$message.warning(`当前限制选择 3 个文件，本次选择了 ${files.length} 个文件，共选择了 ${files.length + fileList.length} 个文件`)
    },
    beforeRemove(file, fileList) {
      // biome-ignore lint/complexity/useOptionalChain: <explanation>
      if(file && file.response && file.response.url){
        return this.$confirm(`确定移除 ${file.name}？`)
      }
      return true
    },
    checkoutindex(index) {
      // console.log('checkoutindex - 680', index)
      this.cruddataindex = index
    },
    checkoutgroupindex(index) {
      this.groupindex = index
    },
    emptytChange() {
      this.$message.success('清空方法回调')
    },

    handleRowSave(row, done, loading) {
      // console.log('handleRowSave-690')
      if (!this.listq[`data${this.cruddataindex}`]) {
        this.listq[`data${this.cruddataindex}`] = []
      }
      this.listq[`data${this.cruddataindex}`].splice(0, 0, row)
      this.sumcrud()
      done() // 关闭表单
      loading() // 按钮停止加载
    },
     rowEditCrud(row, index,crud) {
      if (this.$refs[crud][0]) {
        this.$refs[crud][0].rowEdit(row, index);
      }
    },
    rowDelCrud(row, index,crud) {
      if (this.$refs[crud][0]) {
        this.$refs[crud][0].rowDel(row,index);
      }
    },

    // 修改按钮
    handleRowUpdate(row, index, done, loading) {
      // console.log('handleRowUpdate-702')
      this.listq[`data${this.cruddataindex}`].splice(index, 1, row)
      this.sumcrud()
      done() // 关闭表单
      loading() // 按钮停止加载
    },
    // 删除按钮
    rowDel(row, index) {
      // console.log('rowDel-710')
      this.$confirm('是否删除该条信息？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.listq[`data${this.cruddataindex}`].splice(index, 1)
        this.sumcrud()
      })
    },

    handleRowSaveg(row, done, loading) {
      // console.log('handleRowSaveg-722')
      if (!this.listq[`data${this.cruddataindex}-${this.groupindex}`]) {
        this.listq[`data${this.cruddataindex}-${this.groupindex}`] = []
      }
      this.listq[`data${this.cruddataindex}-${this.groupindex}`].splice(0, 0, row)
      this.sumcrud()
      done() // 关闭表单
      loading() // 按钮停止加载
    },

    // 修改按钮
    handleRowUpdateg(row, index, done, loading) {
      // console.log('handleRowUpdateg-734',this.listq,`data${this.cruddataindex}-${this.groupindex}`,index,row)
      this.listq[`data${this.cruddataindex}-${this.groupindex}`].splice(index, 1, row)
      this.sumcrud()
      done() // 关闭表单
      loading() // 按钮停止加载
    },
    // 删除按钮
    rowDelg(row, index) {
      // console.log('rowDelg-742')
      this.$confirm('是否删除该条信息？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.listq[`data${this.cruddataindex}-${this.groupindex}`].splice(index, 1)
        this.sumcrud()
      })
    },
    sumcrud() {
      this.obj.sumNum1 = 0
      this.obj.sumNum2 = 0
      this.obj.sumNum = 0

      let totalSum = 0
      let totalSum1 = 0
      let totalSum2 = 0
      for (const key of Object.keys(this.listq)) {
        const data = this.listq[key]
        if (!data || (Array.isArray(data) && data.length < 1)) {
          continue
        }
        // console.log('sum',data)
        // 处理数字精度问题 [sungf 2022-09-23]
        totalSum = currency(totalSum).add(currency(data.map(item => !item.sum ? 0 : Number(item.sum)).reduce((x, y) => currency(x).add(currency(y)).value, 0))).value
        totalSum1 = currency(totalSum1).add(currency(data.map(item => !item.sum1 ? 0 : Number(item.sum1)).reduce((x, y) => currency(x).add(currency(y)).value, 0))).value
        totalSum2 = currency(totalSum2).add(currency(data.map(item => !item.sum2 ? 0 : Number(item.sum2)).reduce((x, y) => currency(x).add(currency(y)).value, 0))).value

        // totalSum += data.map(item => !item.sum ? 0 : Number(item.sum))
        //   .reduce((x, y) => x + y)
        // totalSum1 += data.map(item => !item.sum1 ? 0 : Number(item.sum1))
        //   .reduce((x, y) => x + y)
        // totalSum2 += data.map(item => !item.sum2 ? 0 : Number(item.sum2))
        //   .reduce((x, y) => x + y)
      }
      // console.log('tsumall',totalSum)
      this.obj.sumNum1 = totalSum1
      this.obj.sumNum2 = totalSum2
      this.obj.sumNum = totalSum
      this.NumberToChinese(Number(totalSum))
      // for (var index = 0; index < 10; index++) {
      //   if (!this.listq[`data${index}`]) {
      //     continue
      //   }
      //   console.log(index)
      //   for (var a = 0; a < this.listq[`data${index}`].length; a++) {
      //     const item = this.listq[`data${index}`][a]
      //     if (item.sum) {
      //       this.obj.sumNum = Number(item.sum) + Number(this.obj.sumNum)
      //     }
      //     if (item.sum1) {
      //       this.obj.sumNum1 = Number(item.sum1) + Number(this.obj.sumNum1)
      //     }
      //     if (item.sum2) {
      //       this.obj.sumNum2 = Number(item.sum2) + Number(this.obj.sumNum2)
      //     }
      //   }
      // }
      this.checkAcceptanceCd()
    },
    NumberToChinese(money) {
      var cnNums = new Array('零', '壹', '贰', '叁', '肆', '伍', '陆',
        '柒', '捌', '玖')
      // 基本单位
      var cnIntRadice = new Array('', '拾', '佰', '仟')
      // 对应整数部分扩展单位
      var cnIntUnits = new Array('', '万', '亿', '兆')
      // 对应小数部分单位
      var cnDecUnits = new Array('角', '分', '毫', '厘')
      // 整数金额时后面跟的字符
      var cnInteger = '整'
      // 整型完以后的单位
      var cnIntLast = '元'
      // 最大处理的数字
      var maxNum = 999999999999999.9999
      // 金额整数部分
      var integerNum
      // 金额小数部分
      var decimalNum
      // 输出的中文金额字符串
      var chineseStr = ''
      // 分离金额后用的数组，预定义
      var parts
      if (money == '') {
        return ''
      }
      money = parseFloat(money)
      if (money >= maxNum) {
        // 超出最大处理数字
        return ''
      }
      if (money == 0) {
        chineseStr = cnNums[0] + cnIntLast + cnInteger
        return chineseStr
      }
      // 转换为字符串
      money = money.toString()
      if (money.indexOf('.') == -1) {
        integerNum = money
        decimalNum = ''
      } else {
        parts = money.split('.')
        integerNum = parts[0]
        decimalNum = parts[1].substr(0, 4)
      }
      // 获取整型部分转换
      if (parseInt(integerNum, 10) > 0) {
        var zeroCount = 0
        var IntLen = integerNum.length
        for (var i = 0; i < IntLen; i++) {
          var n = integerNum.substr(i, 1)
          var p = IntLen - i - 1
          var q = p / 4
          var m = p % 4
          if (n == '0') {
            zeroCount++
          } else {
            if (zeroCount > 0) {
              chineseStr += cnNums[0]
            }
            // 归零
            zeroCount = 0
            chineseStr += cnNums[parseInt(n)] +
              cnIntRadice[m]
          }
          if (m == 0 && zeroCount < 4) {
            chineseStr += cnIntUnits[q]
          }
        }
        chineseStr += cnIntLast
      }
      // 小数部分
      if (decimalNum != '') {
        var decLen = decimalNum.length
        for (var i = 0; i < decLen; i++) {
          var n = decimalNum.substr(i, 1)
          if (n != '0') {
            chineseStr += cnNums[Number(n)] + cnDecUnits[i]
          }
        }
      }
      if (chineseStr == '') {
        chineseStr += cnNums[0] + cnIntLast + cnInteger
      } else if (decimalNum == '') {
        chineseStr += cnInteger
      }

      this.obj.sumChinese = chineseStr
    },
    fmtRefFrom(tepFromSchema) {
      // console.log('ref',this.obj)
      for (var a = 0; a < tepFromSchema.length; a++) {
        var item = tepFromSchema[a]
        if (item.key === 'form' || item.key === 'contract') {
          for (var b = 0; b < item.option.column.length; b++) {
            var items = item.option.column[b]
            if (this.$route.query.title.indexOf('鹭盛船舶业务付款') !== -1 || this.$route.query.title.indexOf('对外付款申请') !== -1 || this.$route.query.title.indexOf('经营付款(油品)') !== -1) {
              if (items.label === '开户行') {
                items.dicUrl = store.getters.baseApi + '/api/sysExternalAccount/getAllBank?id=' + this.obj.opositeUnit
              }
              if (items.label === '账户号') {
                items.dicUrl = store.getters.baseApi + '/api/sysExternalAccount/getAllBankNum?id=' + this.obj.opositeUnit + '&bankid=' + this.obj.kaihu
              }
            } else if (this.$route.query.title === '往来款付款申请'|| this.$route.query.title === '集团资金调剂付款申请'||this.$route.query.title === '银行账户资金划转申请') {
              // console.log(item)
              if (item.showT === '付款账户信息') {
                if (items.label === '开户行') {
                  items.dicUrl = store.getters.baseApi + '/api/sysAccount/showBank?id=' + this.obj.paymentBank
                }
                if (items.label === '账户号') {
                  items.dicUrl = store.getters.baseApi + '/api/sysAccount/showNum?id=' + this.obj.paymentAaccountNum
                }
              } else if (item.showT === '收款账户信息') {
                if (items.label === '开户行') {
                  items.dicUrl = store.getters.baseApi + '/api/sysAccount/showBank?id=' + this.obj.receiveBank
                }
                if (items.label === '账户号') {
                  items.dicUrl = store.getters.baseApi + '/api/sysAccount/showNum?id=' + this.obj.receiveAccountNum
                }
              }
            }
          }
        }
        // else if (item.key === 'crud' || item.key === 'group-crud') {
        //   item.option.addBtn = false
        //   item.option.menu = false
        //   for (var i = 0; i < 10; i++) {
        //     if (!item[`option${i}`]) {
        //       continue
        //     }
        //     item[`option${i}`].addBtn = false
        //     item[`option${i}`].menu = false
        //   }
        // }
      }
    },
    doInit(ltq = true) {
      if (!this.processCode) {
        this.$message({ showClose: true, message: '请传递流程Code', type: 'error' })
        return
      }
      if (!ltq) {
        this.loadGzSum(this.fileListg[this.groupindex])
      }

      this.loadingComponent = true
      processApi.getProcess(this.processCode).then(res => {
        // 按业务加载
        this.formSchema = JSON.parse(res.data.formSchema)
        // 判是否重提交t

        if (this.$route.query.relyProcessId) {
          this.fmtRefFrom(this.formSchema)
        }
        for (var a = 0; a < this.formSchema.length; a++) {
          if (ltq) {
            this.listq[`data${a}`] = []
          } else {
            if(this.formSchema[a].key=='group-crud' || this.formSchema[a].key=='crud'){
              this.cruddataindex = a
            }
          }
        }
        if (res.data.sp) {
          this.activities.peoples = res.data.sp
        }
        if (res.data.cc) {
          this.ccs.peoples = res.data.cc
        }
        if (res.data.formSchema) {
          this.option = JSON.parse(res.data.formSchema)
        }
      }).catch(e => {

      }).finally(() => {
        this.loadingComponent = false
      })
    },
    handleClose(done) {
      this.$confirm('确认关闭？')
        .then(_ => {
          done()
        })
        .catch(_ => {
        })
    },
    cancelForm() {
      this.$confirm('确认关闭？')
        .then(_ => {
          this.drawer = false
        })
        .catch(_ => {
        })
    },

    saveContractOrNot() {
      console.log(this.listq)
      console.log('file', this.fileListg)
      var zzz = 0
      for (const key of Object.keys(this.listq)) {
        const data = this.listq[key]
        if (data.length > 0) {
          zzz = 1
          break
        }
      }
      if (zzz === 0 && this.$route.query.title.indexOf('合同') === -1) {
        this.$message.warning('请新增费用')
        return
      }
      // 社保 上传工资模版
      // if ('ExternalPaymentApplication' == this.processCode && '是' === this.obj.shebao && this.totalGzSum==0) {
      //   this.$message.warning('请上传工资表')
      //   return
      // }
      // // sumNum
      // if ('ExternalPaymentApplication' == this.processCode && '是' === this.obj.shebao && this.obj.sumNum != this.totalGzSum) {
      //   this.$message.warning('工资表总金额与合计金额不一致')
      //   return
      // }
      // 差旅报销
      if ('TravelReimbursement' == this.processCode || 'Reimbursement' == this.processCode) {
        if (this.totalShuiLoading) {
          this.$message.warning('请等待税金计算完成')
          return
        }
        // this.loadChaiLv()
        // '有' == this.obj.invoice &&
        if (this.totalShuiList) {
          if (this.didxKey) {
            this.listq[`${this.didxKey}`] = this.sanJsonList(this.listq[`${this.didxKey}`])
            this.obj.taxAmount = this.sumAdd(this.listq[`${this.didxKey}`], 'tax')
            this.obj.totalShuiList = this.listq[`${this.didxKey}`]
          } else {
            this.totalShuiList = this.sanJsonList(this.totalShuiList)
            this.obj.taxAmount = this.sumAdd(this.totalShuiList, 'tax')
            this.obj.totalShuiList = this.totalShuiList
          }
          // if (this.invoiceData && this.invoiceData.length) {
          //   this.obj.invoiceData = this.invoiceData
          // }
          // this.obj.amountExcludingTax = currency(this.obj.sumNum).subtract(this.obj.taxAmount).value
        } else {
          // this.obj.amountExcludingTax = this.obj.sumNum
          this.obj.taxAmount = 0
        }
      }
      // 承兑
      if ('AcceptanceDiscount' == this.processCode && this.isCheckAcceptanceByCd()) {

        return
      }
      const promiseArr = []
      for (var a = 0; a < this.$refs.form.length; a++) {
        var item = this.$refs.form[a]
        promiseArr.push(new Promise((resolve, reject) => {
          item.validate((valid) => {
            // console.log(arguments)
            if (valid) {
              resolve()
            } else {
              reject('err')
            }
          })
        }))
      }
      Promise.all(promiseArr).then(res => {
        console.log('校验通过', res)
        if (this.contractHistroy) {
          var str = JSON.stringify(this.contractimageList)
          saveContract(this.obj.contractname, 0, this.obj.company, this.obj.opositeUnit, str).then(res => {
            this.obj.contractid = res.id.toString()
            this.submitForm()
          })
        } else {
          this.submitForm()
        }
      }).catch(err => {
        console.log('校验失败', err)
      }).finally(() => {
        console.log('finally...')
      })
    },
    sanJsonList(list) {
      for (let i = 0; i < list.length; i++) {
        list[i] = this.sanJson(list[i])
      }
      return list
    },
    sanJson(json) {
      const sanitizedJson = {};
      for (const key in json) {
        if (json.hasOwnProperty(key)) {
          if (!key.startsWith('$')) {
            sanitizedJson[key] = json[key];
          }
        }
      }
      return sanitizedJson
    },
    submit(form, done) {
      this.$message.success(JSON.stringify(form))
      done()
    },
    error(err) {
      this.$message.success('请查看控制台')
      console.log(err)
    },
    submitForm() {
      this.briefContent = '公司：' + this.CompanyNameFmt2(this.obj.company) + '│申请人：' + this.obj.name
      if (this.obj.selShip) {
        this.briefContent = this.briefContent + '│船舶：' + this.obj.selShip
      }
      if (this.obj.voyage) {
        this.briefContent = this.briefContent + '│航次：' + this.obj.voyage
      }
      if (this.obj.sumNum) {
        this.briefContent = this.briefContent + '│金额：' + this.obj.sumNum
      }
      // 增加“事由”
      let reason = this.obj.reason
      if (!reason || reason === '') {
        for (const k in this.listq) {
          if (!(this.listq[k] instanceof Array)) {
            continue
          }
          for (const item of this.listq[k]) {
            if (item.purpose && item.reason !== '') {
              if (reason) {
                reason = reason + '、' + item.purpose
              } else {
                reason = item.purpose
              }
              continue
            }
            if (item.remake && item.remake !== '') {
              if (reason) {
                reason = reason + '、' + item.remake
              } else {
                reason = item.remake
              }
              continue
            }
            if (item.reason && item.reason !== '') {
              if (reason) {
                reason = reason + '、' + item.reason
              } else {
                reason = item.reason
              }
              continue
            }
          }
        }
      }
      if (!reason) {
        reason = ''
      }
      if (reason) {
        this.briefContent = this.briefContent + '│事由：' + reason
      }
      // 增加“合同备注”
      if (this.obj.contractRemark) {
        this.briefContent = this.briefContent + '│备注：' + this.obj.contractRemark
      }
      // 增加processJian 区分流程
      if (this.$route.query.title) {
        this.obj.flowName = this.$route.query.title
      }
      this.obj.processJian = this.$route.query.processJian || ''
      this.obj.contractimageList = this.contractimageList
      this.obj.cruddata = this.listq
      this.obj.fileList = this.fileList
      this.obj.fileListg = this.fileListg
      this.obj.groupsize = this.groupCrubSize
      this.obj.grouptypelist = this.groupCrubTypeList
      this.globalParams.params3 = this.obj.sumNum
      this.globalParams.params4 = this.obj.company
      if (this.obj.kaihu) {
        this.globalParams.params9 = this.obj.kaihu
      } else {
        this.globalParams.params9 = this.obj.name
      }
      this.globalParams.params8 = this.obj.Numbers
      this.globalParams.params10 = this.obj.processJian
      // 提交之前提供给调用者的回调，可以用来自定义校验
      let flag = true
      this.$emit('onBeforeSubmit', (res) => {
        if (!res) {
          flag = false
        }
      })
      if (!flag) return

      this.loading = true

      // 表单值
      const params = {
        value: this.obj
      }
      // 全局参数
      if (this.globalParams) {
        params.global = this.globalParams
      }
      // 抄送人
      if (this.ccs.peoples) {
        // console.log(this.ccs.peoples)
        params.ccs = this.ccs.peoples.map(item => item.id)
      }
      // 抄送角色
      if (this.ccs.roles) {
        params.roles = this.ccs.roles.map(item => item.id)
      }
      // 摘要
      if (this.briefContent) {
        params.briefContent = this.briefContent
      }
      // console.log('params========', params)
      processApi.startProcess(this.processCode, params,this.$route.query.title||'').then(res => {
        this.$message.success('提交成功')
        if (this.processCode === 'CurrentAccountApplication') {
          console.log(res.data)
        }
        this.$router.back()
      }).catch(e => {
        this.$message.error(res.data.resultMsg || 'Error')
      }).finally(() => {
        this.loading = false
      })
    },

    addCc() {
      this.$refs.depart.dialog = true
      this.$refs.depart.doInit()
    },
    childCheckedCc(data) {
      if (data.cc) {
        this.ccs.peoples = this.unique(this.ccs.peoples.concat(data.cc))
      }
      // if (data.role) {
      //   this.ccs.roles = this.unique(this.ccs.roles.concat(data.role))
      // }
    },
    // 对象数组去重
    unique(objArray) {
      const hash = {}
      objArray = objArray.reduce(function(item, next) {
        hash[next.name] ? '' : hash[next.name] = true && item.push(next)
        return item
      }, [])
      return objArray
    },
    removeCc(id) {
      this.ccs.peoples = this.ccs.peoples.filter(item => item.id !== id)
    },
    removeRole(id) {
      this.ccs.roles = this.ccs.roles.filter(item => item.id !== id)
    }
    // tip(){
    //   this.$message.success('自定义按钮');
    // }

  }

}
</script>
<style>
.section-grid-cls{
    display: grid;
    grid-template-columns: repeat(4,1fr);
    grid-gap: 10px;
  }
.section-grid-cls span{
    text-align: center;
    font-size: 14px;
  }
</style>
<style scoped>
  #drawerBody {
    padding: 10px;
  }

  #timelineBody > * {
    text-align: left !important;
  }

  .wh40 {
    width: 40px;
    height: 42px;
    padding: 0 !important;
    margin: 0 !important;
  }

  .timelineContent {
    display: flex;
    flex-wrap: wrap;
    justify-content: flex-end;
    align-items: center;
    align-content: space-around;
  }

  .diy-avatar {
    display: inline-block;
    box-sizing: border-box;
    text-align: center;
    color: #fff;
    background: #C0C4CC;
    width: 40px;
    height: 40px;
    line-height: 40px;
    font-size: 14px;
    border-radius: 4px;
    overflow: hidden;
    margin-right: 5px;
  }

</style>
