<template>
  <div class="app-container">
    <AccModel @updateAccModel="updateAccModel"></AccModel>
    <el-form ref="queryForm" :model="queryParams" :inline="true">
      <el-form-item label="收款类型">
        <div  style="position: relative;bottom: 6px;">
        <FcItem :list="fundsClassList" @clkItem="clkQuery" v-model="fundsQueryId" ></FcItem>
      </div>
        <!-- <SimplePanelSelection
          ref="costType"
          class="width"
          :all-data="typeList"
          mode-name="收款类型"
          key-str="code"
          val-str="value"
          components-width="600"
          @setVal="costTypeQuery"
        /> -->
      </el-form-item>
      <el-form-item label="账户类型">
        <div class="width">
          <el-tag
            v-for="(item,index) in paymentTypeList"
            :key="index"
            style="margin-right: 10px;cursor: pointer;"
            :type="queryParams.receiveType==item.code?'success':'info'"
            @click="receiveTypeQuery(item)"
          >{{ item.value }}</el-tag>
        </div>
      </el-form-item>
      <el-form-item label="收款单位">
        <!-- <el-input
          v-model="queryParams.receiveCompany"
          placeholder="收款单位"
          clearable
          size="small"
          style="width: 240px"
        /> -->
        <el-autocomplete v-model="queryParams.receiveCompany" placeholder="收款单位" value-key="label" style="width: 240px" clearable :fetch-suggestions="fetchSuggestions" @select="getBlankAccount()" @change="getBlankAccount()" />
      </el-form-item>
        <el-form-item label="收款银行">
        <el-select v-model="queryParams.accid" clearable filterable placeholder="请选择" @change="topage(1)" >
          <el-option
            v-for="item in accountBlankList"
            :key="item.id"
            :label="(item.bankName||item.accountName)+substrPulx(item.accountNumber||'')"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="付款客户">
        <el-input
          v-model="queryParams.customer"
          placeholder="付款客户"
          clearable
          size="small"
          style="width: 240px"
          @keyup.enter.native="topage(1)"
        />
      </el-form-item>
      <el-form-item label="收款时间">
        <el-date-picker
          v-model="queryParams.createTime"
          size="small"
          type="daterange"
          range-separator="至"
          value-format="yyyy-MM-dd"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          @change="topage(1)"
        />
      </el-form-item>
      <el-form-item label="核销状态">
        <el-tag
          v-for="(item,index) in applyStatusList"
          :key="index"
          style="margin-right: 10px;cursor: pointer;"
          :type="queryParams.receiveStatus==item.code?'success':'info'"
          @click="applyStatusQuery(item)"
        >{{ item.value }}</el-tag>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="topage(1)">搜索</el-button>
        <el-button type="primary" icon="el-icon-plus" size="mini" @click="addReceiveWater">新增</el-button>
      </el-form-item>
    </el-form>
    <vxe-toolbar
      ref="xToolbar"
      export
      custom
      print
      :buttons="toolbarButtons"
    />
    <vxe-table
      ref="xTable"
      v-loading="loading"
      class="el-table-info"
      :data="dataList"
      stripe
      size="small"
      border
      align="center"
      :print-config="{}"
      max-height="800"
      highlight-current-row
      :export-config="{'type':'xlsx', 'sheetName':'sheet1', 'filename': '收款台账', 'isColgroup': true, 'isMerge': true, 'useStyle': true}"
      resizable
    >
      <!-- <vxe-table-column title="#" align="center" type="index"/> -->
      <vxe-table-column title="发生时间" align="center" field="occurDate" :formatter="({row, column, cellValue, index}) => { return myFmtDateTime(cellValue,'MM/DD')}" />
        <!-- :formatter="({row, column, cellValue, index}) => { return costTypeFmt(row, column, cellValue, index)}" -->
      <vxe-table-column title="收款类型" align="center"
        field="costType"  >
        <template slot-scope="scope">
        <span v-if="scope.row.fundsClassName">{{ scope.row.fundsClassName }}</span>
        <span v-else>{{ costTypeFmt(null,null,scope.row.costType,null) }}</span>
        </template>
      </vxe-table-column>
      <vxe-table-column title="账户类型" align="center" field="receviceType" :formatter="({row, column, cellValue, index}) => { return receviceTypeFmt(row, column, cellValue, index)}" />
      <vxe-table-column title="收款金额（元）" align="center" field="balance" :formatter="({row, column, cellValue, index}) => { return NumFmt(row, column, cellValue, index)}" />
      <vxe-table-column title="收款单位" align="center" field="receviceCompanyName" :formatter="({row, column, cellValue, index}) => { return publicFmt(row, column, cellValue, index)}" />
      <vxe-table-column title="付款客户" align="center" field="customerName" :formatter="({row, column, cellValue, index}) => { return publicFmt(row, column, cellValue, index)}" />
      <vxe-table-column title="付款银行" align="center" field="paymentBank" :formatter="({row, column, cellValue, index}) => { return publicFmt(row, column, cellValue, index)}" />
      <vxe-table-column title="付款账户" align="center" field="paymentAccount" :formatter="({row, column, cellValue, index}) => { return publicFmt(row, column, cellValue, index)}" />
      <vxe-table-column title="核销状态" align="center" field="applyStatus" :formatter="({row, column, cellValue, index}) => { return cellValue==0?'未核销':'已核销'}" />
      <vxe-table-column title="凭证号" align="center" field="voucherNum" :formatter="({row, column, cellValue, index}) => { return publicFmt(row, column, cellValue, index)}" />
      <vxe-table-column title="流水类型" align="center" field="isAdvance" :formatter="({row, column, cellValue, index}) => { return cellValue==0?'收款':'预收款'}" />
      <vxe-table-column title="操作" align="center">
        <template slot-scope="scope">
          <el-button v-if="scope.row.applyStatus==0" type="text" @click="updateAddReceiveWater(scope.row)">修改</el-button>
          <el-button type="text" @click="openReceiveOrder(scope.row)">查看收款单</el-button>
          <el-button type="text" @click="openShowAccount(scope.row)">收款账户详情</el-button>
          <el-button type="text" @click="showPic(scope.row)">查看财务截图</el-button>
          <!-- <el-button v-if="scope.row.costType !=1 && scope.row.applyStatus == 1" type="text" @click="saveVoucherReceive(scope.row)">录入凭证号</el-button> -->
          <el-button v-if="receListData[scope.row.id] && receListData[scope.row.id].count==0"  :class="{btnCol:isCertModel=='2'}"  type="text" @click="createReceCertById(scope.row.id,receListData[scope.row.id].code,scope.row.receviceType)" >生成凭证</el-button>
          <el-button v-if="receListData[scope.row.id] && receListData[scope.row.id].count" :class="{btnCol:isCertModel=='2'}"  type="text" @click="showTranByCode(scope.row,receListData[scope.row.id].code)" >查看凭证</el-button>
          <el-button v-if="scope.row.costType ==1 && scope.row.applyStatus != 0 && scope.row.isAdvance == 0 && scope.row.receviceCompanyName !== '海南和盛'" type="text" @click="showDetail(scope.row)">核销详情</el-button>
          <el-button v-if="scope.row.costType ==1 && scope.row.applyStatus != 0 && scope.row.receviceCompanyName == '海南和盛'" type="text" @click="showBusinessDetail(scope.row)">核销详情</el-button>
        </template>
      </vxe-table-column>
    </vxe-table>
    <el-pagination
      background
      layout="prev, pager, next"
      :total="total"
      :page-size="queryParams.pageSize"
      :current-page="queryParams.pageNo"
      style="text-align: right;padding: 10px 0px;background-color: #fff"
      @current-change="eventPage"
    />

    <!--    <el-dialog-->
    <!--      class="xuanxiang2"-->
    <!--      title="收款单"-->
    <!--      :visible.sync="showOrderApply"-->
    <!--      width="60%"-->
    <!--      :before-close="ApplyOrderhandleClose"-->
    <!--      :close-on-click-modal=false>-->
    <!--      <ReceiveOrder ref="receiveOrder"></ReceiveOrder>-->
    <!--    </el-dialog>-->

    <el-dialog
      class="xuanxiang2"
      title="收款录入"
      :visible.sync="showApply"
      width="60%"
      :before-close="ApplyhandleClose"
      :close-on-click-modal="false"
    >
      <div>
        <div style="font-size: 16px;margin-bottom: 10px;">收款基本信息</div>
        <div style="display: flex;justify-content: left;align-items: center;">
          <div style="width: 100px;font-size: 14px;color: #6A6C70;">账户类型：</div>
          <div class="width">
            <el-tag
              v-for="(item,index) in paymentTypeList"
              :key="index"
              style="margin-right: 10px;cursor: pointer;"
              :type="switchComp.receiveType==item.code?'success':'info'"
              @click="receiveType(item)"
            >{{ item.value }}</el-tag>
          </div>
        </div>
        <!-- <div style="margin: 10px 0" />
        <div style="display: flex;justify-content: left;align-items: center;">
          <div style="width: 90px;font-size: 14px;color: #6A6C70;">收款类型：</div>
          <div class="width"><FcItem :list="fundsClassList" @clkItem="clkFundsItem" v-model="fundsSelItemId" ></FcItem></div>
        </div> -->
        <div style="margin: 10px 0" />
        <div v-show="switchComp.receiveType" >
          <div style="display: flex;justify-content: left;align-items: center;">
          <div style="width: 100px;font-size: 14px;color: #6A6C70;">收款类型：</div>
          <div class="width" style="display: flex;align-items: center;">
            <div ><FcItem :list="fundsClassList" @clkItem="clkFundsItem" v-model="fundsSelItemId" ></FcItem></div>
            <!-- <div class="wd30">
              <SimplePanelSelection
                v-if="showApply"
                ref="costType"
                :def-code="switchComp.costType"
                :all-data="typeList"
                mode-name="收款类型"
                key-str="code"
                val-str="value"
                components-width="600"
                @setVal="costType"
              />
            </div> -->
          </div>
          </div>
          <div style="display: flex;align-items: center;margin-left: 20px;">
            <div style="font-size: 14px;color: #6A6C70;margin-right: 6px">收款到账日期:</div>
            <el-date-picker
              class="wd30"
              v-model="switchComp.occurDate"
              type="date"
              value-format="yyyy-MM-dd"
              placeholder="选择日期"
            />
            <div style="color: red;font-size: 12px;margin-left: 6px">tip：系统默认的是今天的日期，如果款不是今天收的 请更改日期</div>
          </div>
        </div>

        <div v-if="switchComp.receiveType == 3 && switchComp.costType">
          <div style="margin: 10px 0" />
          <div style="display: flex;justify-content: left;align-items: center;">
            <div style="width: 100px;font-size: 14px;color: #6A6C70;">票种：</div>
            <div class="width">
              <el-tag
                v-for="(item,index) in ticketTypeList"
                :key="index"
                style="margin-right: 10px;cursor: pointer;"
                :type="switchComp.ticketType==item.code?'success':'info'"
                @click="selectTicketType(item.code)"
              >{{ item.value }}</el-tag>
            </div>
          </div>
        </div>
        <div v-if="switchComp.receiveType && switchComp.costType">
          <div style="margin: 10px 0" />
          <div style="display: flex;justify-content: left;align-items: center;">
            <div style="width: 100px;font-size: 14px;color: #6A6C70;">收款公司：</div>
            <div v-if="switchComp.costType== 1 && receiveComCode1020.length" >
              <el-tag
                v-for="(item,index) in receiveComCode1020"
                :key="index"
                style="margin-right: 10px;cursor: pointer;"
                :type="switchComp.receiveCompany==item.code?'success':'info'"
                @click="switchComp.receiveType == 1 ? selectPersonalReceiveCompany(item):selectReceiveCompany(item)"
              >{{ item.value }}</el-tag>
            </div>
            <!-- && switchComp.costType != 1 -->
            <div v-if="switchComp.costType != null " >
              <el-tag
                v-for="(item,index) in allCompanyList1020"
                :key="index"
                style="margin-right: 10px;cursor: pointer;"
                :type="switchComp.receiveCompany==item.deptId?'success':'info'"
                @click="switchComp.receiveType == 1 ? selectPersonalReceiveCompanyAll(item): selectReceiveCompanyAll(item)"
              >{{ item.name }}</el-tag>
            </div>
          </div>
        </div>
        <div>
          <el-table
            v-if="switchComp.receiveCompany"
            ref="singleTable"
            :data="accountlist"
            highlight-current-row
            style="width: 100%"
            header-row-class-name="cd-tab-cls"
            cell-class-name="cd-tab-cls"
            @current-change="handleCurrentChange"
          >
            <el-table-column
              key="0"
              width="15"
              fixed
            >
              <template slot-scope="scope"><img :class="!scope.row.checked?'showpic':''" style="width: 14px;vertical-align: middle;" src="../../../assets/images/yixuanze.png"></template>
            </el-table-column>
            <el-table-column width="80px" fixed align="center" property="type" label="账户分类" :formatter="(row, column, cellValue, index) => { return cellValue==0?'网银私户':(cellValue==1?'网银公户':'库存现金')}" />
            <el-table-column width="80px" fixed align="center" property="discountOrCash" label="账户类型" :formatter="(row, column, cellValue, index) => { return cellValue==0?'银行网银':(cellValue==1?'承兑账户':'库存现金')}" />
            <el-table-column width="200px" align="center" property="accountName" label="账户名称" />
            <el-table-column align="center" property="bankName" label="银行名称" />
            <el-table-column align="center" property="accountNumber" label="账户号" />
            <el-table-column width="100px" align="center" property="initializationMoney" label="余额" :formatter="NumFmt2" />
          </el-table>
        </div>

        <div v-show=" currentRow && currentRow.id && switchComp.receiveType == 3 && switchComp.costType">
          <div style="margin: 10px 0" />
          <div v-show="!switchComp.id" style="display: flex;justify-content: left;align-items: center;">
            <div style="width: 100px;font-size: 14px;color: #6A6C70;">录入方式：</div>
            <div class="width">
              <el-tag
                v-for="(item,index) in autoTypeList"
                :key="index"
                style="margin-right: 10px;cursor: pointer;"
                :type="isAutoType==item.value?'success':'info'"
                @click="isAutoType=item.value"
              >{{ item.label }}</el-tag>
            </div>
          </div>
          <div v-show="isAutoType==1">
            <div style="margin: 10px 0" />
            <div style="display: flex;justify-content: left;align-items: center;">
              <div style="width: 100px;font-size: 14px;color: #6A6C70;">票号：</div>
              <el-input
                v-model="switchComp.ticketNumber"
                class="width"
                clearable
                placeholder="输入票号"
                label="票号"
              />
            </div>
            <div style="margin: 10px 0" />
            <div style="display: flex;justify-content: left;align-items: center;">
              <div style="width: 100px;font-size: 14px;color: #6A6C70;">能否转让：</div>
              <div class="width">
                <el-tag
                  v-for="(item,index) in cdZrTypeList"
                  :key="index"
                  style="margin-right: 10px;cursor: pointer;"
                  :type="switchComp.isTransferred == item.value?'success':'info'"
                  @click="selTransfer(item)"
                >{{ item.label }}</el-tag>
              </div>
            </div>
            <div style="margin: 10px 0" />
            <div style="display: flex;justify-content: left;align-items: center;">
              <div style="width: 100px;font-size: 14px;color: #6A6C70;">出票日：</div>
              <el-date-picker
                v-model="switchComp.votingDate"
                style="width: 100%;"
                type="date"
                placeholder="选择日期"
                value-format="yyyy-MM-dd"
              />
            </div>
            <div style="margin: 10px 0" />
            <div style="display: flex;justify-content: left;align-items: center;">
              <div style="width: 100px;font-size: 14px;color: #6A6C70;">到期日：</div>
              <el-date-picker
                v-model="switchComp.expireDate"
                style="width: 100%;"
                type="date"
                value-format="yyyy-MM-dd"
                placeholder="选择日期"
              />
            </div>
          </div>
        </div>
        <div v-show="currentRow && currentRow.id && switchComp.costType && switchComp.receiveType && (switchComp.receiveType!=3 || isAutoType==1) ">
          <div style="margin: 10px 0" />
          <div style="display: flex;justify-content: left;align-items: center;">
            <div style="width: 100px;font-size: 14px;color: #6A6C70;">{{ switchComp.receiveType==3?'票面金额（元）：':'收款金额（元）：' }}</div>
            <el-input
              v-model="switchComp.balance"
              class="width"
              type="text"
              clearable
              placeholder="输入收款金额"
              label="收款金额"
              @input="convertToChineseMoney(switchComp.balance)"
              @change="fmtMoney"
            />
          </div>
          <div style="margin: 10px 0" />
          <div style="display: flex;justify-content: left;align-items: center;">
            <div style="width: 100px;font-size: 14px;color: #6A6C70;">大写：</div>
            <el-input
              v-model="switchComp.moneyC"
              class="width"
              clearable
              disabled
            />
          </div>
          <div style="margin: 10px 0" v-show="isDiscountedAmountShow" />
             <div style="display: flex;justify-content: left;align-items: center;" v-show="isDiscountedAmountShow">
            <div style="width: 100px;font-size: 14px;color: #6A6C70;">贴息金额（元）：</div>
            <el-input
              v-model="switchComp.discountedAmount"
              class="width"
              type="text"
              clearable
              placeholder="输入贴息金额"
              label="贴息金额"
              @change="()=>switchComp.discountedAmount=moneyFmt(switchComp.discountedAmount)"
            />
          </div>
        </div>
      </div>
      <div style="margin: 10px 0" />
      <div v-show="currentRow && currentRow.id" style="margin-top: 20px;">
        <div v-show="switchComp.costType && switchComp.receiveType && (switchComp.receiveType!=3 || isAutoType==1) ">
          <div style="font-size: 16px;margin-bottom: 10px;">付款账户信息</div>
          <div v-show="switchComp.receiveType==3">
            <div style="margin: 10px 0" />
            <div style="display: flex;justify-content: left;align-items: center;">
              <div style="width: 100px;font-size: 14px;color: #6A6C70;">出票人</div>
              <el-input
                v-model="switchComp.drawerName"
                class="width"
                type="text"
                clearable
                placeholder="输入出票人"
                label="出票人"
              />
            </div>
            <div style="margin: 10px 0" />
            <div style="display: flex;justify-content: left;align-items: center;">
              <div style="width: 100px;font-size: 14px;color: #6A6C70;">承兑人</div>
              <el-input
                v-model="switchComp.acceptorName"
                class="width"
                type="text"
                clearable
                placeholder="输入承兑人"
                label="承兑人"
              />
            </div>
          </div>
          <!-- switchComp.costType == 1 && switchComp.receiveCompany != 20 -->
          <div v-if="isShowSelCgwlKh">
            <div>
              <div style="display: flex;justify-content: left;align-items: center;">
                <div style="width: 100px;font-size: 14px;color: #6A6C70;">客户类型：</div>
                <div class="width">
                  <el-tag style="margin-right: 10px;cursor: pointer;" :type="switchComp.typeSelect==0?'success':'info'" @click="checkType(0)">货主公司</el-tag>
                  <el-tag style="margin-right: 10px;cursor: pointer;" :type="switchComp.typeSelect==1?'success':'info'" @click="checkType(1)">船公司</el-tag>
                </div>
              </div>
              <div style="margin: 10px 0" />
              <div style="display: flex;justify-content: left;align-items: center;">
                <div style="width: 100px;font-size: 14px;color: #6A6C70;">{{ switchComp.receiveType==3?'背书人':'客户名称：' }}</div>

                <el-input v-if="parseInt(switchComp.receiveCompany) === 25 " v-model="switchComp.payUser" placeholder="付款人" />
                <div v-if="!switchComp.receiveCompany || parseInt(switchComp.receiveCompany)!==25">
                  <PanelSelection
                    v-show="switchComp.typeSelect == 0"
                    ref="kehu"
                    class="width"
                    add-url="/api/baseInit/saveSysCustomerSecondlevel"
                    add-url-method="get"
                    :is-auto-sel="isRequireCustomSel"
                    :def-id="switchComp.customerId"
                    :def-name="switchComp.customerName"
                    :all-data="customerList"
                    :mode-name="switchComp.receiveType==3?'背书人':'客户'"
                    data-key="common_kehu"
                    key-str="id"
                    val-str="fullName"
                    components-width="600"
                    @setVal="setCustomer"
                  />
                  <PanelSelection
                    v-show="switchComp.typeSelect == 1"
                    ref="supplier"
                    class="width"
                    add-url="/api/baseInit/saveSysSupplierName"
                    add-url-method="get"
                    :is-auto-sel="isRequireCustomSel"
                    :def-id="switchComp.customerId"
                    :def-name="switchComp.customerName"
                    :all-data="supplierList"
                    :mode-name="switchComp.receiveType==3?'背书人':'客户'"
                    data-key="common_supplier"
                    key-str="id"
                    val-str="name"
                    components-width="600"
                    @setVal="setSupplier"
                  />
                </div>
              </div>
            </div>
          </div>
          <!-- switchComp.costType != null -->
          <div v-else-if="switchComp.costType != null && !isShowSelCgwlKh">
            <div style="margin: 10px 0" />
            <div style="display: flex;justify-content: left;align-items: center;">
              <div style="width: 100px;font-size: 14px;color: #6A6C70;">{{ switchComp.receiveType==3?'背书人':'客户名称：' }} </div>
              <PanelSelection
                    v-if="switchComp.costType==1 && switchComp.receiveCompany==41"
                    ref="hsKehu"
                    class="width"
                    add-url="/api/baseInit/saveSysCustomerSecondlevel"
                    add-url-method="get"
                    dept-code="HNHS"
                    :is-auto-sel="isRequireCustomSel"
                    :def-id="switchComp.customerId"
                    :def-name="switchComp.customerName"
                    :all-data="customerListHS"
                    :mode-name="switchComp.receiveType==3?'背书人':'客户'"
                    data-key="common_kehu"
                    key-str="id"
                    val-str="fullName"
                    components-width="600"
                    @setVal="setCustomer"
                  />
              <PanelSelection
                v-else
                ref="supplier"
                class="width"
                add-url="/api/sysExternalAccount/savetExternalAccountByCustomerNameAndBankAccount"
                add-url-method="get"
                :is-auto-sel="isRequireCustomSel"
                :account-type="switchComp.receiveType"
                :def-id="switchComp.customerId"
                :def-name="switchComp.customerName"
                :all-data="fdbizCustomerList"
                :mode-name="switchComp.receiveType==3?'背书人':'客户'"
                data-key="fdbiz_customer"
                key-str="id"
                val-str="companyName+bankAccount|4e"
                components-width="600"
                @setVal="setFdbizCustomer"
              />
            </div>
          </div>
          <div v-if="switchComp.costType != null && switchComp.receiveType == 2">
            <div style="margin: 10px 0" />
            <div style="display: flex;justify-content: left;align-items: center;">
              <div style="width: 100px;font-size: 14px;color: #6A6C70;">付款银行：</div>
              <el-input
                v-model="switchComp.paymentBank"
                class="width"
                clearable
                placeholder="输入付款银行"
                label="付款银行"
              />
            </div>
            <div style="margin: 10px 0" />
            <div style="display: flex;justify-content: left;align-items: center;">
              <div style="width: 100px;font-size: 14px;color: #6A6C70;">付款账号：</div>
              <el-input
                v-model="switchComp.paymentAccount"
                class="width"
                clearable
                placeholder="输入付款账号"
                label="付款账号"
              />
            </div>
          </div>
          <div style="margin: 10px 0" />
          <div style="display: flex;justify-content: left;align-items: center;">
            <div style="width: 100px;font-size: 14px;color: #6A6C70;">收款说明：</div>
            <el-input
              v-model="switchComp.topUpRemark"
              clearable
              placeholder="输入收款说明（选填）"
              label="收款说明"
              type="textarea"
              :rows="2"
              maxlength="255"
              show-word-limit
            />
          </div>
        </div>
        <div v-show="switchComp.receiveType && switchComp.receiveType == 3 && isAutoType==2">
          <div style="margin: 10px 0" />
           <div style="display: flex;justify-content: left;align-items: center;">
            <div style="width: 90px;font-size: 14px;color: #6A6C70;">文本识别：</div>
            <div style="width:70%;">
              <textarea name="textListCd" v-model="cdHtmlStr" style="width:100%;" id="" cols="30" rows="10"></textarea>
            </div>
            <el-button class="ocr-btn-cls" @click="fmtHtmlStr">识别</el-button>
          </div>
        </div>
        <div v-show="switchComp.costType && switchComp.receiveType && (switchComp.receiveType!=3 || isAutoType!=null) ">
          <div style="margin: 10px 0" />
          <div style="display: flex;justify-content: left;align-items: center;">
            <div style="width: 90px;font-size: 14px;color: #6A6C70;">上传截图：</div>
            <div>
              <el-upload
                ref="upload"
                accept=".jpg,.png,.gif,.jpeg,.bmp"
                :file-list="fileList"
                :action="uploadUrl"
                list-type="picture-card"
                :limit="1"
                :before-remove="handleRemove"
                :on-error="uploadError"
                :on-success="uploadSuccess"
                :before-upload="beforeAvatarUpload"
                :on-preview="handlePictureCardPreview"
                :class="picList.length>=1?'hiddenClass':''"
              >
                <i class="el-icon-plus" />
              </el-upload>
              <el-dialog :visible.sync="dialogVisible">
                <img width="100%" :src="dialogImageUrl" alt="">
              </el-dialog>

            </div>
            <el-button v-show="switchComp.receiveType && switchComp.receiveType == 3 && isAutoType==2" class="ocr-btn-cls" type="success" :loading="ocrIng" plain @click="ocrClk">{{ ocrIng?'识别中，请稍等..':'批量导入（仅支持图片）' }}</el-button>
            <div v-show="ocrList && ocrList.length" style="margin-left:20px;margin-top:10px;">
              <el-descriptions class="margin-top" title="自动识别信息" :content-style="stlyeContOcr" :column="2" style="max-height:200px;overflow: auto;max-width:90%;" size="mini" border>
                <el-descriptions-item v-for="(ocritem,ocrindex) in ocrList" :key="ocrindex">
                  <template slot="label">
                    {{ ocritem.Name }}
                  </template>
                  {{ ocritem.Value }}
                  <el-tooltip class="item " effect="dark" content="点击复制" placement="top">
                    <i class="el-icon-copy-document cp-cls-tip" :data-clipboard-text="ocritem.Value" />
                  </el-tooltip>

                </el-descriptions-item>
              </el-descriptions>
            </div>
          </div>
        </div>
      </div>

      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submit">提交</el-button>
      </span>
    </el-dialog>

    <el-dialog
      class="xuanxiang2"
      title="核销详情"
      :visible.sync="showDetailApply"
      width="60%"
      :before-close="detailApplyhandleClose"
      :close-on-click-modal="false"
    >
      <div style="text-align:center;"><i v-if="!receiveList || receiveList.length==0" class="el-icon-loading" /></div>

      <div v-for="(item,index) in receiveList" :key="index">
        <el-form :model="item.applicationForm" class="query-form demo-form-inline" style="margin-left: 5%">
          <div style="display: flex;">
            <div class="sp_add_left">
              <el-form-item label="核销金额:" label-width="90px">
                <el-input v-model="item.applicationForm.collectionPrice" placeholder="输入收款金额" clearable disabled>
                  <template slot="append">元</template>
                </el-input>
              </el-form-item>
              <el-form-item label="到账金额:" label-width="90px">
                <el-input v-model="item.applicationForm.receivable" placeholder="到账金额" clearable disabled>
                  <template slot="append">元</template>
                </el-input>
              </el-form-item>
              <el-form-item label="预收款抵扣:" label-width="90px">
                <el-input v-model="item.applicationForm.totalReceiveDeduct" placeholder="输入收款金额" clearable disabled>
                  <template slot="append">元</template>
                </el-input>
              </el-form-item>
            </div>
            <div class="sp_add_right">
              <el-form-item label="付款类型:" label-width="90px">
                <el-input :value="paymentTypeFmt(null,null,item.applicationForm.paymentType,null)" clearable disabled />
              </el-form-item>
              <el-form-item label="备注:" label-width="90px">
                <el-input v-model="item.applicationForm.remark" placeholder="输入备注" clearable disabled />
              </el-form-item>
            </div>
          </div>
        </el-form>
        <el-table
          class="el-table-info"
          :data="item.tableData"
          stripe
          style="margin-left: 5%;width: 90%;"
        >
          <el-table-column
            :formatter="publicFmt"
            prop="cusSupName"
            label="客户名称"
          />
          <el-table-column
            :formatter="publicFmt"
            prop="shipName"
            label="船舶"
          />
          <el-table-column
            :formatter="publicFmt"
            prop="voyageNumber"
            label="航次号"
          />
          <el-table-column
            :formatter="publicFmt"
            prop="costName"
            label="费种"
          />
          <el-table-column
            :formatter="NumFmt"
            prop="receivable"
            label="应收金额"
          />
          <el-table-column
            :formatter="NumFmt"
            prop="money"
            label="收款金额（元）"
          />
          <el-table-column
            :formatter="NumFmt"
            prop="deduct"
            label="预收抵扣"
          />
          <el-table-column
            :formatter="NumFmt"
            prop="charge"
            label="剩余收款"
          />
        </el-table>
      </div>
      <span v-if="receiveWater.applyStatus != 2" slot="footer" class="dialog-footer">
        <el-button type="primary" @click="toAgree()">录入凭证号,完成核销</el-button>
      </span>
    </el-dialog>

    <el-dialog
      class="xuanxiang2"
      title="核销详情"
      :visible.sync="showBusinessDetailApply"
      width="60%"
      :before-close="businessDetailApplyhandleClose"
      :close-on-click-modal="false"
    >
      <div style="font-size: 16px;font-weight: 500;">客户信息：</div>
      <el-form :model="receiveWater" class="query-form demo-form-inline">
        <div style="width: 95%;">
          <el-form-item label="客户公司:" label-width="90px">
            <el-input v-model="receiveWater.customerName" clearable disabled />
          </el-form-item>
          <el-form-item label="开户行:" label-width="90px">
            <el-input v-model="receiveWater.paymentBank" clearable disabled />
          </el-form-item>
          <el-form-item label="客户账号:" label-width="90px">
            <el-input v-model="receiveWater.paymentAccount" clearable disabled />
          </el-form-item>
        </div>
        <div style="display: flex;">
          <div class="sp_add_left">
            <el-form-item label="收款金额:" label-width="90px">
              <el-input v-model="receiveWater.balance" clearable disabled>
                <template slot="append">元</template>
              </el-input>
            </el-form-item>
          </div>
          <div class="sp_add_right">
            <el-form-item label="账户类型:" label-width="90px">
              <el-input :value="receviceTypeFmt(null,null,receiveWater.receviceType,null)" clearable disabled />
            </el-form-item>
          </div>
        </div>
      </el-form>
      <div>
        <div style="font-size: 16px;font-weight: 500;">核销信息：</div>
        <el-form ref="addVerificationInfo" :model="verificationInfo" class="query-form demo-form-inline">
          <div style="display: flex;">
            <div class="sp_add_left">
              <el-form-item label="船名:" label-width="90px">
                <el-input v-model="verificationInfo.shipName" placeholder="请输入船名" clearable disabled />
              </el-form-item>
              <el-form-item label="结算吨位:" label-width="90px">
                <el-input v-model="verificationInfo.settleTonnage" placeholder="请输入结算吨位" clearable disabled>
                  <template slot="append">吨</template>
                </el-input>
              </el-form-item>
              <el-form-item label="滞期费:" label-width="90px">
                <el-input v-model="verificationInfo.demurrage" placeholder="请输入滞期费" clearable disabled>
                  <template slot="append">元</template>
                </el-input>
              </el-form-item>
            </div>
            <div class="sp_add_right">
              <el-form-item label="航次号:" label-width="90px">
                <el-input v-model="verificationInfo.voyageNum" placeholder="请输入航次号" clearable disabled />
              </el-form-item>
              <el-form-item label="单价:" label-width="90px">
                <el-input v-model="verificationInfo.price" placeholder="请输入单价" clearable disabled>
                  <template slot="append">元/吨</template>
                </el-input>
              </el-form-item>
            </div>
          </div>
        </el-form>
      </div>
      <div>
        <div style="font-size: 16px;font-weight: 500;">合同信息：</div>
        <div style="display: flex;padding: 20px 0;width: 95%;">
          <div style="height:40px;line-height: 30px;width: 80px;text-align: right;">合同编号:</div>
          <div style="margin-left: 10px;">
            <el-input disabled :value="detailObj.name" style="width: 300px" />
          </div>
          <div>
            <el-upload
              disabled
              class="upload-demo"
              :action="upload"
              :on-preview="handleBusinessPreview"
              :limit="1"
              :file-list="contractList"
            />
            <el-dialog :visible.sync="dialogVisible" append-to-body>
              <img width="100%" :src="dialogImageUrl" alt="">
            </el-dialog>
          </div>
        </div>
      </div>
      <span v-if="receiveWater.applyStatus !== 2" slot="footer" class="dialog-footer">
        <el-button type="primary" @click="toAgreeBusiness()">录入凭证号,完成核销</el-button>
      </span>
    </el-dialog>

    <el-dialog
      class="xuanxiang2"
      title="收款账户详情"
      :visible.sync="showAccount"
      width="60%"
      :before-close="showAccounthandleClose"
      :close-on-click-modal="false"
    >
      <div style="font-size: 20px;font-weight: 500;margin-bottom: 10px">收款单编号：{{ waterDetail.waterNo }}

      </div>
      <div style="display: flex;margin-bottom: 10px;width: 100%">
        <div style="display: flex;font-size: 20px;width: 30%">
          <div>收款类型：</div>
          <div>{{ waterDetail.fundsClassName || costTypeFmt(null,null,waterDetail.costType,null) }}</div>
        </div>
        <div style="display: flex;font-size: 20px;width: 30%">
          <div>账户类型：</div>
          <div>{{ receviceTypeFmt(null,null,waterDetail.receviceType,null) }}</div>
        </div>
        <div style="display: flex;font-size: 20px;width: 30%">
          <div>收款金额：</div>
          <div>{{ NumFmt(null,null,waterDetail.balance,null) }}元</div>
        </div>
      </div>
      <div v-if="waterDetail.discountedAmount" style="display: flex;margin-bottom: 10px;width: 100%">
        <div style="display: flex;font-size: 20px;width: 30%">
          <div>贴息金额：</div>
          <div>{{ NumFmt(null,null,waterDetail.discountedAmount,null) }}元</div>
        </div>
      </div>
      <!-- <div style="display: flex;margin-bottom: 10px;width: 100%">
        <div v-if="waterDetail.receviceType == 3" style="display: flex;font-size: 20px;flex-shrink:0;">
          <div>票号：</div>
          <div>{{ waterDetail.ticketNumber }}&nbsp;&nbsp;</div>
        </div>
        <div v-if="waterDetail.receviceType == 3" style="display: flex;font-size: 20px;width: 20%">
          <div>票种：</div>
          <div>{{ ticketTypeFmt(null,null,waterDetail.ticketType,null) }}</div>
        </div>
        <div v-if="waterDetail.receviceType == 3" style="display: flex;font-size: 20px;">
          <div>到期时间：</div>
          <div>{{ myFmtDateTime(waterDetail.expireDate,"YYYY-MM-DD") }}</div>
        </div>
      </div> -->
      <div v-if="waterDetail.receviceType == 3" style="display: flex;margin-bottom: 10px;width: 100%;">
        <el-table
          border
          :data="itemCdList"
          style="width: 100%"
        >
          <el-table-column align="center" property="ticketNumber" label="票号" />
          <el-table-column align="center" property="ticketType" label="票种" :formatter="(row, column, cellValue, index) => { return ticketTypeFmt(null,null,cellValue,null)}" />
          <el-table-column align="center" property="balance" label="金额" :formatter="(row, column, cellValue, index) => { return NumFmt(null,null,cellValue,null)+'元'}" />
          <el-table-column align="center" property="expireDate" label="到期时间" :formatter="(row, column, cellValue, index) => { return myFmtDateTime(cellValue,'YYYY-MM-DD') }" />
          <el-table-column align="center" property="picList" label="明细">
            <template slot-scope="scope">
              <span v-show="!scope.row.picList">-</span>
              <el-button v-show="scope.row.picList" type="text" @click="showPic(scope.row)">查看</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <el-table
        ref="singleTable"
        :data="showAccountlist"
        highlight-current-row
        style="width: 100%"
      >
        <el-table-column fixed align="center" property="type" label="账户分类" :formatter="(row, column, cellValue, index) => { return cellValue==0?'网银私户':(cellValue==1?'网银公户':'库存现金')}" />
        <el-table-column fixed align="center" property="discountOrCash" label="账户类型" :formatter="(row, column, cellValue, index) => { return cellValue==0?'银行网银':(cellValue==1?'承兑账户':'库存现金')}" />
        <el-table-column align="center" property="accountName" label="账户名称" width="120px" />
        <el-table-column align="center" property="bankName" label="银行名称" />
        <el-table-column align="center" property="accountNumber" label="账户号" />
      </el-table>
      <div style="margin-top: 10px">
        <div style="font-size: 20px;font-weight: 500;margin-bottom: 5px">收款说明：</div>
        <el-input v-model="waterDetail.remark" type="textarea" disabled placeholder="暂无说明" />
      </div>
    </el-dialog>

    <el-dialog
      class="xuanxiang"
      title="查看财务截图"
      :visible.sync="showPicApply"
      width="60%"
      :before-close="handleClose"
      :close-on-click-modal="false"
    >
      <div v-for="(item,index) in picUrl" :key="index" style="text-align:center;">
        <image-rotate :src="item"></image-rotate>
      </div>
    <!-- <div v-for="(item,index) in picUrl" style="text-align:center;">
      <img  :key="index" style="width: 100%;height: 100%;" :src="item"> -->
      <!-- 页码 -->
      <!-- <span v-if="picUrl.length>1">{{(index+1)+'/'+picUrl.length}}</span>
    </div> -->
    </el-dialog>
    <!-- closeCdDialog :before-close -->
    <el-dialog
      title="承兑汇票批处理"
      fullscreen
      :visible.sync="ocrListShow"
      width="100%"
      custom-class="cd-list-dialog-cls"
      :close-on-click-modal="false"
      @open="setAllSelectByCdTable"
    >
      <div style="padding:10px;">
        <div style="margin-bottom: 10px;">
          <!-- <div style="font-size: 16px;margin-bottom: 10px;">收款基本信息</div>
          <div style="display: flex;justify-content: left;align-items: center;">
            <div style="width: 100px;font-size: 14px;color: #6A6C70;">收款类型：</div>
            <div>
              <SimplePanelSelection
                ref="costType"
                :all-data="typeList"
                mode-name="收款类型"
                :def-name="costDefName"
                :def-code="defCodeCostType"
                key-str="code"
                val-str="value"
                components-width="600"
                @setVal="costType"
              />
            </div>
          </div> -->
          <div style="margin: 10px 0" />
          <div v-show="switchComp.costType" style="display: flex;justify-content: left;align-items: center;">
            <div style="width: 100px;font-size: 14px;color: #6A6C70;">收款公司：</div>
            <div class="width">
              <div v-if="switchComp.costType== 1">
                <el-tag
                  v-for="(item,index) in receiveComCode1020"
                  :key="index"
                  style="margin-right: 10px;cursor: pointer;"
                  :type="switchComp.receiveCompany==item.code?'success':'info'"
                  @click="selectReceiveCompany(item)"
                >{{ item.value }}</el-tag>
              </div>
              <!-- && switchComp.costType != 1 -->
              <div v-if="switchComp.costType != null ">
                <el-tag
                  v-for="(item,index) in allCompanyList1020"
                  :key="index"
                  style="margin-right: 10px;cursor: pointer;"
                  :type="switchComp.receiveCompany==item.deptId?'success':'info'"
                  @click="selectReceiveCompanyAll(item)"
                >{{ item.name }}</el-tag>
              </div>
            </div>
          </div>

          <div>
            <el-table
              v-if="accountlist.length > 0"
              ref="singleTable"
              :data="accountlist"
              highlight-current-row
              style="width: 100%"
              header-row-class-name="cd-tab-cls"
              cell-class-name="cd-tab-cls"
              @current-change="handleCurrentChange"
            >
              <el-table-column
                key="0"
                width="15"
                fixed
              >
                <template slot-scope="scope"><img :class="!scope.row.checked?'showpic':''" style="width: 14px;vertical-align: middle;" src="../../../assets/images/yixuanze.png"></template>
              </el-table-column>
              <el-table-column width="80px" fixed align="center" property="type" label="账户分类" :formatter="(row, column, cellValue, index) => { return cellValue==0?'网银私户':(cellValue==1?'网银公户':'库存现金')}" />
              <el-table-column width="80px" fixed align="center" property="discountOrCash" label="账户类型" :formatter="(row, column, cellValue, index) => { return cellValue==0?'银行网银':(cellValue==1?'承兑账户':'库存现金')}" />
              <el-table-column width="200px" align="center" property="accountName" label="账户名称" />
              <el-table-column align="center" property="bankName" label="银行名称" />
              <el-table-column align="center" property="accountNumber" label="账户号" />
              <el-table-column width="100px" align="center" property="initializationMoney" label="余额" :formatter="NumFmt2" />
            </el-table>
          </div>

        </div>
        <el-table
          v-if="ocrListShow"
          ref="cdListTab"
          :data="ocrCdList"
          highlight-current-row
          border
          header-align="center"
          size="mini"
          stripe
          header-row-class-name="cd-tab-cls cd-tab-header-cls"
          cell-class-name="cd-tab-cls"
          @selection-change="cdListSelectChange"
        >
          <el-table-column
            type="selection"
            width="18"
            min-width="18"
            max-width="18"
          />
          <el-table-column
            type="index"
            label="序号"
            width="25"
            min-width="25"
            max-width="25"
            :index="idx=>idx+1"
          />

          <el-table-column
            label="票号"
            width="240"
            min-width="240"
            max-width="240"
          >
            <template slot-scope="scope">
              <el-input v-model="scope.row.billNo" />
            </template>
          </el-table-column>
          <el-table-column
            label="票据类型"
            width="40"
            min-width="40"
            max-width="40"
          >
            <template slot-scope="scope">
              <el-select v-model="scope.row.billType" placeholder="请选择">
                <el-option
                  v-for="item in cdTypeList"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </template>
          </el-table-column>
          <el-table-column

            label="能否转让"
            width="50"
            min-width="50"
            max-width="50"
          >
            <template slot-scope="scope">
              <el-select v-model="scope.row.isTransferred" placeholder="请选择">
                <el-option
                  v-for="item in cdZrTypeList"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </template>
          </el-table-column>
          <el-table-column

            label="出票日"
            width="76"
            min-width="76"
            max-width="76"
          >
            <template slot-scope="scope">
              <el-date-picker
                v-model="scope.row.issueDate"
                style="width: 100%;"
                type="date"
                prefix-icon="false"
                value-format="yyyy-MM-dd"
                placeholder="选择日期"
              />
            </template>
          </el-table-column>
          <el-table-column

            label="到期日"
            width="76"
            min-width="76"
            max-width="76"
          >
            <template slot-scope="scope">
              <!-- <el-input v-model="scope.row.dueDate" /> -->
              <el-date-picker
                v-model="scope.row.dueDate"
                style="width: 100%;"
                type="date"
                prefix-icon="false"
                value-format="yyyy-MM-dd"
                placeholder="选择日期"
              />
            </template>
          </el-table-column>
          <el-table-column
            width="100"
            min-width="100"
            max-width="100"
            label="票面金额"
          >
            <template slot-scope="scope">
              <el-input v-model="scope.row.billAmount" @change="(val)=>upCdListPrice(val,scope.row)" />
            </template>
          </el-table-column>
          <el-table-column
            width="230"
            min-width="230"
            max-width="230"
            label="出票人"
          >
            <template slot-scope="scope">
              <el-input v-model="scope.row.issuer" />
            </template>
          </el-table-column>
          <el-table-column
            width="230"
            min-width="230"
            max-width="230"
            label="承兑人"
          >
            <template slot-scope="scope">
              <el-input v-model="scope.row.acceptor" />
            </template>
          </el-table-column>
          <el-table-column
            width="132"
            min-width="132"
            max-width="132"
            label="背书人"
          >
            <template slot-scope="scope">
              <!-- switchComp.costType == 1 && switchComp.receiveCompany != 20 -->
              <div v-if="isShowSelCgwlKh" style="display: flex;justify-content: left;align-items: center;">
                <div class="width">
                  <el-tag style="cursor: pointer;" :type="scope.row.typeSelect==0?'success':'info'" @click="scope.row.typeSelect=0">货主公司</el-tag>
                  <el-tag style="cursor: pointer;" :type="scope.row.typeSelect==1?'success':'info'" @click="scope.row.typeSelect=1">船公司</el-tag>
                </div>
              </div>
              <div style="display: flex;justify-content: left;align-items: center;">
                <!-- switchComp.costType == 1 && switchComp.receiveCompany != 20 -->
                <div v-if="isShowSelCgwlKh">
                  <PanelSelection
                    v-if="scope.row.typeSelect == 0"
                    ref="kehuocr"
                    class="width"
                    add-url="/api/baseInit/saveSysCustomerSecondlevel"
                    add-url-method="get"
                    :def-id="scope.row.customerId"
                    :def-name="scope.row.customerName"
                    :all-data="customerList"
                    mode-name="背书人"
                    data-key="common_kehu"
                    key-str="id"
                    val-str="fullName"
                    components-width="600"

                    @setVal="(obj)=>{setItemObj(scope.row,obj.id,obj.fullName)}"
                  />
                  <!-- @setVal="(obj)=>{scope.row.customerId=obj.id;scope.row.customerName=obj.fullName;}" -->
                  <PanelSelection
                    v-if="scope.row.typeSelect == 1"
                    ref="supplierocr"
                    class="width"
                    add-url="/api/baseInit/saveSysSupplierName"
                    add-url-method="get"
                    :def-id="scope.row.customerId"
                    :def-name="scope.row.customerName"
                    :all-data="supplierList"
                    mode-name="背书人"
                    data-key="common_supplier"
                    key-str="id"
                    val-str="name"
                    components-width="600"
                    @setVal="(obj)=>{setItemObj(scope.row,obj.id,obj.name)}"
                  />
                </div>
                <!-- switchComp.costType && (switchComp.costType != 1 || switchComp.receiveCompany==20) -->
                <div v-if="switchComp.costType && !isShowSelCgwlKh">
                  <PanelSelection
                    ref="supplierocr"
                    class="width"
                    add-url="/api/sysExternalCustomer/createAndPlate"
                    add-url-method="post"
                    :account-type="switchComp.receiveType"
                    :def-id="scope.row.customerId"
                    :def-name="scope.row.customerName"
                    :all-data="fdbizCustomerList"
                    mode-name="背书人"
                    data-key="fdbiz_customer"
                    key-str="id"
                    val-str="companyName"
                    components-width="600"
                    @setVal="(obj)=>{setItemObj(scope.row,obj.id,obj.companyName)}"
                  />
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column
            width="90"
            max-width="90"
            min-width="90"
            label="明细"
          >
            <template slot-scope="scope">
              <!-- :file-list="scope.row.fileList" -->
              <el-upload
                accept=".jpg,.png,.jpeg,.bmp,.pdf"
                :file-list="scope.row.showfile"
                :action="uploadUrl"
                :limit="1"
                :before-remove="()=> handleRemoveOcr(scope.row)"
                :on-preview="handleBusinessPreviewOcr"
                :on-error="uploadError"
                :on-success="(response)=> uploadRowFileSuccess(response,scope.row)"
                :before-upload="beforeAvatarUpload"
                :class="scope.row.fileList && scope.row.fileList.length>=1?'hiddenClass':''"
              >
                <el-button size="small" plain type="text">上传明细</el-button>
              </el-upload>
            </template>

          </el-table-column>
          <!-- <el-table-column
            width="180"
              min-width="180"
            max-width="180"
            label="收款说明"
          >
            <template slot-scope="scope">
              <el-input
                v-model="scope.row.topUpRemark"
                clearable
                placeholder="输入收款说明（选填）"
                type="textarea"
                class="cd-cell-sk-cls"
                :rows="1"
                maxlength="255"
              />
            </template>
          </el-table-column> -->
        </el-table>

        <el-dialog :visible.sync="dialogVisibleOcr" :modal="false">
          <img width="100%" :src="dialogImageUrlOcr" alt="">
        </el-dialog>
      </div>
      <span slot="footer" class="dialog-footer">
        <div style="width:430px;">
          <el-input
            v-model="switchComp.topUpRemark"
            clearable
            placeholder="输入收款说明（选填）"
            type="textarea"
            class="cd-cell-sk-cls"
            :rows="3"
            show-word-limit
            maxlength="255"
          />
        </div>
        <div>
          <div style="float:left;margin-top:10px;">
            合计：{{ cdListSum }}
          </div>
          <el-button @click="closeCdDialog">取消</el-button>
          <el-button type="primary" :loading="ocrSubBtn" @click="submitCdList">批量提交</el-button>
        </div>

      </span>
    </el-dialog>
    <el-dialog
      :visible.sync="undefinedComsShow"
      title="选择相关付款账户"
    >
      <div>
        <div v-for="(item,index) in sortUndefinedReveComs" :key="index" style="margin-top:10px;">

          <div v-if="undefinedReveComs[item].list && undefinedReveComs[item].list.length">
            <div style="display: flex;align-items:center;">
              <div>
                请选择相关付款账户：<strong>{{ item }}</strong>
              </div>
              <div style="margin-left:10px;">
                <!-- 少于三个单选 -->
                <div v-if="undefinedReveComs[item].list.length<4">
                  <el-radio v-for="(subitem,subindex) in undefinedReveComs[item].list" :key="subindex" v-model="undefinedReveComs[item].selidx" :label="subindex">{{ subitem[undefinedReveComs[item].key] }}</el-radio>
                </div>
                <div v-else>
                  <el-select v-model="undefinedReveComs[item].selidx" placeholder="请选择">
                    <el-option v-for="(subitem,subindex) in undefinedReveComs[item].list" :key="subindex" :label="subitem[undefinedReveComs[item].key]" :value="subindex" />
                  </el-select>
                </div>
              </div>
            </div>
          </div>
          <div v-else>
            未找到相关付款账户：<strong>{{ item }}</strong>
            <br>
            <i v-if="switchComp.costType == 1 && switchComp.receiveCompany != 20" style="margin-left:5px;">请联系业务部门相关负责人，创建<strong>{{ item }}</strong>客户信息</i>
            <i v-else style="margin-left:5px;">请前往综合管控平台/<a :href="orUrl+'/sysExternalCustomer/sysExternalCustomerList'" target="_blank" style="color:#13ce66;">客户管理</a>创建<strong>{{ item }}</strong>客户信息</i>
          </div>

        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="undefinedComsShow = false">取 消</el-button>
        <el-button type="primary" :loading="undefinedComsLoading" @click="upUndefinedComs">确 定</el-button>
      </span>
    </el-dialog>
    <el-dialog
      :visible.sync="dialogYacc"
      title="生成凭证"
      width="80%"
      :before-close="handleYacc"
    >
      <span>&nbsp;&nbsp;账期:&nbsp;<el-date-picker
        v-model="monthDateAcc"
        type="month"
        value-format="yyyyMM"
        placeholder="选择日期">
      </el-date-picker>
      </span>
      <Subacc :tableData="tableDataAcc" :paymentType="paymentType" @update:tableData="v=> tableDataAcc = v"  :accounts="ledgerAccountList" @ledgerAccount="v=> ledgerAccount = v"   :classList="kjClassList"></Subacc>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogYacc = false">取消</el-button>
          <el-button type="primary" @click="saveYacc()">
            提交
          </el-button>
        </div>
      </template>
    </el-dialog>
    <el-dialog
    :visible.sync="dialogTarnShow"
    title="查看凭证"
    width="65%"
    :before-close="closeTarnShow"
  >
    <resultTrans v-for="item in showTableTrans" :tableData="item" :classList="kjClassList"></resultTrans>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialogTarnShow = false">关闭</el-button>
      </div>
    </template>
  </el-dialog>
  </div>
</template>

<script>
import { billByImgUrl } from '@/api/system/ocr'
import 'vxe-table/lib/style.css'
import { getAllCompany } from '@/api/business/wxDepartmentapi'
import PanelSelection from '@/components/PanelSelection/panelSelection'
import SimplePanelSelection from '@/components/PanelSelection/simplePanelSelection'
import { savetExternalAccountByCustomerNameAndBankAccount } from '@/api/business/sysExternalAccount'
import {
  addReceiveWater, getBusinessReceiveDetail, addReceiveWaterSubList, updateAddReceiveWater,
  getReceive, updateAddReceiveWaterSubs, isReceiveWaterExist,
  getReceiveWater,
  updateReceiveStatus, getReceiveWaterSubByParentId,
  updateReceiveWaterStatus
} from '@/api/system/receiveWater'
import { selectbycode, selectbyDeptId, selectbyId, selectPersonalAccount } from '@/api/system/sysAccount'
import { getDictionaryList, saveSysCustomerSecondlevel, saveSysSupplierName } from '@/api/system/baseInit'
import { selectAccount } from '@/api/business/accountMoneyForMonthapi'
import dayjs from 'dayjs'
import { fmtDictionary } from '@/utils/util'
import { saveVoucherReceive } from '@/api/system/processVoucherRecord'
import { UPLOAD_URL, OA_BASE_URL } from '@/utils/config'
import { judgeBillByJson, judgeBillByStr } from '@/utils/ocr'
import Cookies from 'js-cookie'
import ClipboardJS from 'clipboard'
import ImageRotate from '@/components/ImageRotate/index'
import VXETablePluginExportXLSX from 'vxe-table-plugin-export-xlsx'
import VXETable from 'vxe-table'
VXETable.use(VXETablePluginExportXLSX)
import currency from 'currency.js'
import {onlyTreeList} from '@/api/system/fundsClassification'
import FcItem from '@/views/components/fcitem/fcitem'
import {getReceiptVoucher,getVoucherStatusByReceIds,getHaokjSub,saveVoucher,getAcctgTransListByCode} from '@/api/business/processapi'
import Subacc from '@/views/system/businessPayment/subacc.vue'
import resultTrans from '@/views/system/businessPayment/resultTrans.vue';
import AccModel from '@/components/AccModel/index.vue'
const CD_TIEX_KEY=5; // 承兑贴现 code 5

export default {
  name: 'Index',
  components: { PanelSelection, SimplePanelSelection, ImageRotate,FcItem,Subacc,resultTrans,AccModel },
  data() {
    return {
      accountBlankList:[],
      cdHtmlStr:'',
      isShowSelCgwlKh: false, // 是否显示成功物流客户选择
      isRequireCustomSel: false,
      costDefName: '',
      isAutoType: '1', // 无 null 1 手动  2：自动
      cdTypeList: [
        {
          value: '银承',
          label: '银承'
        },
        {
          value: '商承',
          label: '商承'
        }
      ],
      cdZrTypeList: [
        {
          value: '能转让',
          label: '能转让'
        },
        {
          value: '不能转让',
          label: '不能转让'
        }
      ],
      autoTypeList: [
        {
          value: '1',
          label: '手工录入'
        },
        {
          value: '2',
          label: '批量导入'
        }
      ],
      // transferredList: [
      //   {
      //     value: '1',
      //     label: '能转让'
      //   },
      //   {
      //     value: '2',
      //     label: '不能转让'
      //   }
      // ],
      dialogImageUrlOcr: '',
      dialogVisibleOcr: false,
      stlyeContOcr: {
        width: '50%'
      },
      ocrListShow: false,
      toolbarButtons: [],
      upload: UPLOAD_URL,
      applyStatusList: [
        {
          code: 0,
          value: '未核销'
        },
        {
          code: 1,
          value: '已核销'
        }
      ],
      queryParams: {
        receiveType: null,
        receiveCompany: null,
        customer: null,
        receiveStatus: null,
        createTime: null,
        pageSize: 10,
        pageNo: 1,
        costType: null,
        startTime: undefined,
        endTime: undefined,
        accid: null,
        fundsClassId:null
      },
      ticketTypeList: [
        { code: 1, value: '银行承兑' },
        { code: 2, value: '商业承兑' }
      ],
      typeList: [],
      total: 0,
      loading: false,
      dataList: [],
      showApply: false,
      switchComp: {
        costType: null,
        show: true,
        balance: 0,
        customerId: undefined,
        loading: false,
        topUpRemark: undefined,
        customerName: undefined,
        typeSelect: 0,
        receiveCompany: null,
        receviceCompanyName: null,
        receiveType: undefined,
        ticketNumber: null,
        paymentBank: null,
        paymentAccount: null,
        receiveBank: null,
        receiveAccount: null,
        ticketType: null,
        expireDate: null,
        moneyC: null,
        payUser: null,
        occurDate: dayjs(new Date()).format('YYYY-MM-DD'),
        isTransferred: null,
        votingDate: null,
        drawerName: null,
        acceptorName: null
      },
      receiveCompanyList: [],
      allCompanyList: [],
      myCompanyList: [],
      plateList: [],
      accountlist: [],
      customerList: [],
      customerListHS:[],
      supplierList: [],
      fdbizCustomerList: [],
      paymentTypeList: [],
      currentRow: null,
      dictionaryLists: [],
      submitObj: {},
      // tableData: [],
      // applicationForm: {},
      receiveList: [],
      showDetailApply: false,
      showBusinessDetailApply: false,
      receiveWater: {},
      verificationInfo: {},
      showAccount: false,
      waterDetail: {},
      showAccountlist: [],
      picList: [],
      fileList: [],
      uploadUrl: UPLOAD_URL,
      dialogImageUrl: '',
      dialogVisible: false,
      showPicApply: false,
      picUrl: null,
      detailObj: {},
      contractList: [],
      userCompanyIds: [],
      ocrList: [],
      ocrCdList: [],
      itemCdList: [],
      ocrSubBtn: false,
      ocrIng: false,
      cdListSum: 0,
      undefinedReveComs: {},
      undefinedComsShow: false,
      undefinedComsLoading: false,
      orUrl: OA_BASE_URL,
      fundsClassList: [],
      fundsSelItemId: [],
      fundsQueryId: [],
      isShowCg: false,
      receListData:{},
      dialogYacc: false,
      monthDateAcc: dayjs().format('YYYYMM'),
      tableDataAcc: [],
      kjClassList: [],
      dialogTarnShow: false,
      showTableTrans: [],
      ledgerAccount:'',
      ledgerAccountList:[],
      isCertModel:'1',
    }
  },
  computed: {
    isDiscountedAmountShow(){
      if(this.switchComp.costType == CD_TIEX_KEY){
        return true
      }
      return false
    },
    receiveComCode1020() {
      if (!this.isShowCg) {
        return []
      }
      if (this.switchComp.costType != 1) {
        return []
      }
      if (this.receiveCompanyList) {
        // allCompanyList
      // 只保留 网联 内帐，code 10 25
        return this.receiveCompanyList.filter(item=>item.code == 10 || item.code == 25)
      }
      return []
    },
    allCompanyList1020() {
      if (this.allCompanyList) {
        if (this.switchComp.costType == 1 && this.isShowCg) {
          // 排除 成功 8 山东 10 和盛 41
          // && item.deptId != 41
          return this.allCompanyList.filter(item => {
            return item.deptId != 8 && item.deptId != 10
          })
        }
        return this.allCompanyList
      }
    return []
    },
    sortUndefinedReveComs() {
      // 根据 是否有list 进行排序
      let list = []
      if (this.undefinedReveComs && Object.keys(this.undefinedReveComs).length) {
        list = Object.keys(this.undefinedReveComs).sort((a, b) => {
          if (this.undefinedReveComs[a].list && this.undefinedReveComs[a].list.length) {
            if (this.undefinedReveComs[b].list && this.undefinedReveComs[b].list.length) {
              return this.undefinedReveComs[a].list.length - this.undefinedReveComs[b].list.length > 0 ? -1 : 1
            }
            return -1
          }
          return 0
        })
      }
      return list
    },
    // cdListSum() {
    //   let sum = 0
    //   try {
    //     if (this.ocrCdList && this.ocrCdList.length > 0 && this.$refs.cdListTab && this.$refs.cdListTab.selection) {
    //       if(this.$refs.cdListTab && this.$refs.cdListTab.selection.length>0){
    //         this.$refs.cdListTab.selection.forEach(item => {
    //           // sum += this.priceToNum(item.billAmount)
    //             sum = currency(sum).add(this.priceToNum(item.billAmount))
    //           })
    //       }
    //       // else{
    //       //   this.ocrCdList.forEach(item => {
    //       // // sum += this.priceToNum(item.billAmount)
    //       //   sum = currency(sum).add(this.priceToNum(item.billAmount))
    //       // })
    //       // }
    //     }
    //   } catch (e) {
    //     console.log(e)
    //   }
    //   return currency(sum, { symbol: '¥', precision: 2 }).format()
    // },
    defCodeCostType() {
      if (this.switchComp.costType) {
        return this.switchComp.costType + ''
      }
      return ''
    },
    totalDebit() {
      return this.tableDataAcc.reduce((sum, row) => sum + Number((row.debit||0)), 0);
    },
    totalCredit() {
      return this.tableDataAcc.reduce((sum, row) => sum + Number((row.credit||0)), 0);
    },
  },
  created() {
    this.loadFundsClassList()
    this.loadAllCompany()
    var list = Cookies.get('deptIds')
    if (list) {
      this.userCompanyIds = JSON.parse(Cookies.get('deptIds'))
      console.info(this.userCompanyIds)
    }
    var getSupplier = this.$store.dispatch('data/getSupplierListSaveInVuex')
    var getCustomer = this.$store.dispatch('data/getCustomerListSaveInVuex')
    var getCustomerHS = this.$store.dispatch('data/getCustomerListSaveInVuexHs').catch(e=>console.log('1603',e))
    var getFdbizCustomer = this.$store.dispatch('data/getFdbizCustomerListSaveInVuex')
    var getCompany = this.$store.dispatch('data/getCompanyListSaveInVuex')
    var getPlate = this.$store.dispatch('data/getPlateListSaveInVuex')
    var dic = getDictionaryList('contract_company,settlement_type,fdbiz_cost_type')
    Promise.all([getSupplier, getCustomer, dic, getCompany, getFdbizCustomer, getPlate,getCustomerHS]).then(values => {
      let res = values[0]
      if (res) {
        this.supplierList = res.supplierList
      }
      res = values[1]
      if (res) {
        this.customerList = res.customerList
      }
      res = values[2]
      if (res) {
        this.dictionaryLists = res.map
        this.receiveCompanyList = res.map['contract_company']
        this.paymentTypeList = res.map['settlement_type']
        this.typeList = res.map['fdbiz_cost_type']
      }
      res = values[3]
      if (res) {
        if (this.userCompanyIds && this.userCompanyIds.length > 0) {
          var companys = res.companyList
          var ids = this.userCompanyIds.map(item => { return Number(item) })
          this.allCompanyList = companys.filter(tmp => ids.includes(tmp.deptId))
        }
        if (!this.allCompanyList || this.allCompanyList.length <= 0) {
          this.allCompanyList = res.companyList
        }
        if (this.allCompanyList && this.allCompanyList.length > 0 ) {
          this.allCompanyList.forEach(item => {
            if (item.deptId == 8 ) {
              this.isShowCg = true
            }
          })
        }
      }
      res = values[4]
      if (res) {
        this.fdbizCustomerList = res.fdbizCustomerList
      }
      res = values[5]
      if (res) {
        this.plateList = res.plateList
      }
      res = values[6]
      if (res) {
        this.customerListHS = res.customerList
      }
      // this.topage()
      this.getBlankAccount()
    }).catch(err => {
      console.log('1657',err)
    })
  },
  mounted() {
    document.addEventListener('paste', this.onPasteUpload)
    var clipboard = new ClipboardJS('.cp-cls-tip')
    clipboard.on('success', () => {
      this.$message.success('复制成功')
    })
    this.loadKeMuList()
  },
  beforeDestroy() {
    document.removeEventListener('paste', this.onPasteUpload)
  },
  methods: {
    updateAccModel(type){
      this.isCertModel = type
      this.getVoucherStatusByReceIds(this.dataList.map(i=>i.id).join(','))
    },
    saveYacc() {
      // save
      // this.saveAccTable('1', () => {
      this.saveAccTable(this.ctypeCode, () => {
        this.handleYacc()
      })
    },
    saveAccTable(type = '1', func = undefined) {
      // 验证数据，
      if (this.totalCredit != this.totalDebit) {
        this.$message.error('借方金额与贷方金额不一致')
        return
      }
      // if (type == '-1' && this.totalCredit > this.paymentTotal) {
      //   this.$message.error('借贷金额大于付款金额')
      //   return
      // }
      const loading = this.$loading({
        lock: true,
        text: 'Loading',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.2)'
      })

      saveVoucher(this.tableProcessIdAcc, type, this.monthDateAcc,this.tableDataAcc,'',this.ledgerAccount).then((data) => {
        if (data.result) {
          this.$message.success('保存成功')
          func && func()
        } else {
          this.$message.error(data.msg || '保存失败')
        }
        getVoucherStatusByReceIds(this.tableProcessIdAcc).then(res => {
          if (res && res.data) {
            this.receListData = Object.assign({}, this.receListData, res.data)
          }
        })

        loading.close()
      }).catch((err) => {
        this.$message.error('保存失败')
        loading.close()
      })
    },
    loadKeMuList() {
      getHaokjSub().then(res => {
        if (res && res.data) {
          this.kjClassList = res.data || []
          this.upSubIdByFind()
        }
      })
    },
    monthDateStr() {
      return dayjs(this.monthDateAcc).format('YYYY年MM月')
    },
    handleYacc() {
      this.dialogYacc=false
    },
    getVoucherStatusByReceIds(ids) {
      this.receListData={}
      getVoucherStatusByReceIds(ids).then(res=>{
        if (res && res.data) {
          this.receListData = res.data
        }
      })
    },
    loadTranListByProcessIdAndCode(pid, code,func=undefined) {
      const loading = this.$loading({
        lock: true,
        text: 'Loading',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.2)'
      })
      this.showTableTrans = []
      getAcctgTransListByCode(pid, code).then(res => {
        this.showTableTrans = res.result
        func && func()
      }).finally(() => {
        loading.close()
      })
    },
    closeTarnShow() {
      this.dialogTarnShow = false
    },
    showTranByCode(data,code) {
      this.loadTranListByProcessIdAndCode(data.id, code, () => {
        this.dialogTarnShow = true
      })
    },
    createReceCertById(id,code,type) {
      // 3 承兑
      console.log(type)
      if(!code){
        return
      }
      this.paymentType=type
      this.ctypeCode = code
      this.getReceiptVoucher(id)
    },
    getReceiptVoucher(id) {
      const loading = this.$loading({
        lock: true,
        text: 'Loading',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.2)'
      })
      this.tableDataAcc = []
      this.tableProcessIdAcc = id
      getReceiptVoucher(id).then(res => {
      console.log(res)
        this.tableDataAccCode(res)
        this.dialogYacc = true
      }).finally(() => {
        loading.close()
      })
    },
    tableDataAccCode(res) {
        const list = res && res.data ? res.data : []
        // findSub
        this.tableDataAcc = list || []
        this.ledgerAccountList = res && res.accounts
        // this.tableDataAcc = list.map(item => {
        //   return {
        //     // summary: '计提'+this.monthDateStr+'工资及社保公积金',
        //     ...item
        //   }
        // })
        // this.dialogYacc = true
        this.upSubIdByFind()
    },
    upSubIdByFind() {
      if (this.tableDataAcc && this.tableDataAcc.length > 0 && this.kjClassList && this.kjClassList.length > 0) {
        // tableDataAcc 中 findSub 获取 kjClassList glAccountName 相等 ，treePath(2241^********^) = subject (2241)^
        //tableDataAcc -> findSub,subject
        // kjClassList -> glAccountName,treePath, glAccountCode
           // 承兑 现金
           if (3 == this.paymentType) {
           this.tableDataAcc[0].findSubId = '1121'
          this.tableDataAcc[0].subject = '1121'
          this.tableDataAcc[this.tableDataAcc.length - 1].findSubId = '1122'
          this.tableDataAcc[this.tableDataAcc.length - 1].subject = '1122'
         }
          if (1 == this.paymentType) {
            this.tableDataAcc[0].findSubId = '1001'
            this.tableDataAcc[0].subject = '1001'
             this.tableDataAcc[this.tableDataAcc.length - 1].findSubId = '5001'
            this.tableDataAcc[this.tableDataAcc.length - 1].subject = '5001'
          }

        for (let i = 0; i < this.tableDataAcc.length; i++) {
          if (this.tableDataAcc[i].findSub && !this.tableDataAcc[i].findSubId) {
            try {
              const fundsCode = this.kjClassList.find(item => item.treePath.startsWith(this.tableDataAcc[i].subject + '^')  && item.glAccountName == this.tableDataAcc[i].findSub)
              if (fundsCode) {
                this.tableDataAcc[i].findSubId = fundsCode.glAccountCode
                this.tableDataAcc[i].subject = fundsCode.glAccountCode
              }
            } catch (error) {
              console.log('error', error)
            }
          }
        }



      }
    },
    clkFundsItem(item) {
      console.log('clkFundsItem', item)
      const data = {
        code: item.typeCode && item.typeCode=='wuliu'? 1 : -1,
      }
      this.costType(data)
    },
     rotateImg(index, tye) {
      if (this.$refs.simgs[index].style.transform.indexOf('rotate') > -1) {
        const r = this.$refs.simgs[index].style.transform
        let r1 = r.substring(r.indexOf('(') + 1, r.indexOf('deg'))
        if (tye === 'left') {
          r1 = Number(r1) - 90
        } else {
          r1 = Number(r1) + 90
        }
        this.$refs.simgs[index].style.transform = 'rotate(' + r1 + 'deg)'
      } else {
        this.$refs.simgs[index].style.transform = 'rotate(' + (tye == 'left' ? '-90deg' : '90deg') + ')'
      }
    },
    substrPulx(str){
      if (!str || str.trim() == '') {
        return ''
      }
      str = str.trim()
      str = str.replace(/\s+/g, '')
      return `(${str.substr(-4)})`
    },
    getBlankAccount() {
      console.log(this.queryForm)
      // if (!this.queryForm.companyid){
      //   this.queryForm.accid = undefined
      // }
      let cid=null
      if (this.queryParams.receiveCompany) {
        let myitem = this.myCompanyList.find(item=>item.label==this.queryParams.receiveCompany)
        if (myitem) {
          cid = myitem.deptId
        }
      }
      selectAccount(null, cid).then(res => {
        this.accountBlankList = res.list
      })
      this.topage(1)
    },
    fmtHtmlStr() {
      if (!this.cdHtmlStr) {
        // tip
        this.$message.error('请先粘贴内容')
        return
      }
      const parser = new DOMParser()
      const doc = parser.parseFromString(this.cdHtmlStr,'text/html')

     // const keyval={
      //   1:keyarray[0], // 票号
      //   2:keyarray[1], //票据类型
      //   3:keyarray[2], //能否转让
      //   4:keyarray[3], //出票日
      //   5:keyarray[4], //到期日
      //   // 票面金额
      //   // 出票人
      //   // 承兑人
      //   // 背书人
      // }
      const keyarray = ['billNo','billType','isTransferred','issueDate','dueDate','billAmount','issuer','acceptor','underwriter']

      // 跳过第一行，跳过数组长度不够
      // let trs = doc.getElementsByTagName('tr')
      // console.log(trs)
      // console.log('----')
      const listcd = []
      const trlist = doc.querySelectorAll('tr')
      // 跳过第一行，跳过数组长度不够
      for (let i = 1; i < trlist.length; i++) {
        const tdlist = trlist[i].children
        // 判断长度
        if (tdlist.length <= 10) {
          continue
        }
        const itemdata = {}
        for (let j = 1; j < tdlist.length && j < 10;  j++) {
          let val = ''
          // 判断子数据长度
          if (tdlist[j].children.length > 1) {
            // 判断 display
            for (let z = 0; z < tdlist[j].children.length; z++) {
              if (tdlist[j].children[z].style.display != 'none') {
                // console.log('index',j,tdlist[j].children[z].textContent)
                val = tdlist[j].children[z].textContent
                break
              }
            }
          } else {
            // console.log('index',j,tdlist[j].textContent )
            val = tdlist[j].textContent
          }
          // 删除空格
          val = val && val.replaceAll(' ','') || ''
          itemdata[keyarray[j-1]] = val
        }
        listcd.push(itemdata)
      }
      console.log(listcd)
      if (listcd.length > 0) {
        this.openCdListView(listcd)
      }else{
        this.$message.error('粘贴内容不符合要求')
      }
    },
    setItemObj(item, id, name) {
      item.customerId = id
      item.customerName = name
    },
    cdListSelectChange(selection) {
      console.log('sel chan', selection)
      let sum = 0
      if (selection && selection.length) {
        selection.forEach(item => {
          sum = currency(sum).add(this.priceToNum(item.billAmount))
        })
      }
      this.cdListSum = currency(sum, { symbol: '¥', precision: 2 }).format()
    },
    selTransfer(item) {
      if (this.switchComp.isTransferred == item.value) {
        // this.switchComp.isTransferred = null
        this.$set(this.switchComp, 'isTransferred', null)
      } else {
        // this.switchComp.isTransferred = item.value
        this.$set(this.switchComp, 'isTransferred', item.value)
      }
    },
    errTipByOcr(msg) {
      this.$message.error(msg)
      this.ocrSubBtn = false
    },
    closeCdDialog() {
      // this.switchComp.costType = null
      // this.switchComp.receiveType = null
      // this.currentRow = null
      this.ocrListShow = false
      // this.accountlist = []
    },
    submitCdList() {
      this.ocrSubBtn = true
      if (!this.switchComp.costType) {
        this.errTipByOcr('请选择收款类型')
        return
      }
      if (!this.switchComp.receiveType) {
        this.errTipByOcr('请选择账户类型')
        return
      }
      if (!this.currentRow || !this.currentRow.id) {
        this.errTipByOcr('请选择收款账户')
        return
      }
      // if (!this.switchComp.customerId) {
      //   this.errTipByOcr('请选择付款客户')
      //   return
      // }
      // 根据承兑批量增加
      if (!this.$refs.cdListTab.selection || this.$refs.cdListTab.selection.length == 0) {
        this.errTipByOcr('请选择相关承兑汇票')
        return
      }

      const cdList = this.$refs.cdListTab.selection
      console.log('cdlist', cdList)
      // 请选择付款客户
      const customerIdItem = cdList.find(item => !item.customerName)
      if (customerIdItem) {
        this.errTipByOcr(`请输入${customerIdItem.billNo}背书人`)
        return
      }
      // 非空检查
      // const errItem = cdList.find(item => !item.fileList || item.fileList.length == 0)
      // if (errItem) {
      //   this.errTipByOcr(`请上传${errItem.billNo}相关明细`)
      //   return
      // }
      // 金额
      const amountItem = cdList.find(item => item.billAmount == undefined)
      if (amountItem) {
        this.errTipByOcr(`请输入${amountItem.billNo}票面金额`)
        return
      }
      // 增加非同一个背书人验证
      const loading = this.$loading({
        lock: true,
        text: '数据提交中，请稍后！',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      })
      // 批量自动提交客户

      const list = []
      let isAsync = false
      for (let i = 0; i < cdList.length; i++) {
        const tmp = cdList[i]
        // 没有id 添加客户
        if (!tmp.customerId && tmp.customerName) {
          isAsync = true
          this.addCustomerByName(tmp.customerName).then(res => {
            if (res) {
              tmp.customerId = res
              this.addCdTmpToList(tmp, list)
            } else {
              loading.close()
              this.errTipByOcr('添加客户失败,请稍后再试')
            }
          }).catch(() => {
            loading.close()
            this.errTipByOcr('添加客户失败,请稍后再试')
          }).finally(() => {
            if (list.length === cdList.length) {
              this.pushReceiveWaterList(list, loading)
            }
          })
          continue
        }
        // 循环
        this.addCdTmpToList(tmp, list)
      }
      if (isAsync && list.length !== cdList.length) {
        return
      }
      this.pushReceiveWaterList(list, loading)
    },
    pushReceiveWaterList(list, loading) {
      // 判断是否付款人一致，不一致 不提交
      const sysCustomerId = list[0].sysCustomerId
      const isExCust = list.find(item => sysCustomerId != item.sysCustomerId)
      if (isExCust) {
        this.errTipByOcr('承兑汇票不是同一个客户')
        loading.close()
        return
      }
      console.log(list)
      const data = {
        list: list,
        picList: this.picList && this.picList.toString() || ''
      }
      if (this.switchComp.id) {
        data.id = this.switchComp.id
        updateAddReceiveWaterSubs(data).then(res => {
          if (res.resultCode == '0') {
            this.$message.success('提交成功')
            this.ocrListShow = false
            this.showApply = false
            this.topage()
          } else {
            this.$message.error(res.resultMsg)
          }
        }).finally(() => {
          this.ocrSubBtn = false
          loading.close()
        })
      } else {
        addReceiveWaterSubList(data).then(res => {
          if (res.resultCode == '0') {
            this.$message.success('提交成功')
            this.ocrListShow = false
            this.showApply = false
            this.topage()
          } else {
            this.$message.error(res.resultMsg)
          }
        }).finally(() => {
          this.ocrSubBtn = false
          loading.close()
        })
      }
    },
    addCdTmpToList(tmp, list) {
      const data = {
        id: tmp.id || null,
        custorOrSupplier: tmp.typeSelect,
        balance: this.priceToNum(tmp.billAmount),
        remark: this.switchComp.topUpRemark,
        sysCustomerId: tmp.customerId,
        accountId: this.currentRow && this.currentRow.id,
        receviceType: this.switchComp.receiveType,
        receviceCompany: this.switchComp.receiveCompany,
        ticketNumber: tmp.billNo,
        customerName: tmp.customerName,
        picList: tmp.fileList && tmp.fileList.toString() || '',
        receiveBank: this.switchComp.receiveBank,
        receiveAccount: this.switchComp.receiveAccount,
        ticketType: /商|业/.test(tmp.billType) ? 2 : 1,
        expireDate: tmp.dueDate,
        // costType: this.switchComp.costType,
        costType: this.isShowSelCgwlKh ? this.switchComp.costType : -1,
        receviceCompanyName: this.switchComp.receviceCompanyName,
        // applyStatus: this.switchComp.costType != 1 ? 1 : 0,
        applyStatus: !this.isShowSelCgwlKh ? 1 : 0,
        acceptorName: tmp.acceptor,
        drawerName: tmp.issuer,
        votingDate: tmp.issueDate,
        isTransferred: tmp.isTransferred,
        occurDate: this.switchComp.occurDate,
        fundsClassId: this.fundsSelItemId && this.fundsSelItemId.length > 0 ? this.fundsSelItemId[this.fundsSelItemId.length - 1] : ''
      }
      list.push(data)
    },
    ocrClk() {
      if (this.switchComp.id) {
        // 修改不执行
        return
      }
      if (!this.picList || this.picList.length == 0) {
        this.$message.error('请先上传图片')
        return
      }

      // 识别中
      this.ocrIng = true
      // 识别图片
      this.ocrByUrl(this.picList[0]).then(data => {
        console.log('ocr data', data)
        if (!data) {
          this.$message.error('抱歉，未识别成功')
          return
        }
        // 清空 付款公司
        this.ocrPaymentCompany = ''
        this.ocrPaymentBank = ''
        this.ocrPaymentAccount = ''
        // 收款公司
        this.ocrReceiveCompany = ''

        // 类型判断
        if (data.type == 0) {
        // 暂不支持此类型
          this.$message.error('抱歉，暂不支持此类型识别')
          return
        }
        // 类型 code 现金 1 网银 2 汇票 3
        if (!data.type) {
          return
        }
        this.switchComp.receiveType = data.type + 1
        if (data.type == 2 && data.isCdList) {
        // 打开承兑列表
          this.openCdListView(data.cdlist)
          return
        }
        // 清空录入方式
        // this.isAutoType = '1'

        // 汇票
        if (data.type == 2) {
          if (data.billType == '银行汇票') {
            this.switchComp.ticketType = 1
          }
          if (data.expireDate) {
            this.switchComp.expireDate = this.dateFmt(data.expireDate)
          }
          if (data.billNo) {
            this.switchComp.ticketNumber = data.billNo
          }
          if (data.votingDate) {
            this.switchComp.votingDate = this.dateFmt(data.votingDate)
          }
          if (data.drawerName) {
            this.switchComp.drawerName = data.drawerName
          }
          if (data.acceptorName) {
            this.switchComp.acceptorName = data.acceptorName
          }
          if (data.isTransferred) {
            this.switchComp.isTransferred = data.isTransferred
          }
        }
        if (data.receiptAmount) {
          this.switchComp.balance = this.moneyFmt(data.receiptAmount)
          this.NumberToChinese(this.switchComp.balance)
        }
        if (data.receivablesCompany) {
          this.ocrReceiveCompany = data.receivablesCompany
        }
        // 付款客户名称
        if (data.paymentCompany) {
          this.ocrPaymentCompany = data.paymentCompany
          this.ocrPaymentBank = data.paymentBank
          this.ocrPaymentAccount = data.paymentAccount
          // console.log('ocr company', this.ocrPaymentCompany)
          // 自动填充
          // 内帐
          if (this.switchComp.receiveCompany && parseInt(this.switchComp.receiveCompany) === 25) {
            this.switchComp.payUser = this.ocrPaymentCompany
          } else {
            this.switchComp.customerName = this.ocrPaymentCompany
          }
          if (this.switchComp.receiveType == 2) {
            this.switchComp.paymentBank = this.ocrPaymentBank
            this.switchComp.paymentAccount = this.ocrPaymentAccount
          }
          // 自动填充
          this.funSetCompany(data.paymentCompany)
          // if (this.switchComp.receiveType ) {
          //   // 非物流业务
          //   if(this.switchComp.costType != 1){
          //     this.setCustomerByOcr(this.fdbizCustomerList, 'companyName', this.switchComp.receiveType != 1)
          //   }else{
          //     // 和盛

          //      if (this.switchComp.receiveCompany == 20) {
          //       this.setCustomerByOcr(this.fdbizCustomerList, 'companyName', true)
          //     } else {
          //       // console.log('sel select', this.switchComp.typeSelect)
          //       this.setCustomerByOcr(this.switchComp.typeSelect == 0 ? this.customerList : this.supplierList, this.switchComp.typeSelect == 0 ? 'fullName' : 'name', false)
          //     }
          //   }

          // }
        // // 查询
        // const obj = this.fdbizCustomerList.find(item => item.companyName == data.paymentCompany)
        // if (obj) {
        //   this.switchComp.customerId = obj.id
        //   this.switchComp.customerName = obj.companyName
        //   this.switchComp.paymentBank = data.paymentBank || obj.bankName
        //   this.switchComp.paymentAccount = data.paymentAccount || obj.bankAccount
        // }
        }
      }).finally(() => {
        this.ocrIng = false
      })
    },
    dateFmt(date) {
      if (date) {
        return date.replace(/年|月/g, '-').replace(/日/g, '')
      }
      return date
    },
    priceToNum(price) {
      var num = 0
      if (price) {
        num = price.replace(/[^\d\.]/g, '')
      }
      return num
    },
    upUndefinedComs() {
      // 判断是否选择
      if (!this.undefinedReveComs || Object.keys(this.undefinedReveComs).length == 0) {
        this.undefinedComsShow = false
        return
      }
      this.undefinedComsLoading = true
      this.ocrCdList = this.ocrCdList.map(item => {
        if (this.undefinedReveComs.hasOwnProperty(item.underwriter)) {
          const tmpCom = this.undefinedReveComs[item.underwriter]
          if (tmpCom.selidx !== undefined) {
            item.customerId = tmpCom.list[tmpCom.selidx][tmpCom.id]
            item.customerName = tmpCom.list[tmpCom.selidx][tmpCom.key]
          }
        }
        return item
      })
      this.undefinedComsLoading = false
      this.undefinedComsShow = false
    },
    listFilterOne(list, key, id) {
      if (list.length == 1) {
        return list[0]
      }
      const s = list.find(item => item[key] == id)
      if (s) {
        return s
      } else {
        return list[0]
      }
    },
    funSetCompany(cname) {
      // 默认customerList
      const tmpKey = !this.switchComp.costType || !this.switchComp.receiveCompany ? '' : this.switchComp.costType == 1 && this.switchComp.receiveCompany != 20 ? 'fullName' : 'companyName'
      const tmpComList = !this.switchComp.costType || !this.switchComp.receiveCompany ? [] : this.switchComp.costType == 1 && this.switchComp.receiveCompany != 20 ? this.customerList : this.switchComp.costType == 1 && this.switchComp.receiveCompany ==41 ? this.customerListHS: this.fdbizCustomerList
      // 自动判断 船公司
      const tmpSup = !this.switchComp.costType || !this.switchComp.receiveCompany ? [] : this.switchComp.costType == 1 && this.switchComp.receiveCompany != 20 && this.supplierList || []
      let filList = tmpComList.filter(sub => cname==sub[tmpKey])

      if (!filList || filList.length == 0) {
        filList = tmpComList.filter(sub => new RegExp(cname).test(sub[tmpKey]))
      }

      if (filList && filList.length > 0) {
        const fitem = this.listFilterOne(filList, 'paymentBank', this.ocrPaymentBank)
        const typeSelect = undefined
        // 修改船公司
        if (this.switchComp.costType == 1 && this.switchComp.receiveCompany != 20) {
          typeSelect == 0
        }
        this.setCustIdAndNameOrTypeSel(fitem.id, fitem[tmpKey], fitem.bankName, fitem.bankAccount, typeSelect)
      } else {
         filList = tmpSup.filter(sub => cname==sub.name)
        if (!filList || filList.length == 0) {
            filList = tmpSup.filter(sub => new RegExp(cname).test(sub.name))
         }

        // const t = tmpSup.find(sub => sub.name === item.underwriter)
        if (filList && filList.length > 0) {
          const fitem = this.listFilterOne(filList, 'paymentBank', this.ocrPaymentBank)
          const typeSelect = undefined
          // 修改船公司
          if (this.switchComp.costType == 1 && this.switchComp.receiveCompany != 20) {
            typeSelect == 1
          }
          this.setCustIdAndNameOrTypeSel(fitem.id, fitem[tmpKey], fitem.bankName, fitem.bankAccount, typeSelect)
        }
      }
    },
    setCustIdAndNameOrTypeSel(id, name, bankName, bankAccount, type = undefined) {
      this.switchComp.customerId = id
      this.switchComp.customerName = name
      if (this.switchComp.receiveType == 2) {
        this.switchComp.paymentBank = this.ocrPaymentBank || bankName
        this.switchComp.paymentAccount = this.ocrPaymentAccount || bankAccount
      } else {
        this.switchComp.paymentBank = null
        this.switchComp.paymentAccount = null
      }
      if (type != undefined) {
        this.switchComp.typeSelect = type
      }
    },
    openCdListView(list) {
      // 承兑列表
      this.undefinedReveComs = {}
      this.ocrList = []
      this.ocrCdList = []
      // this.showApply = false
      this.ocrListShow = true
      let tmpCom = null // 缓存
      let supTmpCom = null
      const noFindCompNames = {}

      // 默认customerList
      const tmpKey = !this.switchComp.costType || !this.switchComp.receiveCompany ? '' : this.switchComp.costType == 1 && this.switchComp.receiveCompany != 20 ? 'fullName' : 'companyName'
      const tmpComList = !this.switchComp.costType || !this.switchComp.receiveCompany ? [] : this.switchComp.costType == 1 && this.switchComp.receiveCompany != 20 ? this.customerList : this.switchComp.costType == 1 && this.switchComp.receiveCompany == 41 ? this.customerListHS: this.fdbizCustomerList
      // 自动判断 船公司
      const tmpSup = !this.switchComp.costType || !this.switchComp.receiveCompany ? [] : this.switchComp.costType == 1 && this.switchComp.receiveCompany != 20 && this.supplierList || []

      this.ocrCdList = list.filter(item => Object.keys(item).length > 3)
        .map(item => {
          item.billAmount = this.priceFmtAndRep(item.billAmount)
          item.issueDate = this.dateFmt(item.issueDate)
          item.dueDate = this.dateFmt(item.dueDate)
          item.fileList = []
          item.customerName = item.underwriter
          item.customerId = ''
          item.typeSelect = 0
          // 背书人underwriter 比对,为空 或 已记录在列表
          if (!item.underwriter || noFindCompNames.hasOwnProperty(item.underwriter)) {
            return item
          }
          let isRun = true
          if (tmpCom && tmpCom[tmpKey] === item.underwriter) {
            item.customerId = tmpCom.id
            item.customerName = tmpCom[tmpKey]
            isRun = false
          } else {
            // 模糊匹配，如果结果有多个 记录 并 选择
            let filList = tmpComList.filter(sub => item.underwriter==sub[tmpKey])
            if (!filList || filList.length == 0) {
              filList = tmpComList.filter(sub => new RegExp(item.underwriter).test(sub[tmpKey]))
            }
             if (filList && filList.length > 0) {
              if (filList.length == 1) {
                tmpCom = filList[0]
                item.customerId = tmpCom.id
                item.customerName = tmpCom[tmpKey]
              } else {
                noFindCompNames[item.underwriter] = { list: filList, key: tmpKey, id: 'id' }
              }
              isRun = false
            }
            // const t = tmpComList.find(sub => sub[tmpKey] === item.underwriter)
          }
          if (!isRun) {
            return item
          }
          // 未找到 去船公司找
          if (tmpSup && tmpSup.length > 0) {
            if (supTmpCom && supTmpCom.name === item.underwriter) {
              item.customerId = supTmpCom.id
              item.customerName = supTmpCom.name
              item.typeSelect = 1
            } else {
              let filList = tmpSup.filter(sub => item.underwriter==sub.name)
              if (!filList || filList.length == 0) {
                filList = tmpSup.filter(sub => new RegExp(item.underwriter).test(sub.name))
              }
              // const t = tmpSup.find(sub => sub.name === item.underwriter)
              if (filList && filList.length > 0) {
                if (filList.length == 1) {
                  supTmpCom = filList[0]
                  item.customerId = supTmpCom.id
                  item.customerName = supTmpCom.name
                } else {
                  noFindCompNames[item.underwriter] = { list: filList, key: 'name', id: 'id' }
                }
                item.typeSelect = 1
              }
            }
          }
          // 未找到付款公司 记录
          if (!item.customerId && !noFindCompNames.hasOwnProperty(item.underwriter)) {
            // noFindCompNames[item.underwriter] = {}
            // 默认值，未找到 之后统一自动添加
            item.customerName = item.underwriter
          }
          return item
        })
        // 未找到背书人公司 提示
      if (noFindCompNames && Object.keys(noFindCompNames).length) {
        this.undefinedReveComs = noFindCompNames
        this.undefinedComsShow = true
        // console.log('cms', this.undefinedReveComs)
      }
    },
    recPriceAndNum(price) {
      if (price.indexOf('.') != price.lastIndexOf('.')) {
        price = price.replace('.', '')
        return this.recPriceAndNum(price)
      }
      return price
    },
    priceFmtAndRep(price) {
      if (price) {
        price = this.priceToNum(price)
        // 多个. 识别
        if (price.indexOf('.') != price.lastIndexOf('.')) {
          // 删除多个. 保留最后一个
          price = this.recPriceAndNum(price)
        }
        // 格式化
        price = this.moneyFmt(price)
      }
      return price
    },
    upCdListPrice(val, row) {
      row.billAmount = this.moneyFmt(val)
      // 重新计算总额
      this.cdListSelectChange(this.$refs.cdListTab.selection)
    },
    setAllSelectByCdTable() {
      this.$nextTick(() => {
        if (!this.$refs.cdListTab.selection || this.$refs.cdListTab.selection.length === 0) {
          this.$refs.cdListTab.toggleAllSelection()
        }

        // let sum = 0
        // this.$refs.cdListTab.selection.forEach(item => {
        //   // sum += this.priceToNum(item.billAmount)
        //     sum = currency(sum).add(this.priceToNum(item.billAmount))
        //   })
        //  this.cdListSumNow = currency(sum, { symbol: '¥', precision: 2 }).format()
      })
      this.cdListSelectChange(this.ocrCdList)
    },
    fetchSuggestions(queryStr, cb) {
      let companys = this.myCompanyList
      if (queryStr) {
        companys = companys.filter(item => {
          return item.label.indexOf(queryStr) > -1
        })
      }
      cb(companys)
    },
    loadAllCompany() {
      getAllCompany().then(res => {
        this.myCompanyList = res.data
      })
    },
    openReceiveOrder(obj) {
      this.$router.push({ path: '/receiveOrder', query: { receiveWater: obj.id }})
    },
    NumberToChinese(money) {
      if (money == '') {
        return ''
      }
      console.log('money=123=>',money)
      money = money.replace(/[^\d\.]/g, '')
      console.log('money==>',money)
      var cnNums = new Array('零', '壹', '贰', '叁', '肆', '伍', '陆',
        '柒', '捌', '玖')
      // 基本单位
      var cnIntRadice = new Array('', '拾', '佰', '仟')
      // 对应整数部分扩展单位
      var cnIntUnits = new Array('', '万', '亿', '兆')
      // 对应小数部分单位
      var cnDecUnits = new Array('角', '分', '毫', '厘')
      // 整数金额时后面跟的字符
      var cnInteger = '整'
      // 整型完以后的单位
      var cnIntLast = '元'
      // 最大处理的数字
      var maxNum = 999999999999999.9999
      // 金额整数部分
      var integerNum
      // 金额小数部分
      var decimalNum
      // 输出的中文金额字符串
      var chineseStr = ''
      // 分离金额后用的数组，预定义
      var parts

      money = parseFloat(money)
      if (money >= maxNum) {
        // 超出最大处理数字
        return ''
      }
      if (money == 0) {
        chineseStr = cnNums[0] + cnIntLast + cnInteger
        return chineseStr
      }
      // 转换为字符串
      money = money.toString()
      if (money.indexOf('.') == -1) {
        integerNum = money
        decimalNum = ''
      } else {
        parts = money.split('.')
        integerNum = parts[0]
        decimalNum = parts[1].substr(0, 4)
      }
      // 获取整型部分转换
      if (parseInt(integerNum, 10) > 0) {
        var zeroCount = 0
        var IntLen = integerNum.length
        for (var i = 0; i < IntLen; i++) {
          var n = integerNum.substr(i, 1)
          var p = IntLen - i - 1
          var q = p / 4
          var m = p % 4
          if (n == '0') {
            zeroCount++
          } else {
            if (zeroCount > 0) {
              chineseStr += cnNums[0]
            }
            // 归零
            zeroCount = 0
            chineseStr += cnNums[parseInt(n)] +
                  cnIntRadice[m]
          }
          if (m == 0 && zeroCount < 4) {
            chineseStr += cnIntUnits[q]
          }
        }
        chineseStr += cnIntLast
      }
      // 小数部分
      if (decimalNum != '') {
        var decLen = decimalNum.length
        for (var i = 0; i < decLen; i++) {
          var n = decimalNum.substr(i, 1)
          if (n != '0') {
            chineseStr += cnNums[Number(n)] + cnDecUnits[i]
          }
        }
      }
      if (chineseStr == '') {
        chineseStr += cnNums[0] + cnIntLast + cnInteger
      } else if (decimalNum == '') {
        chineseStr += cnInteger
      }
      this.switchComp.moneyC = chineseStr
    },
    /**
     * 将金额转换为中文大写金额
     * @param {number|string} money - 要转换的金额，可以是数字或字符串
     * @returns {string} 转换后的中文大写金额
     */
    convertToChineseMoney(money) {
      // 处理输入为空或无效的情况
      if (!money && money !== 0) {
        return '';
      }
      // 去除非数字和小数点的字符并转换为浮点数
      money = parseFloat(money.toString().replace(/[^\d\.]/g, ''));
      if (isNaN(money)) {
        return '';
      }
      const cnNums = ['零', '壹', '贰', '叁', '肆', '伍', '陆', '柒', '捌', '玖'];
      const cnIntRadice = ['', '拾', '佰', '仟'];
      const cnIntUnits = ['', '万', '亿', '兆'];
      const cnDecUnits = ['角', '分', '毫', '厘'];
      const cnInteger = '整';
      const cnIntLast = '元';
      const maxNum = 999999999999999.9999;

      if (money >= maxNum) {
        return '';
      }
      if (money === 0) {
        return cnNums[0] + cnIntLast + cnInteger;
      }

      let [integerPart, decimalPart = ''] = money.toString().split('.');
      let chineseStr = '';

      // 处理整数部分
      if (parseInt(integerPart, 10) > 0) {
        let zeroCount = 0;
        const intLen = integerPart.length;
        for (let i = 0; i < intLen; i++) {
          const n = integerPart.substr(i, 1);
          const p = intLen - i - 1;
          const q = Math.floor(p / 4);
          const m = p % 4;
          if (n === '0') {
            zeroCount++;
          } else {
            if (zeroCount > 0) {
              chineseStr += cnNums[0];
            }
            zeroCount = 0;
            chineseStr += cnNums[parseInt(n)] + cnIntRadice[m];
          }
          if (m === 0 && zeroCount < 4) {
            chineseStr += cnIntUnits[q];
          }
        }
        chineseStr += cnIntLast;
      }

      // 处理小数部分
      decimalPart = decimalPart.substr(0, 4);
      if (decimalPart) {
        const decLen = decimalPart.length;
        for (let i = 0; i < decLen; i++) {
          const n = decimalPart.substr(i, 1);
          if (n !== '0') {
            chineseStr += cnNums[Number(n)] + cnDecUnits[i];
          }
        }
      } else {
        chineseStr += cnInteger;
      }

      return chineseStr;
    },
    handleBusinessPreviewOcr(file) {
      const url = file.url || file.response.url
      if (url.indexOf('.png') !== -1 || url.indexOf('.jpeg') !== -1 || url.indexOf('.jpg') !== -1) {
        this.dialogImageUrlOcr = url
        this.dialogVisibleOcr = true
      } else {
        window.open(url, '_blank')
      }
    },
    handleBusinessPreview(file) {
      // let { href } = this.$router.resolve({ path: file.response.url })
      if (file.response.url.indexOf('.png') !== -1 || file.response.url.indexOf('.jpeg') !== -1 || file.response.url.indexOf('.jpg') !== -1) {
        this.dialogImageUrl = file.response.url
        this.dialogVisible = true
      } else {
        window.open(file.response.url, '_blank')
      }
    },
    onPasteUpload(e) {
      console.log('--------')
      console.log(e)
      const upload = this.$refs.upload
      if (this.ocrListShow ||  !upload) {
        return
      }
      let isUpload = false
      const items = e.clipboardData.items
      for (const item of items) {
        if (item.type === 'image/png') {
          const file = new File([item.getAsFile()], new Date().getTime() + '.png')
          upload.handleStart(file)
          isUpload = true
        }
      }
      if (isUpload) {
        upload.submit()
      }
    },
    handleClose() {
      this.showPicApply = false
    },
    showPic(item) {
      if (!item.picList) {
        this.$message.warning('该流水暂无财务截图!')
        return
      }
      this.picUrl = null
      this.showPicApply = true
      this.picUrl = item.picList.split(',')
    },
    toremove(file, fileList) {
      if (file.response != undefined && file.response != null && file.response != '') {
        console.info(file, fileList)
        var list = Object.assign([], this.picList)
        this.picList = []
        for (var i = 0; i < list.length; i++) {
          if (list[i] != file.response.url) {
            this.picList.push(list[i])
          }
        }
      } else {
        console.info(file, fileList)
        var list = Object.assign([], this.picList)
        this.picList = []
        for (var i = 0; i < list.length; i++) {
          if (list[i] != file.url) {
            this.picList.push(list[i])
          }
        }
      }
    },
    handlePictureCardPreview(file) {
      this.dialogImageUrl = file.url
      this.dialogVisible = true
    },
    // 上传前对文件的大小的判断
    beforeAvatarUpload(file) {
      const isLt5M = file.size / 1024 / 1024 < 5
      if (!isLt5M) {
        this.$message.warning('上传图片大小不能超过 5 MB!')
      }
      return isLt5M
    },
    // 上传成功后的回调
    uploadSuccess(response, file, fileList) {
      this.picList.push(response.url)
      console.info(this.picList.toString())
      // 自动识别
      this.ocrClk()
    },
    uploadRowFileSuccess(response, row) {
      // 保存
      if (response && response.url) {
        // this.$set(row, 'fileList', [response.url])
        row.fileList = [response.url]
      }
    },
    ocrByUrl(imgurl) {
      this.ocrList = []
      return new Promise((resolve, reject) => {
        billByImgUrl(imgurl).then(res => {
          if (res) {
            try {
              res = JSON.parse(res)
              console.log('res====>',res)
              this.ocrList = res.StructuralItems || []
            } catch (e) {
              resolve()
            }
          }
          resolve(judgeBillByJson(res, this.switchComp.receiveType && this.switchComp.receiveType == 3 && this.isAutoType == 2 ? 1 : 0))
        }).catch(err => {
          reject(err)
        })
      })
    },
    uploadError(response, file, fileList) {
      this.$message.error(`上传失败，请重试！`)
    },
    handleRemove(file, fileList) {
      var thzs = this
      return this.$confirm('此操作将删除该截图, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        thzs.toremove(file, fileList)
        return true
      })
    },
    handleRemoveOcr(row) {
      var thzs = this
      return this.$confirm('此操作将删除该截图, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        row.fileList = []
        return true
      })
    },
    showAccounthandleClose() {
      this.showAccount = false
    },
    openShowAccount(obj) {
      console.info(obj)
      this.waterDetail = obj
      if (obj.accountId) {
        selectbyId(obj.accountId).then(res => {
          if (res) {
            this.showAccountlist = res.list
          }
        })
      }
      // 查询承兑列表
      this.itemCdList = []
      // 是否承兑
      if (obj.receviceType && obj.receviceType == 3) {
        getReceiveWaterSubByParentId({ id: obj.id }).then(res => {
          console.log('cdlist', res)
          if (res && res.data) {
            this.itemCdList = res.data
          }
        })
      }
      this.showAccount = true
    },
    saveVoucherReceive(obj) {
      this.$prompt('请输入凭证号', '提示', {
        confirmButtonText: '提交',
        cancelButtonText: '取消'
      }).then(({ value }) => {
        var save = saveVoucherReceive(value, obj.id)
        var updateNoPage = updateReceiveWaterStatus(obj.id, 2, 0)
        Promise.all([save, updateNoPage]).then(values => {
          const res1 = values[0]
          const res2 = values[1]
          if (res1 && res2) {
            this.$message({
              type: 'success',
              message: '操作成功！'
            })
            this.topage()
          }
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消'
        })
      })
    },
    toAgreeBusiness() {
      this.$prompt('请输入凭证号', '提示', {
        confirmButtonText: '提交',
        cancelButtonText: '取消'
      }).then(({ value }) => {
        var save = saveVoucherReceive(value, this.receiveWater.id)
        var updateNoPage = updateReceiveWaterStatus(this.receiveWater.id, 2, 0)
        Promise.all([save, updateNoPage]).then(values => {
          const res1 = values[0]
          const res2 = values[1]
          if (res1 && res2) {
            this.$message({
              type: 'success',
              message: '操作成功！'
            })
            this.businessDetailApplyhandleClose()
          }
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消'
        })
      })
    },
    toAgree() {
      this.$prompt('请输入凭证号', '提示', {
        confirmButtonText: '提交',
        cancelButtonText: '取消'
      }).then(({ value }) => {
        var save = saveVoucherReceive(value, this.receiveWater.id)
        var update = updateReceiveStatus(this.receiveWater.id)
        var updateNoPage = updateReceiveWaterStatus(this.receiveWater.id, 2, 0)
        Promise.all([save, update, updateNoPage]).then(values => {
          const res1 = values[0]
          const res2 = values[1]
          const res3 = values[2]
          if (res1 && res2 && res3) {
            this.$message({
              type: 'success',
              message: '操作成功！'
            })
            this.detailApplyhandleClose()
          }
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消'
        })
      })
    },
    showBusinessDetail(obj) {
      this.receiveWater = obj
      getBusinessReceiveDetail(obj.id).then(res => {
        if (res != undefined && res.resultCode === '0') {
          console.info(res.data)
          this.detailObj = res.data
          this.verificationInfo = res.info
          var obj = JSON.parse(res.data.imageList)
          console.info(obj)
          this.contractList = obj
        }
      })
      this.showBusinessDetailApply = true
    },
    showDetail(obj) {
      console.info(obj)
      // this.tableData = []
      // this.applicationForm = {}
      this.receiveList = []
      this.receiveWater = obj
      var querydata = {
        receiveWaterId: obj.id
      }
      this.showDetailApply = true
      getReceive(querydata).then(res => {
        if (res != undefined && res.resultCode === '0' && res.list) {
          for (let i = 0; i < res.list.length; i++) {
            const r = res.list[i]
            const obj = {
              applicationForm: r.data,
              tableData: r.list && r.list.map(item => {
                item.charge = item.receivable - item.money - item.deduct
                return item
              }) || []
            }
            this.receiveList.push(obj)
          }
          // this.applicationForm = res.data
          // this.tableData = res.list
          // for (var i = 0; i < this.tableData.length; i++) {
          //   var tmp = this.tableData[i]
          //   tmp.charge = tmp.receivable - tmp.money - tmp.deduct
          // }
        }
      })
    },
    detailApplyhandleClose() {
      this.showDetailApply = false
      this.receiveWater = {}
      this.topage()
    },
    businessDetailApplyhandleClose() {
      this.showBusinessDetailApply = false
      this.receiveWater = {}
      this.topage()
    },
    checkType(item) {
      if (this.switchComp.typeSelect != item) {
        this.switchComp.typeSelect = item
      }
      console.log('公司选择', this.switchComp.customerName)
      // 设置ocr
      this.setCustomerByOcr(
        this.switchComp.typeSelect == 0
          ? this.customerList : this.supplierList,
        this.switchComp.typeSelect == 0 ? 'fullName' : 'name',
        true)
    },
    addReceiveWater() {
      this.fileList = []
      this.picList = []
      this.fundsSelItemId = []
      this.showApply = true
      this.currentRow = null
      this.submitObj = {},
      this.switchComp = {
        id: null,
        costType: null,
        show: true,
        balance: 0,
        customerId: undefined,
        loading: false,
        topUpRemark: undefined,
        customerName: undefined,
        typeSelect: 0,
        receiveCompany: null,
        receviceCompanyName: null,
        receiveType: undefined,
        ticketNumber: null,
        paymentBank: null,
        paymentAccount: null,
        receiveBank: null,
        receiveAccount: null,
        ticketType: null,
        expireDate: null,
        occurDate: dayjs(new Date()).format('YYYY-MM-DD'),
        payUser: null
      }
      this.$nextTick(function() {
        this.$refs.costType && this.$refs.costType.clean()
      })
      // 加载分类列表
      this.loadFundsClassList()
    },
    loadFundsClassList() {
      if(this.fundsClassList.length == 0) {
        onlyTreeList('rec').then(res => {
          if (res.resultCode == '0' && res.data) {
            this.fundsClassList = res.data
            // console.log('fundsClassList', this.fundsClassList)
          }
        })
      }
    },
    loadAccountList(costType, receiveType, code) {
      if (costType == 1) {
        var type = 0
        if (receiveType == 3) {
          type = 1
        } if (receiveType == 1) {
          type = 2
        }
        console.log('rid', this.currentRow.id)
        selectbycode(code, type).then(res => {
          if (res) {
            for (var i = 0; i < res.list.length; i++) {
              var tmp = res.list[i]
              if (tmp.id == this.currentRow.id) {
                tmp['checked'] = true
              } else {
                tmp['checked'] = false
              }
            }
            this.accountlist = res.list
          }
        })
      } else {
        var type = 0
        if (receiveType == 3) {
          type = 1
        } else if (receiveType == 1) {
          type = 2
        }
        selectbyDeptId(code, type).then(res => {
          if (res) {
            for (var i = 0; i < res.list.length; i++) {
              var tmp = res.list[i]
              if (tmp.id == this.currentRow.id) {
                tmp['checked'] = true
              } else {
                tmp['checked'] = false
              }
            }
            this.accountlist = res.list
          }
        })
      }
    },
    updateAddReceiveWater(item) {
      // 修改
      console.log(item)
      this.fileList = []
      this.picList = []
      this.isAutoType = '1'
      if (item.picList) {
        this.fileList = item.picList.split(',').map(item => {
          return {
            url: item
          }
        })
        this.picList = item.picList.split(',')
      }
      this.ocrCdList = []
      // 判断是否汇票
      if (item.receviceType && item.receviceType == 3) {
        this.ocrListShow = true
        // 加载承兑汇票列表
        getReceiveWaterSubByParentId({ id: item.id }).then(res => {
          if (res && res.data) {
            this.ocrCdList = res.data.map(item => this.upDataReceiveWater(item))
            this.setAllSelectByCdTable()
          }
        })
      } else {
        this.showApply = true
      }
      this.currentRow = {
        id: item.accountId
      }
      this.submitObj = {}
      // 加载账户列表
      this.loadAccountList(item.costType, item.receviceType, item.receviceCompany)
      this.switchComp = {
        id: item.id,
        costType: item.costType,
        fundsClassId: item.fundsClassId,
        occurDate: item.occurDate,
        show: true,
        balance: this.moneyFmt(item.balance),
        customerId: item.sysCustomerId,
        loading: false,
        topUpRemark: item.remark,
        customerName: item.customerName,
        typeSelect: item.custorOrSupplier,
        receiveCompany: item.receviceCompany,
        receviceCompanyName: item.receviceCompanyName,
        receiveType: item.receviceType,
        ticketNumber: item.ticketNumber,
        paymentBank: item.paymentBank,
        paymentAccount: item.paymentAccount,
        receiveBank: item.receiveBank,
        receiveAccount: item.receiveAccount,
        ticketType: item.ticketType,
        expireDate: item.expireDate,
        isTransferred: item.isTransferred,
        votingDate: item.votingDate,
        drawerName: item.drawerName,
        acceptorName: item.acceptorName,
        payUser: item.payUser
      }
      this.fundsSelItemId = item.fundsClassPathIds ? item.fundsClassPathIds.split(',') : []
      this.NumberToChinese(this.switchComp.balance)
      console.log(this.switchComp)

      // this.$nextTick(function() {
      //   this.$refs.costType.clean()
      // })
      // 加载数据
    },
    upDataReceiveWater(item) {
      const data = {
        id: item.id,
        costType: item.costType,
        occurDate: item.occurDate,
        billAmount: this.moneyFmt(item.balance),
        customerId: item.sysCustomerId,
        loading: false,
        topUpRemark: item.remark,
        customerName: item.customerName,
        typeSelect: item.custorOrSupplier,
        receiveCompany: item.receviceCompany,
        receviceCompanyName: item.receviceCompanyName,
        receiveType: item.receviceType,
        billNo: item.ticketNumber,
        fileList: item.picList && item.picList.split(',') || [],
        showfile: item.picList && item.picList.split(',').map(item => { return { url: item, name: '查看' } }) || [],
        paymentBank: item.paymentBank,
        paymentAccount: item.paymentAccount,
        receiveBank: item.receiveBank,
        receiveAccount: item.receiveAccount,
        billType: item.ticketType == 1 ? '银行承兑' : '商业承兑',
        dueDate: item.expireDate,
        isTransferred: item.isTransferred,
        issueDate: item.votingDate,
        issuer: item.drawerName,
        acceptor: item.acceptorName,
        payUser: item.payUser
      }
      return data
    },
    submit() {
      if (!this.switchComp.costType) {
        this.$message.error('请选择收款类型')
        return
      }
      if (!this.switchComp.receiveType) {
        this.$message.error('请选择账户类型')
        return
      }
      if (!this.currentRow || !this.currentRow.id) {
        this.$message.error('请选择收款账户')
        return
      }
      // 判断图片是否合规, 承兑汇票 并且 批量导入
      if (this.switchComp.receiveType == 3 && this.isAutoType == 2) {
        this.$message.error('请上传符合要求的图片')
        return
      }

      if (!this.switchComp.balance) {
        this.$message.error('请输入金额')
        return
      }
      if (this.switchComp.receiveCompany && parseInt(this.switchComp.receiveCompany) != 25 && !this.switchComp.customerName) {
        this.$message.error('请选择付款客户')
        // + this.switchComp.receiveCompany + ',' + this.switchComp.customerName
        return
      }
      if ((this.switchComp.receiveType && this.switchComp.receiveType != 1) && (!this.picList || this.picList.length <= 0)) {
        this.$message.error('请上传网银截图  ')
        return
      }
      const loading = this.$loading({
        lock: true,
        text: '数据提交中，请稍后！',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      })
      if (this.switchComp.id) {
        this.submitObj.id = this.switchComp.id
      }
      if (!this.switchComp.customerId && this.switchComp.customerName) {
        // 新增客户 获取客户id
        this.addCustomerByName(this.switchComp.customerName).then(res => {
          if (res) {
            this.switchComp.customerId = res
            this.saveRecevieFun(loading)
          } else {
            this.loading = false
            this.$message.error('添加客户失败,请稍后再试')
          }
        }).catch(() => {
          this.loading = false
          this.$message.error('添加客户失败,请稍后再试')
        })
        return
      }
      this.saveRecevieFun(loading)
    },
    addCustomerByName(name) {
      // 根据名称 新增客户
      return new Promise((resolve, reject) => {
        // 物流业务 并且 非和盛
        if (this.switchComp.costType == 1 && (this.switchComp.receiveCompany == 10 || this.switchComp.receiveCompany == 25 || this.switchComp.receiveCompany == 41)) {
        // 客户 供应商
          if (this.switchComp.typeSelect == 1) {
            saveSysSupplierName(name,this.switchComp.receiveCompany==41?'HNHS':'').then(res => {
              if (res && res.id) {
                resolve(res.id)
              } else {
                reject()
              }
            }).catch(() => {
              reject()
            })
          } else {
            saveSysCustomerSecondlevel(name,this.switchComp.receiveCompany==41?'HNHS':'').then(res => {
              if (res && res.id) {
                resolve(res.id)
              } else {
                reject()
              }
            }).catch(() => {
              reject()
            })
          }
          // /api/baseInit/saveSysCustomerSecondlevel

          // /api/baseInit/saveSysSupplierName
        } else {
          // 非物流业务
          // /api/sysExternalCustomer/createAndPlate
          const data = {
            name: name,
            bankName: this.switchComp.paymentBank,
            bankAccount: this.switchComp.paymentAccount
          }
          console.log('save data', data)
          savetExternalAccountByCustomerNameAndBankAccount(data).then(res => {
            if (res && res.id) {
              resolve(res.id)
            } else {
              reject()
            }
          }).catch(() => {
            reject()
          })
        }

        // addCustomerByName(name).then(res => {
        //   if (res) {
        //     resolve(res)
        //   } else {
        //     reject()
        //   }
        // }).catch(() => {
        //   reject()
        // })
      })
    },
    saveRecevieFun(loading) {
      this.submitObj.custorOrSupplier = this.switchComp.typeSelect
      this.submitObj.balance = this.priceToNum(this.switchComp.balance)
      this.submitObj.remark = this.switchComp.topUpRemark
      this.submitObj.sysCustomerId = this.switchComp.customerId
      if (this.currentRow) {
        this.submitObj.accountId = this.currentRow.id
      }
      this.submitObj.receviceType = this.switchComp.receiveType
      this.submitObj.receviceCompany = this.switchComp.receiveCompany
      this.submitObj.ticketNumber = this.switchComp.ticketNumber
      this.submitObj.paymentBank = this.switchComp.paymentBank
      this.submitObj.paymentAccount = this.switchComp.paymentAccount
      this.submitObj.customerName = this.switchComp.customerName
      this.submitObj.picList = this.picList.toString()
      this.submitObj.receiveBank = this.switchComp.receiveBank
      this.submitObj.receiveAccount = this.switchComp.receiveAccount
      this.submitObj.ticketType = this.switchComp.ticketType
      this.submitObj.expireDate = this.switchComp.expireDate
      this.submitObj.costType = this.isShowSelCgwlKh ? this.switchComp.costType : -1
      this.submitObj.receviceCompanyName = this.switchComp.receviceCompanyName
      this.submitObj.occurDate = this.switchComp.occurDate
      // 能否转让 1 能 2 不能
      this.submitObj.isTransferred = this.switchComp.isTransferred
      // 出票日
      this.submitObj.votingDate = this.switchComp.votingDate
      // 出票人
      this.submitObj.drawerName = this.switchComp.drawerName
      // 承兑人
      this.submitObj.acceptorName = this.switchComp.acceptorName
      // 付款人（物流业务内账收款，允许只填写一个收款人文本，然后由业务人员在物流业务系统选择真实客户
      this.submitObj.payUser = this.switchComp.payUser
      // 收款类型
      this.submitObj.fundsClassId = this.fundsSelItemId && this.fundsSelItemId.length > 0 ? this.fundsSelItemId[this.fundsSelItemId.length-1] : ''
      // 贴息金额
      if ( this.isDiscountedAmountShow ) {
        this.submitObj.discountedAmount = this.priceToNum(this.switchComp.discountedAmount)
      } else {
        this.submitObj.discountedAmount = 0
      }
      this.submitObj.costType = this.isShowSelCgwlKh ? this.switchComp.costType : -1
      // if (this.switchComp.costType != 1) {
      //   this.submitObj.applyStatus = 1
      // }
      if (!this.isShowSelCgwlKh) {
        this.submitObj.applyStatus = 1
      }
      console.info(this.submitObj,this.isDiscountedAmountShow)
      if (this.submitObj.id) {
        updateAddReceiveWater(this.submitObj).then(res => {
          loading.close()
          this.$message.success('修改成功')
          this.ApplyhandleClose()
        })
      } else {
        // 新增时判断 是否已经录入过收款 收款账户或付款人 收款金额 时间备选
        isReceiveWaterExist(this.submitObj).then(res => {
          if (res && res.data && res.data.length > 0) {
            // this.$message.error('该客户已经录入过收款，请勿重复录入')
            // 提示 已存在 询问 是否继续
            // 表格
            // danyuange yangshi
            const tdstyle = 'style="border:1px solid #e8e8e8;padding:5px;text-align:center;font-size:12px;color:#333"'
            let rows = `<tr><td ${tdstyle}>付款公司</td><td ${tdstyle}>金额</td><td ${tdstyle}>收款公司</td><td ${tdstyle}>账户类型</td><td ${tdstyle}>收款类型</td><td ${tdstyle}>创建时间</td></tr>`

            // 取前10条
            rows += res.data.slice(0, 10).map(mitem => {
              return `<tr ><td ${tdstyle}>${mitem.customerName || '--'}</td>
               <td ${tdstyle}>${mitem.balance}</td>
               <td ${tdstyle}>${mitem.receviceCompanyName || '--'}</td>
               <td ${tdstyle}>${this.receviceTypeFmt(null, null, mitem.receviceType, null)}</td>
               <td ${tdstyle}>${this.costTypeFmt(null, null, mitem.costType, null)}</td>
               <td ${tdstyle}>${mitem.createTime}</td>

               </tr>`
            })
            // table 样式 最大高度 滚动
            const tabs = `<div style="max-height:200px;overflow-y:auto;"><table style="border-collapse: collapse;"><tbody > ${rows}</tbody></table></div>`
            this.$confirm(`系统中有重复的收款${tabs}，是否继续录入？`, '提示', {
              confirmButtonText: '继续录入',
              cancelButtonText: '取消',
              dangerouslyUseHTMLString: true
            }).then(() => {
              this.addAndCloseSaveReceiveWater(true, loading)
            }).catch(() => {
              this.addAndCloseSaveReceiveWater(false, loading)
            })

            return
          } else {
            this.addAndCloseSaveReceiveWater(true, loading)
          }
        }).catch(() => {
          // 网络错误
          this.$message.error('网络错误，请稍后再试')
          loading.close()
        })
        // addReceiveWater(this.submitObj).then(res => {
        //   loading.close()
        //   this.$message.success('新增成功')
        //   this.ApplyhandleClose()
        // })
      }
    },
    addAndCloseSaveReceiveWater(isup, loading) {
      if (isup) {
        addReceiveWater(this.submitObj).then(res => {
          loading.close()
          this.$message.success('添加成功')
          this.ApplyhandleClose()
        })
      } else {
        loading.close()
        this.$message.info('取消添加')
      }
    },
    topage(page = '') {
      if (page) {
        this.queryParams.pageNo = page
      }
      if (
        this.queryParams.createTime !== undefined &&
            this.queryParams.createTime !== null
      ) {
        this.queryParams.startTime = this.queryParams.createTime[0]
        this.queryParams.endTime = this.queryParams.createTime[1]
      } else {
        this.queryParams.startTime = undefined
        this.queryParams.endTime = undefined
      }
      this.loading = true
      var query = {
        receiveCompany: this.queryParams.receiveCompany,
        customer: this.queryParams.customer,
        startTime: this.queryParams.startTime,
        endTime: this.queryParams.endTime,
        costType: this.queryParams.costType,
        receiveStatus: this.queryParams.receiveStatus,
        receiveType: this.queryParams.receiveType,
        pageNo: this.queryParams.pageNo,
        pageSize: this.queryParams.pageSize,
        accid: this.queryParams.accid,
        fundsClassId: this.queryParams.fundsClassId
      }
      this.dataList = []
      getReceiveWater(query).then(response => {
        const data = response.page
        this.dataList = data.records
        this.total = data.total
        if(this.dataList)
        this.getVoucherStatusByReceIds(this.dataList.map(i=>i.id).join(','))
      }).finally(() => {
        this.loading = false
      })
    },
    eventPage(e) {
      this.queryParams.pageNo = e
      this.topage()
    },
    ApplyhandleClose() {
      this.showApply = false
      this.accountlist = []
      this.topage()
    },
    selectTicketType(id) {
      this.switchComp.ticketType = id
    },
    selectReceiveCompanyAll(obj) {
      this.isShowSelCgwlKh = false
      var that = this
      if (this.switchComp.receiveCompany == obj.deptId) {
        this.switchComp.receiveCompany = null
        this.switchComp.receviceCompanyName = null
        this.accountlist = []
      } else {
        this.switchComp.receiveCompany = obj.deptId
        this.switchComp.receviceCompanyName = obj.name
        var type = 0
        if (this.switchComp.receiveType == 3) {
          type = 1
        } else if (this.switchComp.receiveType == 1) {
          type = 2
        }
        selectbyDeptId(obj.deptId, type).then(res => {
          if (res) {
            for (var i = 0; i < res.list.length; i++) {
              var tmp = res.list[i]
              tmp['checked'] = false
            }
            this.accountlist = res.list
          }
        })
      }
      this.switchComp.customerId = null
      this.switchComp.customerName = null
      // 查询 设置 付款公司
      console.log('sel bank company', this.switchComp.receiveCompany)
      this.setCustomerByOcr(this.fdbizCustomerList, 'companyName', true)
      // 打开批量承兑汇票时，修改默认值 更新公司
      if (this.ocrListShow) {
        this.setCustomerCompanyOcr()
      }
    },
    selectPersonalReceiveCompanyAll(obj) {
      this.isShowSelCgwlKh = false
      // var that = this
      if (this.switchComp.receiveCompany == obj.deptId) {
        this.switchComp.receiveCompany = null
        this.switchComp.receviceCompanyName = null
        this.accountlist = []
      } else {
        this.switchComp.receiveCompany = obj.deptId
        this.switchComp.receviceCompanyName = obj.name
        var type = 0
        if (this.switchComp.receiveType == 3) {
          type = 1
        } else if (this.switchComp.receiveType == 1) {
          type = 2
        }
        selectbyDeptId(obj.deptId, type).then(res => {
          if (res) {
            for (var i = 0; i < res.list.length; i++) {
              var tmp = res.list[i]
              tmp['checked'] = false
            }
            this.accountlist = res.list
          }
        })
      }
      this.switchComp.customerId = null
      this.switchComp.customerName = null
    },
    selectPersonalReceiveCompany(obj) {
      // var that = this
      if (this.switchComp.receiveCompany == obj.code) {
        this.switchComp.receiveCompany = null
        this.switchComp.receviceCompanyName = null
        this.accountlist = []
        this.isShowSelCgwlKh = false
      } else {
        this.switchComp.receiveCompany = obj.code
        this.switchComp.receviceCompanyName = obj.value
        // 和盛特殊处理
        if (obj.code == '20') {
          this.isShowSelCgwlKh = false
        }else{
          this.isShowSelCgwlKh = true
        }
        var type = 0
        if (this.switchComp.receiveType == 3) {
          type = 1
        } if (this.switchComp.receiveType == 1) {
          type = 2
        }
        selectbycode(obj.code, type).then(res => {
          if (res) {
            for (var i = 0; i < res.list.length; i++) {
              var tmp = res.list[i]
              tmp['checked'] = false
            }
            this.accountlist = res.list
          }
        })
      }
      this.switchComp.customerId = null
      this.switchComp.customerName = null
    },
    selectReceiveCompany(obj) {
      // console.log('comp sel and type', this.switchComp.receiveType, obj.code)
      // var that = this
      if (this.switchComp.receiveCompany == obj.code) {
        this.switchComp.receiveCompany = null
        this.switchComp.receviceCompanyName = null
        this.accountlist = []
        this.isShowSelCgwlKh = false
      } else {
        this.switchComp.receiveCompany = obj.code
        this.switchComp.receviceCompanyName = obj.value
        // 和盛不显示
        if (obj.code == '20') {
          this.isShowSelCgwlKh = false
        } else {
          this.isShowSelCgwlKh = true
        }
        var type = 0
        if (this.switchComp.receiveType == 3) {
          type = 1
        } if (this.switchComp.receiveType == 1) {
          type = 2
        }
        selectbycode(obj.code, type).then(res => {
          if (res) {
            for (var i = 0; i < res.list.length; i++) {
              var tmp = res.list[i]
              tmp['checked'] = false
            }
            this.accountlist = res.list
          }
        })
      }

      this.switchComp.customerId = null
      this.switchComp.customerName = null
      this.$nextTick(() => {
        if (this.switchComp.typeSelect == 1) {
          if (this.$refs.supplier) {
            this.$refs.supplier.clean()
          }
          if (this.$refs.supplierocr) {
            this.$refs.supplierocr.clean()
          }
        } else {
          if (this.$refs.kehu) {
            this.$refs.kehu.clean()
          }
          if (this.$refs.kehuocr) {
            this.$refs.kehuocr.clean()
          }
        }
        // 查询 设置 付款公司
        // console.log('sel bank company', this.switchComp.receiveCompany)
        if (this.switchComp.receiveCompany == 20) {
          this.setCustomerByOcr(this.fdbizCustomerList, 'companyName', true)
        } else {
          // console.log('sel select', this.switchComp.typeSelect)
          this.setCustomerByOcr(this.switchComp.typeSelect == 0 ? this.customerList : this.supplierList, this.switchComp.typeSelect == 0 ? 'fullName' : 'name', true)
        }
      })
      // 打开批量承兑汇票时，修改默认值 更新公司
      if (this.ocrListShow) {
        this.setCustomerCompanyOcr()
      }
    },
    setCustomerByOcr(list, namekey, setBank = true) {
      console.log('set ocr name start', this.switchComp.customerName)
      if (!this.ocrPaymentCompany) {
        return
      }
      // 查询 正则匹配 item[namekey] == this.ocrPaymentCompany
      let obj = list.filter(item => this.ocrPaymentCompany==item[namekey])
      if (!obj || obj.length == 0) {
         obj = list.filter(item => new RegExp(this.ocrPaymentCompany).test(item[namekey]))
      }

      if (obj && obj.length > 0) {
        obj = obj[0]
        this.switchComp.customerId = obj.id
        this.switchComp.customerName = obj[namekey]
        console.log('set ocr name start:company', setBank, this.switchComp.receiveType, this.switchComp.receiveType != 1, obj, this.ocrPaymentBank, this.ocrPaymentAccount)
        if (setBank && this.switchComp.receiveType && this.switchComp.receiveType == 2) {
          this.switchComp.paymentBank = this.ocrPaymentBank || obj.bankName
          this.switchComp.paymentAccount = this.ocrPaymentAccount || obj.bankAccount
        } else {
          this.switchComp.paymentBank = null
          this.switchComp.paymentAccount = null
        }
        console.log('set ocr name end:company', this.switchComp.paymentBank, this.switchComp.paymentAccount)
      }
      // 未匹配 不清空
      //  else {
      //   this.switchComp.customerId = null
      //   this.switchComp.customerName = null
      //   this.switchComp.paymentBank = null
      //   this.switchComp.paymentAccount = null
      // }
    },
    handleCurrentChange(val) {
      this.currentRow = val
      for (var i = 0; i < this.accountlist.length; i++) {
        var tmp = this.accountlist[i]
        if (tmp.id == val.id) {
          tmp.checked = true
        } else {
          tmp.checked = false
        }
      }
    },
    setCustomer(obj) {
      console.log('test set customer', obj, this.switchComp.customerName)
      this.switchComp.customerId = obj.id
      this.switchComp.customerName = obj.fullName
      // this.switchComp.paymentBank = null
      // this.switchComp.paymentAccount = null
    },
    setSupplier(obj) {
      console.log('test set supplier', obj, this.switchComp.customerName)
      this.switchComp.customerId = obj.id
      this.switchComp.customerName = obj.name
      // this.switchComp.paymentBank = null
      // this.switchComp.paymentAccount = null
    },
    setFdbizCustomer(obj) {
      this.switchComp.customerId = obj.id
      this.switchComp.customerName = obj.companyName
      if (this.switchComp.receiveType == 2) {
        this.switchComp.paymentBank = this.ocrPaymentBank || obj.bankName
        this.switchComp.paymentAccount = this.ocrPaymentAccount || obj.bankAccount
      }
    },
    // costTypeQuery(item) {
    //   this.queryParams.costType = item.code
    //   this.topage(1)
    // },
    clkQuery() {
      this.queryParams.fundsClassId = this.fundsQueryId && this.fundsQueryId.length > 0 ? this.fundsQueryId[this.fundsQueryId.length - 1] : null
      this.topage(1)
    },
    costType(item) {
      const id = item.code
      console.log(id)
      // console.log(item.value)
      this.switchComp.costType = id
      // this.costDefName = item.value
      this.accountlist = []
      // 设置默认值
      if (this.switchComp.receiveType && this.switchComp.receiveType != 1) {
        // 是否选择类型
        if (this.switchComp.costType && this.switchComp.costType != 1) {
          this.setCustomerByOcr(this.fdbizCustomerList, 'companyName', true)
        }
        // 设置默认收款公司
        this.setReceiveCompanyOcr()
      }
      // 打开批量承兑汇票时，修改默认值 更新公司
      if (this.ocrListShow) {
        this.setCustomerCompanyOcr()
      }
    },
    setCustomerCompanyOcr() {
      let tmpCom = null // 缓存
      let supTmpCom = null
      // 默认customerList
      const tmpKey = !this.switchComp.costType || !this.switchComp.receiveCompany ? '' : this.switchComp.costType == 1 && this.switchComp.receiveCompany != 20 ? 'fullName' : 'companyName'
      const tmpComList = !this.switchComp.costType || !this.switchComp.receiveCompany ? [] : this.switchComp.costType == 1 && this.switchComp.receiveCompany != 20 ? this.customerList : this.fdbizCustomerList
      // 自动判断 船公司
      const tmpSup = !this.switchComp.costType || !this.switchComp.receiveCompany ? [] : this.switchComp.costType == 1 && this.switchComp.receiveCompany != 20 && this.supplierList || []
      this.ocrCdList = this.ocrCdList.map(item => {
        item.customerName = ''
        item.customerId = ''
        item.typeSelect = 0
        // 背书人underwriter 比对
        if (tmpCom && tmpCom[tmpKey] === item.underwriter) {
          item.customerId = tmpCom.id
          item.customerName = tmpCom[tmpKey]
        } else {
          const t = tmpComList.find(sub => sub[tmpKey] === item.underwriter)
          if (t) {
            tmpCom = t
            item.customerId = tmpCom.id
            item.customerName = tmpCom[tmpKey]
          }
        }
        // 未找到 去船公司找
        if (!item.customerId && tmpSup && tmpSup.length > 0) {
          if (supTmpCom && supTmpCom.name === item.underwriter) {
            item.customerId = supTmpCom.id
            item.customerName = supTmpCom.name
            item.typeSelect = 1
          } else {
            const t = tmpSup.find(sub => sub.name === item.underwriter)
            if (t) {
              supTmpCom = t
              item.customerId = supTmpCom.id
              item.customerName = supTmpCom.name
              item.typeSelect = 1
            }
          }
        }
        return item
      })
    },
    setReceiveCompanyOcr() {
      // 判断是否识别收款公司
      // switchComp.receiveCompany
      // switchComp.receviceCompanyName
      // 银行
      // selectReceiveCompany  costType=1 receiveComCode1020 code
      // selectReceiveCompanyAll costType!=1 allCompanyList deptId
      // 汇票
      // selectReceiveCompany  costType=1 receiveComCode1020 code
      // selectReceiveCompanyAll costType!=1 allCompanyList deptId
      if (!this.ocrReceiveCompany) {
        return
      }
      // 清空
      this.switchComp.receiveCompany = null
      this.switchComp.receviceCompanyName = null
      this.accountlist = []
      if (this.switchComp.costType == 1) {
        const obj = this.receiveComCode1020.find(item => item.spare1 == this.ocrReceiveCompany)
        if (obj) {
          this.selectReceiveCompany(obj)
        }
      } else {
        const obj = this.allCompanyList.find(item => item.name == this.ocrReceiveCompany)
        if (obj) {
          this.selectReceiveCompanyAll(obj)
        }
      }
    },
    applyStatusQuery(item) {
      const id = item.code
      if (this.queryParams.receiveStatus == id) {
        this.queryParams.receiveStatus = null
      } else {
        this.queryParams.receiveStatus = id
      }
      this.topage(1)
    },
    receiveTypeQuery(item) {
      const id = item.code
      console.log(id)
      console.log(item.value)
      if (this.queryParams.receiveType == id) {
        this.queryParams.receiveType = null
      } else {
        this.queryParams.receiveType = id
      }
      this.topage(1)
    },
    receiveType(item) {
      const id = item.code
      console.log(id)
      console.log(item.value)
      if (this.switchComp.receiveType == id) {
        this.switchComp.receiveType = null
      } else {
        this.switchComp.receiveType = id
      }
      this.accountlist = []
      this.switchComp.receiveCompany = null
      this.switchComp.receviceCompanyName = null

      // 承兑 默认 银行
      if (this.switchComp.receiveType == 3) {
        if (!this.switchComp.ticketType) {
          this.switchComp.ticketType = 1
        }
      } else {
        this.switchComp.ticketType = null
      }
      // 设置默认值
      if (this.switchComp.receiveType && this.switchComp.receiveType != 1) {
        // 是否选择类型
        if (this.switchComp.costType && this.switchComp.costType != 1) {
          this.setCustomerByOcr(this.fdbizCustomerList, 'companyName', true)
        }
      }
      // 清空银行卡
      if (this.switchComp.receiveType && this.switchComp.receiveType != 2) {
        this.switchComp.paymentBank = null
        this.switchComp.paymentAccount = null
      }
    },
    myFmtDateTime(cellValue, fmtstr) {
      if (cellValue == undefined || cellValue == null || cellValue == '') {
        return '--'
      }
      return dayjs(cellValue).format(fmtstr)
    },
    costTypeFmt(row, column, cellValue, index) {
      return fmtDictionary(cellValue, this.dictionaryLists['fdbiz_cost_type'])
    },
    receviceTypeFmt(row, column, cellValue, index) {
      return fmtDictionary(cellValue, this.dictionaryLists['settlement_type'])
    },
    receviceCompanyFmt(row, column, cellValue, index) {
      return fmtDictionary(cellValue, this.dictionaryLists['contract_company'])
    },
    publicFmt(row, column, cellValue, index) {
      var v = '--'
      if (cellValue != undefined && cellValue != null && cellValue != '') {
        v = cellValue
      }
      return v
    },
    NumFmt(row, column, cellValue, index) {
      var v = 0
      if (cellValue != undefined && cellValue != null && cellValue != '') {
        v = parseFloat(Math.abs(cellValue).toFixed(2)) && parseFloat(Math.abs(cellValue).toFixed(2)).toString().replace(/(^|\s)\d+/g, (m) => m.replace(/(?=(?!\b)(\d{3})+$)/g, ','))
      }
      return v
    },
    NumFmt2(row, column, cellValue, index) {
      var v = 0
      if (cellValue != undefined && cellValue != null && cellValue != '') {
        v = parseFloat(Math.abs(cellValue).toFixed(2))
      }
      return v
    },
    paymentTypeFmt(row, column, cellValue, index) {
      return fmtDictionary(cellValue, this.dictionaryLists['settlement_type'])
    },
    ticketTypeFmt(row, column, cellValue, index) {
      return fmtDictionary(cellValue, this.ticketTypeList)
    },
    fmtMoney(money) {
      if (money) {
        money = money.toString().replace(/[^\d\.]/g, '')
        this.switchComp.balance = money.replace(money.indexOf('.') > -1 ? /\B(?=(\d{3})+\.)/g : /\B(?=(\d{3})+$)/g, ',')
      }
    },
    moneyFmt(money) {
      if (money) {
        money = money.toString().replace(/[^\d\.]/g, '')
        return money.replace(money.indexOf('.') > -1 ? /\B(?=(\d{3})+\.)/g : /\B(?=(\d{3})+$)/g, ',')
      }
      return 0
    }
  }
}
</script>
<style>
.cd-list-dialog-cls .el-dialog__body{
  padding:0;
}
.cd-list-dialog-cls .el-table th.gutter {
  display: table-cell !important;
}

.cd-list-dialog-cls .el-table colgroup.gutter {
  display: table-cell !important;
}

.cd-list-dialog-cls table {
  width: 100% !important;
}

.cd-tab-cls{
  padding:0 !important;
  line-height:32px;
}
.cd-tab-cls .cell{
  padding:0 !important;
  font-size: 12px !important;
}
.cd-tab-cls .cell input{
  padding:0;
  font-size: 12px;
}
.cd-cell-sk-cls{
  padding:0 !important;
}
.cd-tab-header-cls .el-table__cell{
  text-align:center;
}

</style>

<style scoped>
  .width{
    width: 100%;
  }
  .query-form{
    text-align: left;
    padding-top: 10px;
  }
  .sp_add_left{
    width: 45%;
    margin-right: 5%;
  }
  .sp_add_right{
    width: 45%;
  }
  .el-table-info >>> .cell{
    text-align: center;
  }
  .el-table-info >>> th {
    background: #EDF5FF;
  }
  .hiddenClass >>> .el-upload {
    display: none;
  }
  .upload-demo{
    display: flex;
  }
  .showpic{
    display: none;
  }
  .ocr-btn-cls{
    /* float: right; */
    margin-left: 20px;
  }
.wd30{
    width:30%;
  }
  .btnCol{
    color: #13ce66;
  }
  .btnCol:hover{
    color:#10b85a;
  }
</style>
