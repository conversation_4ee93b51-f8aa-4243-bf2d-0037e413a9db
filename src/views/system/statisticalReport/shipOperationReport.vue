<template>
  <section class="app-container">
    <div class="sel-ship">
      <el-radio-group  v-model="params.shipId" @change="changeShip" >
        <el-radio-button v-for="item in shipList" :label="item.id">{{item.name}}</el-radio-button>
      </el-radio-group>
    </div>
    <div class="query-form">
     <span class="title">选择日期</span>
     <el-date-picker
        v-model="params.month"
        @change="changeShip"
        placeholder="选择日期"
        type="month"
        value-format="yyyy-MM"
        clearable
        size="small"
      />
    </div>
   <div>
    <div class="item-cls">
       <span class="header-title">收入统计</span>
         <el-table
          :data="voyages"
          style="width: 100%"
          border
          stripe
          size="medium"
        >
          <el-table-column
            label="挂帐时间"
            width="80"
            align="center"
          >
            <template slot-scope="scope">
              {{ scope.row.creditTime ? dayjs(scope.row.creditTime).format('MM.DD') : '-' }}
            </template>
          </el-table-column>

          <el-table-column
            label="船名"
            width="80"
            align="center"
          >
            <template slot-scope="scope">
              {{ scope.row.shipName || '--' }}
            </template>
          </el-table-column>

          <el-table-column
            prop="voyageNum"
            label="航次"
            width="80"
            align="center"
          >
            <template slot-scope="scope">
              {{ scope.row.voyageNum }}
            </template>
          </el-table-column>

          <el-table-column
            label="起始港-卸载港"
            width="150"
            align="center"
          >
            <template slot-scope="scope">
              {{ scope.row.loadingPortName || '--' }}-{{ scope.row.unloadingPortName || '--' }}
            </template>
          </el-table-column>

          <el-table-column
            prop="goodsName"
            label="货种"
            width="80"
            align="center"
          />

          <el-table-column
            prop="tonnage"
            label="实装(吨)"
            width="100"
            align="center"
          >
            <template slot-scope="scope">
              {{ scope.row.tonnage || '-' }}
            </template>
          </el-table-column>

          <el-table-column
            prop="price"
            label="运价(元/吨)"

            align="center"
          >
            <template slot-scope="scope">
              {{ formatMoney(scope.row.price) }}
            </template>
          </el-table-column>

          <el-table-column
            prop="settleSumPrice"
            label="包船价(元)"
            width="120"
            align="center"
          >
            <template slot-scope="scope">
              {{ formatMoney(scope.row.settleSumPrice) }}
            </template>
          </el-table-column>

          <el-table-column
            label="动态费用(元)"
            width="120"
            align="center"
          >
            <template slot-scope="scope">
              <span class="dynamic-costs-link" >
                {{ formatMoney(getVoyageDynamicCostsTotal(scope.row)) }}
              </span>
            </template>
          </el-table-column>

          <el-table-column
            label="合计(元)"
            width="120"
            align="center"
          >
            <template slot-scope="scope">
              {{ formatMoney(getVoyageTotal(scope.row)) }}
            </template>
          </el-table-column>
        </el-table>
          <el-table
          :data="voyageExtraIncomes"
          style="width: 100%"
          stripe
          border
          size="medium"
        >
          <el-table-column
            prop="shipName"
            label="船名"
            width="100"
            align="center"
          />

          <el-table-column
            label="日期"
            width="120"
            align="center"
          >
            <template slot-scope="scope">
              {{ scope.row.dataYear }}/{{ scope.row.dataMonth.toString().padStart(2,'0') }}
            </template>
          </el-table-column>

          <el-table-column
            prop="ptypeName"
            label="收入类型"
            align="center"
            :show-overflow-tooltip="true"
          >
            <template slot-scope="scope">
              <span :title="scope.row.fullPtypeName">{{ scope.row.ptypeName }}</span>
            </template>
          </el-table-column>

          <el-table-column
            label="金额(价税)"
            width="120"
            align="center"
          >
            <template slot-scope="scope">
              <money :value="scope.row.price" empty-string="-" />
            </template>
          </el-table-column>

          <el-table-column
            label="税率"
            width="100"
            align="center"
          >
            <template slot-scope="scope">
              {{ scope.row.priceTaxRate ? (scope.row.priceTaxRate * 100).toFixed(0) + '%' : '-' }}
            </template>
          </el-table-column>

          <el-table-column
            label="不含税金额"
            width="120"
            align="center"
          >
            <template slot-scope="scope">
              <money :value="scope.row.price / (1 + (scope.row.priceTaxRate || 0.09))" empty-string="-" />
            </template>
          </el-table-column>

          <el-table-column
            label="税额"
            width="120"
            align="center"
          >
            <template slot-scope="scope">
              <money
                :value="scope.row.price * ((scope.row.priceTaxRate || 0.09) / (1 + (scope.row.priceTaxRate || 0.09)))"
                empty-string="-"
              />
            </template>
          </el-table-column>

          <el-table-column
            prop="customerName"
            label="客户公司名称"
            align="center"
            :show-overflow-tooltip="true"
          />

          <el-table-column
            prop="voyageSummary"
            label="关联航次"
            align="center"
            :show-overflow-tooltip="true"
          />


        </el-table>
         <el-table
          :data="nonVoyageExtraIncomes"
          style="width: 100%"
          stripe
          border
          size="medium"
        >
          <el-table-column
            prop="shipName"
            label="船名"
            width="100"
            align="center"
          />

          <el-table-column
            label="日期"
            width="120"
            align="center"
          >
            <template slot-scope="scope">
              {{ scope.row.dataYear }}/{{ scope.row.dataMonth.toString().padStart(2,'0') }}
            </template>
          </el-table-column>

          <el-table-column
            prop="ptypeName"
            label="收入类型"
            align="center"
            :show-overflow-tooltip="true"
          >
            <template slot-scope="scope">
              <span :title="scope.row.fullPtypeName">{{ scope.row.ptypeName }}</span>
            </template>
          </el-table-column>

          <el-table-column
            label="金额(价税)"
            width="120"
            align="center"
          >
            <template slot-scope="scope">
              <money :value="scope.row.price" empty-string="-" />
            </template>
          </el-table-column>

          <el-table-column
            label="税率"
            width="100"
            align="center"
          >
            <template slot-scope="scope">
              {{ scope.row.priceTaxRate ? (scope.row.priceTaxRate * 100).toFixed(0) + '%' : '-' }}
            </template>
          </el-table-column>

          <el-table-column
            label="不含税金额"
            width="120"
            align="center"
          >
            <template slot-scope="scope">
              <money :value="scope.row.price / (1 + (scope.row.priceTaxRate || 0.09))" empty-string="-" />
            </template>
          </el-table-column>

          <el-table-column
            label="税额"
            width="120"
            align="center"
          >
            <template slot-scope="scope">
              <money
                :value="scope.row.price * ((scope.row.priceTaxRate || 0.09) / (1 + (scope.row.priceTaxRate || 0.09)))"
                empty-string="-"
              />
            </template>
          </el-table-column>

          <el-table-column
            prop="customerName"
            label="客户公司名称"
            align="center"
            :show-overflow-tooltip="true"
          />

        </el-table>
        <!-- <ship-month-table :outcomeList="incomeList"></ship-month-table> -->
    </div>
     <div class="item-cls">
         <span class="header-title">成本统计</span>
         <div v-for="(item, index) in actualCostList" :key="index" class="costs-list__item">
            <div class="cost-detail-table-wrapper">
              <table class="cost-detail-table">
                <tbody>
                  <tr v-for="cat in item._shipCostChild" :key="cat.id">
                    <td>
                      <div class="name-cell">{{ cat.typeName }}</div>
                      <div class="amount-cell">{{ formatMoney(cat._sum || 0) }}</div>
                    </td>
                    <td v-for="n in getMaxSubCats(item._shipCostChild)" :key="n">
                      <div v-if="cat.children && cat.children[n - 1]">
                        <div class="name-cell">{{ cat.children[n - 1].typeName }}</div>
                        <div class="amount-cell">{{ formatMoney(cat.children[n - 1]._sum || 0) }}</div>
                      </div>
                      <div v-else>
                        <div class="name-cell" />
                        <div class="amount-cell" />
                      </div>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        <!-- <ship-month-table :outcomeList="outcomeList" :oilList="oilList"></ship-month-table> -->
     </div>

   </div>


  </section>
</template>
<script>
import {shipList,costOfRevenue} from '@/api/system/onAccount.js'
import ShipMonthTable from '@/views/components/shipMonthTable/index.vue'
import dayjs from 'dayjs'

export default{
  components:{ShipMonthTable},
  data(){
    return {
      shipList:[],
      params:{
        shipId:'',
        month:dayjs().format('YYYY-MM')
      },
      inCostList:[],
      outCostList:[],
      incomeList:[],
      outcomeList:[],
      oilList:[],
      voyages:[],
      extraIncomes:[],
      actualCostList: [],
    }
  },
  computed:{
      voyageExtraIncomes() {
        return this.extraIncomes.filter(item => item.voyageId)
      },
      // 非航次收入
      nonVoyageExtraIncomes() {
        return this.extraIncomes.filter(item => !item.voyageId)
      },
      dynamicCosts() {
        const costs = []
        this.voyages.forEach(voyage => {
          if (voyage.voyageCostGroupList && voyage.voyageCostGroupList.length > 0) {
            voyage.voyageCostGroupList.forEach(group => {
              if (group.costDtoList && group.costDtoList.length > 0) {
                group.costDtoList.forEach(cost => {
                  costs.push({
                    ...cost,
                    voyageNum: voyage.voyageNum,
                    voyageId: voyage.id,
                    planLoadingDate: voyage.planLoadingDate,
                    ship: voyage.ship,
                    groupCompanyName: group.companyName,
                    groupSumPrice: group.sumPrice
                    // Ensure priceTaxRate is included
                  })
                })
              }
            })
          }
        })
        return costs
      }
  },
  created(){
    console.log('16-created')
    this.loadData()
  },
  methods:{
      getMaxSubCats(shipCostChild) {
      if (!shipCostChild || !shipCostChild.length) return 0
      let max = 0
      shipCostChild.forEach(cat => {
        if (cat.children && cat.children.length > max) {
          max = cat.children.length
        }
      })
      return max
    },
     formatMoney(value) {
      if (!value && value !== 0) return '-'
      return Number(value).toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g, ',')
    },
    loadData(){
      shipList().then(res=>{
        console.log('19',res)
        this.shipList = res.data
        this.params.shipId = this.shipList[0] && this.shipList[0].id
        this.loadDetail()
      })
    },
    changeShip(){
      this.loadDetail()
    },
    loadDetail(){
      if(!this.params.shipId){
        return
      }
      if(!this.params.month){
        return
      }
      const data={
        id:this.params.shipId,
        month:this.params.month,
      }
      costOfRevenue(data).then(res=>{
        this.inCostList = this.sortCostTypes(res.inCostList)
        this.outCostList = this.sortCostTypes(res.outCostList)
        this.incomeList = this.sortCostPriceTypes(res.incomeList,this.inCostList)
        this.outcomeList = this.sortCostPriceTypes(res.outcomeList,this.outCostList)
        this.oilList = res.outOilList
        this.voyages = res.voyageList
         // 将嵌套的shipCostRecordChildDtoList展平，保留父级ID
        const flattenedIncomes = []
        const inTemList = res.incomeList || []
        inTemList.forEach(record => {
          const s = record.shipCostRecordChildDtoList || []
          s.forEach(child => {
            flattenedIncomes.push({
              ...child,
              dataYear: record.dataYear,
              dataMonth: record.dataMonth,
              ship: record.ship,
              parentId: record.id, // 保存父级ID
              confirm: record.confirm
            })
          })
        })
        this.extraIncomes = flattenedIncomes

        const tmpList = res.outList || []
        // 计算每行的小计
        tmpList.forEach(item => {
          // 计算船舶费用小计
          const s = item.shipCostRecordChildDtoList || []
          const shipCostSum = s.reduce((sum, child) => {
            const price = Number.parseFloat(child.price)
            return sum + (Number.isFinite(price) ? price : 0)
          }, 0) || 0
          const shipCostChild = this.sortCostPriceTypes(s)
          const o = item.oilCostRecordChildDtoList || []
          // 计算油品费用小计
          const oilCostSum = o.reduce((sum, child) => {
            const price = Number.parseFloat(child.waterBalance)
            return sum + (Number.isFinite(price) ? price : 0)
          }, 0) || 0

          // 总小计
          item._sum = shipCostSum + oilCostSum
          // 保存分项小计，用于显示
          item._shipCostSum = shipCostSum
          item._oilCostSum = oilCostSum
          item._shipCostChild = shipCostChild
        })
        this.actualCostList = tmpList
      })
    },
    sortCostPriceTypes(list) {
      if (this.costTypeList == null || this.costTypeList.length === 0) {
        if (!this.stime) {
          this.stime = 1
        } else {
          this.stime++
        }
        if (this.stime === 3) {
          this.stime = 0
          return null
        }
        // sleep 等待costTypeList加载完成
        setTimeout(() => {
          this.sortCostPriceTypes(list)
        }, 1000)
      }
      if (list == null || list.length === 0) {
        return []
      }
      const listMap = {}
      list.forEach(item => {
        if (!listMap[item.ptypeId]) {
          listMap[item.ptypeId] = []
        }
        listMap[item.ptypeId].push(item)
      })
      // console.log('map', listMap)
      // 已costTypeList 为模版  list为数据 返回 新的集合
      const sortedTypes = []
      // const lts = this.costTypeList
      const lts = JSON.parse(JSON.stringify(this.costTypeList))
      lts.forEach(type => {
        const t = type
        // console.log('479', t.id, Boolean(listMap[t.id]))
        let p = 0
        if (listMap[t.id]) {
          // p = Number.parseFloat(listMap[t.id]?.price) || 0
          p = Number.parseFloat(listMap[t.id].reduce((sum, child) => {
            const price = Number.parseFloat(child.price)
            return sum + (Number.isFinite(price) ? price : 0)
          }, 0) || 0) || 0
          t.su = listMap[t.id]
        }
        const sum = this.addChildrenPriceToSortedList(t, listMap)
        t._sum = sum + p
        sortedTypes.push(t)
      })
      // console.log('st', sortedTypes)
      return sortedTypes
    },
     sortCostTypes(types) {
      if (types == null || types.length === 0) {
        return []
      }
      const typeMap = {}
      const root = []
      // biome-ignore lint/complexity/noForEach: <explanation>
      types.forEach(type => {
        // typeMap[type.id] = { ...type, children: [] }
        typeMap[type.id] = type
        typeMap[type.id].children = []
        if (!type.parentId) {
          root.push(typeMap[type.id])
        }
      })
      // biome-ignore lint/complexity/noForEach: <explanation>
      types.forEach(type => {
        if (type.parentId && typeMap[type.parentId]) {
          typeMap[type.parentId].children.push(typeMap[type.id])
        }
      })
      return root
    },
    sortCostPriceTypes(list,costTypeList) {
      if (list == null || list.length === 0) {
        return []
      }
      const listMap = {}
      // biome-ignore lint/complexity/noForEach: <explanation>
      list.forEach(item => {
        if (!listMap[item.ptypeId]) {
          listMap[item.ptypeId] = []
        }
        listMap[item.ptypeId].push(item)
      })
      const sortedTypes = []
      const lts = JSON.parse(JSON.stringify(costTypeList))
      // biome-ignore lint/complexity/noForEach: <explanation>
      lts.forEach(type => {
        const t = type
        let p = 0
        if (listMap[t.id]) {
          p = Number.parseFloat(listMap[t.id].reduce((sum, child) => {
            const price = Number.parseFloat(child.price)
            return sum + (Number.isFinite(price) ? price : 0)
          }, 0) || 0) || 0
          t._detail = listMap[t.id]
        }
        const sum = this.addChildrenPriceToSortedList(t, listMap)
        t._sum = sum + p
        sortedTypes.push(t)
      })
      return sortedTypes
    },
    addChildrenPriceToSortedList(item, listMap) {
      if (!item.children || !item.children.length) return 0
      let sum = 0
      // biome-ignore lint/complexity/noForEach: <explanation>
      item.children.forEach(child => {
        const t = child
        let p = 0
        if (listMap[t.id]) {
          p = Number.parseFloat(listMap[t.id].reduce((s, c) => {
            const price = Number.parseFloat(c.price)
            return s + (Number.isFinite(price) ? price : 0)
          }, 0) || 0) || 0
          t._detail = listMap[t.id]
        }
        const csum = this.addChildrenPriceToSortedList(t, listMap)
        t._sum = csum + p
        sum += t._sum
      })
      return sum
    },
     getVoyageDynamicCosts(voyage) {
      return this.dynamicCosts.filter(cost => cost.voyageId === voyage.id)
    },
    getVoyageDynamicCostsTotal(voyage) {
      return this.getVoyageDynamicCosts(voyage).reduce((sum, cost) => {
        const amount = Number.parseFloat(cost.prices) || 0
        return sum + (cost.costType === 1 ? amount : -amount)
      }, 0)
    },
     getVoyageTotal(voyage) {
      const freight = ((voyage.price || 0) * (voyage.tonnage || 0)) + (voyage.settleSumPrice || 0)
      const dynamicCosts = this.getVoyageDynamicCostsTotal(voyage)
      return freight + dynamicCosts
    }
  }
}
</script>
<style scoped>
.sel-ship{
  padding-bottom: 8px;
  border-bottom: 1px solid #eee;
}
.query-form{
  padding-top: 8px;
  padding-bottom: 8px;
  border-bottom: 1px solid #eee;
}
.query-form .title{
  font-size: 16px;
}
.item-cls {
  margin-top: 8px;
  margin-bottom: 8px;
}
.header-title{
  font-size: 16px;

}

</style>
