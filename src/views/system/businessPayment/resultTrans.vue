<template>
  <div>
      <div style="display: flex; justify-content: space-between;">
        <span>
          账期：{{ tableData.period}}
        </span>
        <span>帐套:{{ tableData.appName }}</span>
        <span>
          凭证字号：{{ tableData.acctgTransCategoryId.name || ''}}-{{ tableData.voucherNo}}
        </span>
      </div>
      <el-table
        :data="updatedTableData"
        header-cell-class-name="custom-header"
        border
        highlight-current-row
        style="width: 100%">
        <el-table-column
          prop="summary"
          label="摘要"
          width="130">
          <template #default="{ row }">
            <!-- <span>{{ row.comments }}</span> -->
            <el-input v-model="row.comments" placeholder="摘要" class="custom-input"></el-input>
          </template>
        </el-table-column>
        <el-table-column
          prop="subject"
          label="科目"
          >
          <template #default="{ row,$index }">
              <span v-show="!classDataList || classDataList.length==0">{{ row.longText }}</span>
              <el-select v-show="classDataList && classDataList.length"  :ref="'elSelect'+$index"  class="custom-input" v-model="row.glAccount.code" style="width: 100%;" default-first-option filterable placeholder="请选择科目">
                  <el-option  v-for="c in classDataList" :value="c.glAccountCode" :label="c.glAccountCode+' '+c.longName">
                  </el-option>
              </el-select>
          </template>
        </el-table-column>

        <el-table-column
          prop="debit"
          label="借方金额"
          align="right"
          header-align="center"
          width="100">
          <template #default="{ row }">
            <el-input-number v-model="row.basePostedDr"      :controls="false" class="custom-input custom-input0"></el-input-number>
            <!-- <span>{{ fmtMoney(row.basePostedDr) }}</span> -->
          </template>
        </el-table-column>

        <el-table-column
          prop="credit"
          label="贷方金额"
          align="right"
          header-align="center"
          width="100">
          <template #default="{ row }">
            <el-input-number v-model="row.basePostedCr"     :controls="false" class="custom-input custom-input0"></el-input-number>
            <!-- <span>{{ fmtMoney(row.basePostedCr)}}</span> -->
          </template>
        </el-table-column>
        <el-table-column
          label="操作"
          header-align="center"
          align="center"
          width="180"
        >
        <template #default="{ row,$index }">
          <el-button @click="delChild($index)"  class="el-icon-remove"  size="mini">删除</el-button>
          <el-button @click="addChild(row,$index)"  class="el-icon-plus"  size="mini">添加</el-button>
        </template>
      </el-table-column>
      </el-table>

      <div class="total">
        <span>合计:</span>
        <span   >借方金额: {{ fmtMoney(totalDebit.toFixed(2)) }}</span>
        <span   >贷方金额: {{ fmtMoney(totalCredit.toFixed(2)) }}</span>
      </div>
      <div class="footer" style="text-align: right;">
        <el-button type="primary" size="mini" v-loading="saveBtn" @click="changeData()">保存</el-button>
      </div>
  </div>
</template>

<script>
import { getHaokjSub, updateVoucher} from '@/api/business/processapi'
import currency from 'currency.js'
export default {
  props: {
    tableData: {
      type: Array|Object,
      required: true,
    },
    classList: {
      type: Array,
      required: false,
    }
  },
  watch: {
    tableData: {
      handler(newVal) {
        this.updatedTableData = newVal.details;
        this.loadClassList()
      },
      deep: true,
    },
    // classList: {
    //   handler(newVal) {
    //     this.classDataList = newVal
    //   },
    //   deep: true,
    //   immediate: true,
    // }
    // updatedTableData: {
    //   handler(newVal) {
    //     this.$emit('update:tableData', newVal);
    //   },
    //   deep: true,
    // },
  },
  data() {
    return {
      classDataList:[],
      // tableData: [
      //   { summary: '', subject: '', debit: 0, credit: 0 },
      //   // 可以初始化更多行数据
      // ],
      updatedTableData: this.tableData.details,
      saveBtn:false,

    };
  },
  created() {
    this.loadClassList()
  },
  computed: {
    totalDebit() {
      return this.tableData.details.reduce((sum, row) => sum + Number((row.basePostedDr||0)), 0);
    },
    totalCredit() {
      return this.tableData.details.reduce((sum, row) => sum + Number((row.basePostedCr||0)), 0);
    },
  },
  methods: {
     // 格式化显示的值
     formatter(value) {
      return value === 0 ? '' : value;
    },
    // 解析输入的值
    parser(value) {
      return value === '' ? 0 : value;
    },
    delChild(index){
      this.updatedTableData = this.updatedTableData.splice(index,1)
    },
    addChild(item,index){
      console.log('150',index)
      const n = Object.assign({},item,{
        basePostedDr:0,
        basePostedCr:0,
      })
      const i = index+1
      this.updatedTableData = this.updatedTableData.splice(i,0,n)
    },
    loadClassList(){
      if(!this.tableData || !this.tableData.appCode){
        return
      }
      getHaokjSub(this.tableData.appCode).then(res=>{
        if (res && res.data) {
          this.classDataList = res.data
        }
      })
    },
    fmtMoney(v) {
      if(!v){
        return '--'
      }
      return currency(v,{separator: ',',symbol:''}).format();
    },
    updateCurrentData() {
      if(this.saveBtn){
        return
      }
      this.saveBtn = true
      const json = this.tableData
      json.details = this.updatedTableData
      // console.log("121",json)
      updateVoucher(json).then(res => {
        // console.log("121",res)
        this.$message.success('保存成功')
      }).catch((e)=>{
        this.$message.error(e)
      }).finally(()=>{
        this.saveBtn = false
      })
    },
    changeData() {
      this.updateCurrentData()
    },
    labelSub(subid) {
      if (this.classDataList) {
        return subid + ' ' +this.classDataList.find(item => item.glAccountCode === subid).longName
      }
      return subid
    },
    // updateCurrentData() {
    //   this.$emit('update:tableData', this.updatedTableData);
    // }
  },
};
</script>
<style>
.custom-header {
    background-color: #3FC8DD !important;
    color: #fff;
}
.custom-header .cell {
  text-align: center;
}
</style>
<style scoped>
.total {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  margin-bottom: 20px;
}
.total span {
  margin-right: 10px;
  line-height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.total .el-tag {
  margin-left: 5px;
}
.flexcenter{
  display: flex;
  align-items: center;
}
.custom-input0 /deep/ input[aria-valuenow='0']{
  color: transparent;
}
.custom-input0 /deep/ input[aria-valuenow='0']:focus-within,.custom-input0 /deep/ input[aria-valuenow='0']:hover,.custom-input0 /deep/ input[aria-valuenow='0']:focus{
  color:#606266;
}
</style>

