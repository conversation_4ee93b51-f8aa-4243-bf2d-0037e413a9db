<template>
  <section>
    <el-form :inline="true" :model="params" style="padding:10px;">
      <el-form-item label="挂账状态" style="text-align: left">
        <el-tag class="pointer" :type="params.status=='1'?'success':'info'" @click="selstatus('1')">待挂账</el-tag>
        &nbsp;
        <el-tag class="pointer" :type="params.status=='2'?'success':'info'" @click="selstatus('2')">已挂账</el-tag>
      </el-form-item>
      <el-form-item label="船舶名称">
        <el-input
          v-model="params.shipName"
          placeholder="船舶名称"
          clearable
          size="small"
          style="width: 240px"
        />
      </el-form-item>
      <el-form-item label="挂帐号">
        <el-input
          v-model="params.onAccountNum"
          placeholder="挂帐号"
          clearable
          size="small"
          style="width: 240px"
        />
      </el-form-item>
       <el-button @click="loadData(1)">查询</el-button>
    </el-form>

    <el-table
      v-loading="tableLoading"
      tooltip-effect="dark"
      stripe
      :data="costList"
    >
    <el-table-column prop="finishTime"  align="center"  label="挂账年月" :formatter="formatDateYYYYMM"></el-table-column>
      <el-table-column prop="globalParam5"  align="center"  label="挂账号" :formatter="publicFmt"></el-table-column>
      <el-table-column label="收/付" align="center" prop="globalParam3">
        <template slot-scope="scope">
          <span v-if="scope.row.globalParam3=='1'">其他收入</span>
          <span v-else>其他支出</span>
        </template>
      </el-table-column>
      <el-table-column label="申请人" align="center" prop="sponsorName" />
      <el-table-column label="申请时间" align="center" prop="sponsorTime" :formatter="formatDate" />
      <el-table-column label="总金额" align="center" prop="sumBlance" :formatter="formatMoney" />

      <el-table-column label="操作" align="center" prop="">
        <template slot-scope="scope">
          <el-button type="text" size="mini" @click="showDetail(scope.row)">查看</el-button>
        </template>
      </el-table-column>
    </el-table>
    <div style="text-align: right;">
      <el-pagination
        hide-on-single-page
        background
        :total="total"
        :page-size="pageSize"
        :current-page="pageNum"
        layout="prev, pager, next"
        @current-change="loadData"
      />
    </div>

    <el-dialog
      title="查看明细"
      :visible.sync="dialogVisible"
      width="80%"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :before-close="handleClose"
    >
    <div id="apply-print-id" style="display: flex;justify-content: space-between;padding:5px 0;">
        <div>挂账号：{{ dialogData.globalParam5 }} </div>
        <div>核算：{{ dialogData.areaName||'--' }}</div>
      </div>
      <el-table
      id="apply-table-id"
        v-loading="tableLoading"
        tooltip-effect="dark"
        stripe
        :data="dialogData.costs"
      >
        <el-table-column label="船名" align="center" prop="shipName" />
        <el-table-column label="离港日期" align="center" prop="epartureTimeDate" :formatter="formatDateMMDD" />
        <el-table-column label="科目" align="center" prop="costType" :formatter="formatCostType" />
        <el-table-column label="吨位" align="center" prop="tonnage" :formatter="formatTonnage" />
        <el-table-column label="单价" align="right" prop="money" :formatter="formatMoney" />
        <el-table-column label="价格" align="right" prop="costPrice" :formatter="formatMoney" />
        <el-table-column label="不含税金额" align="right" prop="noTaxMoney" :formatter="formatMoney" />
        <el-table-column label="税额" align="right" prop="taxMoney" :formatter="formatMoney" />
        <el-table-column label="税率" align="center" prop="costTax" :formatter="formatTax" />
        <el-table-column label="发票类型" align="center" prop="notesType" :formatter="formatNotesType" />
        <el-table-column :label="dialogData.globalParam3==1?'客户':'供应商'" align="center" :prop="dialogData.globalParam3==1?'secondCustomerName':'sysSupplierName'" />
        <el-table-column label="类型" align="center"  :formatter="formatMoneyType" />
        <el-table-column label="合同公司" align="center" prop="contractCompany" :formatter="formatContractCompany" />
      </el-table>
      <div id="sp-list-id" style="margin-top:10px;display:flex;justify-content: space-around;">
              <div v-for="litem in spLogList" :key="litem.id">
                <span v-if="litem.nodeLabel">{{ litem.nodeLabel }}:{{ litem.userName||'--'}}</span>
              </div>
      </div>
      <div style="text-align: right;">
        <el-button @click="printApply">打印</el-button>
      </div>
      <template v-if="dialogData.status == 1" slot="footer">
        <div style="text-align: right;">
          <el-popconfirm title="确认标记为已挂账吗？" @confirm="markFinish">
            <el-button slot="reference" type="primary">标记为已挂账</el-button>
          </el-popconfirm>
        </div>
      </template>
    </el-dialog>
  </section>
</template>

<script>

import { updateOnAccountOtherCostProcess } from '@/api/system/onAccount.js'

import dayjs from 'dayjs'

import { getOnAccountListOtherCost } from '@/api/system/onAccount.js'
import { getDictionaryList } from '@/api/system/baseInit'
import {getWuLiuSpPerspnListByProcessId} from '@/api/system/process'
import currency from 'currency.js'
export default {
  data() {
    return {
      params: {
        status: '1',
        shipName:'',
        onAccountNum:''
      },
      total: 0,
      pageNum: 1,
      pageSize: 20,
      tableLoading: false,
      costList: [],
      dict: {
        bill_tax: [],
        contract_company: [],
        other_expenditure: [],
        other_income: [],
        notes_type: []
      },
      dialogVisible: false,
      dialogData: {
        status: '2',
        costs: []
      },
      spLogList:[]
    }
  },
  mounted() {
    this.tableLoading = true
    getDictionaryList('other_income,other_expenditure,contract_company,bill_tax,notes_type').then(res => {
      this.dict.other_income = res.map.other_income
      this.dict.other_expenditure = res.map.other_expenditure
      this.dict.contract_company = res.map.contract_company
      this.dict.bill_tax = res.map.bill_tax
      this.dict.notes_type = res.map.notes_type || []
      this.loadData()
    })
  },
  methods: {
    printHtmlId(id) {

      const html = document.querySelector('#' + id).innerHTML
      // 新建一个 DOM
      const div = document.createElement('div')
      const printDOMID = 'printDOMElement'
      div.id = printDOMID
      div.innerHTML = html

      // 提取第一个表格的内容 即表头
      const ths = div.querySelectorAll('.el-table__header-wrapper th')
      const ThsTextArry = []
      for (let i = 0, len = ths.length; i < len; i++) {
          if (ths[i].innerText !== '') ThsTextArry.push(ths[i].innerText)
      }

      // 删除多余的表头
      div.querySelector('.hidden-columns').remove()
      // 第一个表格的内容提取出来后已经没用了 删掉
      div.querySelector('.el-table__header-wrapper').remove()

      // 将第一个表格的内容插入到第二个表格
      let newHTML = '<thead><tr>'
      for (let i = 0, len = ThsTextArry.length; i < len; i++) {
          newHTML += '<th style="text-align: center;" >' + ThsTextArry[i] + '</th>'
      }

      newHTML += '</tr></thead>'
      console.log('newHTML', newHTML)
      // 删除 colgroup
      div.querySelector('.el-table__body-wrapper colgroup').remove()
      // div.querySelector('.el-table__body-wrapper table').insertAdjacentHTML('afterbegin', newHTML)
      // table>tbody 插入 thead  变成 table>thead>tbody
      div.querySelector('.el-table__body-wrapper table').insertAdjacentHTML('afterbegin', newHTML)
      // 宽度修改
      div.querySelector('.el-table__body-wrapper table').style.width = '100%'
      // border = 1
      // div.querySelector('.el-table__body-wrapper table').style.border = '1px solid #ebeef5'
      div.querySelector('.el-table__body-wrapper table').setAttribute('border', '1')

      return div.innerHTML
      },
      printApply() {
      const loading = this.$loading({
        lock: true,
        text: '加载中...',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      })
      let divEl = `<div style="margin-left:180px;font-size:22px;">`
      divEl += document.getElementById('apply-print-id').outerHTML
      divEl += this.printHtmlId('apply-table-id')
      divEl += document.getElementById('sp-list-id').outerHTML
      divEl += `</div>
      <style>
      table {
        border-collapse: collapse;
        border-spacing: 0;
      }
      table td,table th{
        text-align: center;
        padding: 10px;
      }
      </style>
      `
      console.log('pdiv',divEl)
      loading.close()
      this.$XPrint({
        sheetName: '打印合值 挂账号：'+(this.dialogData.globalParam5||'--'),
        content: divEl
      })

      },
    loadSpListByProcessId(processId) {
      this.spLogList = []
      getWuLiuSpPerspnListByProcessId(processId).then(res => {
        console.log('process',res)
        if (res.data) {
          this.spLogList = res.data
        }
      })
    },
    selstatus(status) {
      this.params.status = status
      this.loadData(1)
    },
    loadData(pageNum = 1) {
      this.pageNum = pageNum
      var param = {
        pageNo: this.pageNum,
        pageSize: this.pageSize,
        applyStatus: this.params.status,
        onAccountNum:this.params.onAccountNum,
        shipName:this.params.shipName
      }
      this.tableLoading = true
      getOnAccountListOtherCost(param).then(res => {
        if (res !== undefined) {
          console.log('res.page.records==1111=>',res.page.records)
          this.costList = res.page.records
          this.total = res.page.total
        }
      }).finally(() => {
        this.tableLoading = false
      })
    },
    myFmtDateTime(cellValue, fmtstr) {
      if (cellValue === undefined || cellValue == null || cellValue == '') {
        return '--'
      }
      return dayjs(cellValue).format(fmtstr)
    },
    showDetail(row) {
      this.dialogData = row
     
      //循环this.dialogData.costs，最后一个对象不循环，计算noTaxTotal和taxTotal，
      let noTaxTotal = 0;
      let taxTotal = 0;
      for (let i = 0; i < this.dialogData.costs.length; i++) {
        if (i === this.dialogData.costs.length - 1) {
          break;
        }
        let info = this.dialogData.costs[i]
        let noTaxMoney = this.shuino(info.costTax, info.costPrice)
        let taxMoney = this.calculateTaxMoneyNew(info.costTax, info.costPrice)
        noTaxTotal += noTaxMoney
        taxTotal += taxMoney
        info['noTaxMoney'] = noTaxMoney
        info['taxMoney'] = taxMoney
      }
      this.dialogData.costs[this.dialogData.costs.length - 1]['noTaxMoney'] = noTaxTotal
      this.dialogData.costs[this.dialogData.costs.length - 1]['taxMoney'] = taxTotal
       console.log('this.dialogData====',this.dialogData);
      this.dialogVisible = true
      this.loadSpListByProcessId(row.id)
    },
    handleClose() {
      this.dialogVisible = false
    },
    formatCostType(row, column, cellValue, index) {
      // eslint-disable-next-line eqeqeq
      if (row.type == 1) {
        // eslint-disable-next-line eqeqeq
        const costTypeDict = this.dict.other_income.find(item => item.code == cellValue)
        return costTypeDict ? costTypeDict.value : ''
      } else {
        // eslint-disable-next-line eqeqeq
        const costTypeDict = this.dict.other_expenditure.find(item => item.code == cellValue)
        return costTypeDict ? costTypeDict.value : ''
      }
    },
    formatMoneyType(row, column, cellValue, index ) {
      if (this.dialogData.globalParam3 == 1) {
        return '收入'
      }
      return '支出'
    },
    formatMoney(row, column, cellValue, index) {
      if (!cellValue) {
        return '--'
      }
      return cellValue.toFixed(2).replace(/(^|\s)\d+/g, (m) => m.replace(/(?=(?!\b)(\d{3})+$)/g, ','))
    },
    formatDate(row, column, cellValue, index) {
      return this.myFmtDateTime(cellValue, 'YYYY-MM-DD')
    },
    formatDateYYYYMM(row, column, cellValue, index) {
      return this.myFmtDateTime(cellValue, 'YYYY-MM')
    },
    formatDateMMDD(row, column, cellValue, index) {
      return this.myFmtDateTime(cellValue, 'MM/DD')
    },
    formatContractCompany(row, column, cellValue, index) {
      // eslint-disable-next-line eqeqeq
      const dictObj = this.dict.contract_company.find(item => item.code == cellValue)
      return dictObj ? dictObj.value : ''
    },
    formatTax(row, column, cellValue, index) {
      // eslint-disable-next-line eqeqeq
      const dictObj = this.dict.bill_tax.find(item => item.code == cellValue)
      return dictObj ? dictObj.value : ''
    },
    formatNotesType(row, column, cellValue, index) {
      // eslint-disable-next-line eqeqeq
      const dictObj = this.dict.notes_type.find(item => item.code == cellValue)
      return dictObj ? dictObj.value : ''
    },
    formatTonnage(row, column, cellValue, index) {
      return window.round(cellValue, 8)
    },
    markFinish() {
      // console.log('-----------')
      this.dialogVisible = true
      updateOnAccountOtherCostProcess(this.dialogData.id, this.dialogData.statusId, '2', '挂账完成').then(res => {
        this.dialogVisible = false
        this.loadData()
      })
    },
    publicFmt(row, column, cellValue, index) {
      var v = "--";
      if (cellValue != undefined && cellValue != null && cellValue != "") {
        v = cellValue
      }
      return v;
    },
    shuino(tax, price) {
      // return
      //判断price是否为undefined
      if (price == undefined) {
        return '--';
      }
      let v = 0.0;
      // 确保 price 是数字类型
      if (price != null && price !== '') {
        // 先转成字符串，price去掉,
        price = price.toString();
        price = price.replace(/,/g, '');
        // 将 price 转换为数字
        v = Number(price);

        // 如果转换失败，返回0
        if (isNaN(v)) {
          return 0;
        }
      }
      console.log(tax, price)
      if (tax != null && tax != "" && tax != "10") {
        if (tax == "20") {
          console.log('v===20>')
          v = v / 1.01;
        } else if (tax == "30") {
          v = v / 1.06;
        } else if (tax == "40") {
          v = v / 1.09;
        } else if (tax == "50") {
          v = v / 1.13;
        } else if (tax == "25") {
          v = v / 1.03;
        }
      }
      console.log('v===>', v)
      // 使用 currency.js 进行四舍五入，保持与其他方法一致
      return currency(v, {precision: 2}).value;
    },
    /**
     * 计算要交的税
     * @param {string} tax - 税率代码
     * @param {number} price - 价税合计
     * @returns {number} - 返回需要交的税额
     */
    calculateTaxMoney(tax, price) {
      console.log('price=calculateTaxMoney=11==>',price)
      console.log('tax=calculateTaxMoney=222==>',tax)

      let v = 0.0;
      if (price != null) {
        v = price;
      }
      if (tax != null && tax !== "" && tax != "10" && tax != "15") {
        if (tax == "20") {
          v = v * 0.01;
        } else if (tax == "30") {
          v = v * 0.06;
        } else if (tax == "40") {
          v = v * 0.09;
        } else if (tax == "50") {
          v = v * 0.13;
        } else if (tax == "25") {
          v = v * 0.03;
        }
        // 使用currency.js进行四舍五入,保持与其他方法一致的精度
        v = currency(v, {precision: 2}).value;
      } else {
        v = 0.0;
      }
      return v;
    },
    /**
     * 计算要交的税(新方法)
     * @param {string} tax - 税率代码
     * @param {number} price - 价税合计
     * @returns {number} - 返回需要交的税额
     */
    calculateTaxMoneyNew(tax, price) {
      if (price == undefined) {
        return '--';
      }
      //把price转成字符串
      price = price.toString();
      price = price.replace(/,/g, '');
      const a = this.shuino(tax, price);
      const p = price - a;
      return currency(p, {precision: 2}).value;
      // return this.calculateTaxMoney(tax, a);
    },
    formatNoTaxMoney(row, column, cellValue, index) {
      if (!cellValue) {
        return '--'
      }
      // 使用shuino方法计算不含税金额
      const result = this.shuino(row.costTax, cellValue)
      if (result === '--') {
        return '--'
      }
      return result.toFixed(2).replace(/(^|\s)\d+/g, (m) => m.replace(/(?=(?!\b)(\d{3})+$)/g, ','))
    },
    formatTaxMoney(row, column, cellValue, index) {
      if (!cellValue) {
        return '--'
      }
      // 使用calculateTaxMoneyNew方法计算税额
      const result = this.calculateTaxMoneyNew(row.costTax, cellValue)
      if (result === '--') {
        return '--'
      }
      return result.toFixed(2).replace(/(^|\s)\d+/g, (m) => m.replace(/(?=(?!\b)(\d{3})+$)/g, ','))
    }
  }
}
</script>

<style scoped>
    .pointer {
        cursor: pointer;
    }
</style>
