<template>
  <div class="app-container">
    <el-divider content-position="left"  id="peizaidantitle">
      <span  id="pzd-title">{{shipName}}/{{ shipLine.epartureTimeDate == null ? "": shipLine.epartureTimeDate.substring(0,10) }}</span> 配载单
    </el-divider>
    <div style="text-align: right;">
    <el-button @click="printPz">打印</el-button>
  </div>
    <div id="hangci-biaoti-id" class="hangci-biaoti" style="position: relative">
      <div id="peizaidan">
        <div>
          <span class="hangci-biaoti12">挂账号：{{publicFmtn(shipLine.onAccountNum)}} </span>
          <span v-if="stcode === '30'" class="hangci-biaoti12">船东：{{name}}</span>
          <span class="hangci-biaoti12">船东结算公司：{{fmtsuNamen(shipLine.settleCompany)}}</span>
          <span class="hangci-biaoti12">船舶类型：{{businessTypeName}}</span>
          <span class="hangci-biaoti12"> 租船人：{{fmtUserNamen(findShipUser)}}</span>
        </div>
        <div style="margin-top:10px;">
          <span class="hangci-biaoti12">起运港：{{startPort||'--'}}</span>
          <span class="hangci-biaoti12">目的港：{{endPort||'--'}}</span>
          <span class="hangci-biaoti12">装货码头：{{loadWharf||'--'}}</span>
          <span class="hangci-biaoti12">卸货码头：{{finalWharf||'--'}}</span>
          <span class="hangci-biaoti12">吨位：{{totalTon||'--'}}</span>
          <span class="hangci-biaoti12">货种：{{goodsType||'--'}}</span>
        </div>
        <div style="margin-top:10px;">
          <span v-if="stcode ==='30'" class="hangci-biaoti12">整船利润：【含税：{{parseFloat(taxIncomnei).toFixed(2) }} 】 【不含税：{{parseFloat(noTaxIncomnei).toFixed(2)}}】</span>
          <span v-if="stcode !=='30'" class="hangci-biaoti12">整船利润：【含税：{{parseFloat(taxIncomwai).toFixed(2) }} 】 【不含税：{{parseFloat(noTaxIncomwai).toFixed(2)}}】</span>
          <span v-if="queren==1&&preStatus==2" class="hangci-biaoti12">航次利润：{{profits.toFixed(2)}}（总账）,{{chenggongprofits.toFixed(2)}}（成功）,{{heshengprofits.toFixed(2)}}（和盛）</span>
          <span class="hangci-biaoti12" v-if="stcode ==='30'">定金：{{shipLine.dingjin}}</span>

        </div>
      </div>

      <span class="hangci-biaoti12">
        航次状态：
        <span v-if="shipLine.preStatus==0" class="hangci-biaoti-yupeizai">未配载</span>
        <span v-if="shipLine.preStatus==1" class="hangci-biaoti-yupeizai">预配载中</span>
        <span v-if="shipLine.preStatus==2" class="hangci-biaoti-yupeizai">配载完成</span>
      </span>

      <el-switch
        class="hsbhs hide-print"
        @change="hsbhs"
        v-model="isTaxIncluded"
        active-text="不含税配载单"
        inactive-text="含税配载单"
        active-color="#409EFF"
        inactive-color="#409EFF"
      >
      </el-switch>
      <div class="hide-print" style="float:right;text-align: right;position: absolute;top: 30px;right: 0">
        <el-tag style="margin-right: 30px;cursor: pointer;" :type="stcode==item.code?'success':'info'"
                v-for="(item,index) in  receiveTypelist" :key="item.code" @click="checkst(item.code)"
        >{{item.name}}</el-tag>
      </div>
    </div>
    <vxe-toolbar
        ref="xToolbar"
        custom
      />
    <vxe-table
      :data="datalist"
      class="mytable-scrollbar pzd-main"
      stripe
      :loading="biaoloading"
      :column-config="{resizable: true}"
      :row-config="{isHover: true}"
      :footer-method="footerMethod1"
      show-footer
      size="small"
      ref="xTable"
      :merge-cells="mergeCells"
      border
      align="center"
      show-overflow
      max-height="430"
      highlight-current-row
      highlight-current-column
    >
      <vxe-table-column fixed="left" field="onename" :formatter="publicFmt" title="一级客户" width="90"></vxe-table-column>
      <vxe-table-column fixed="left" field="customerName" :formatter="publicFmt" title="二级客户" width="90"></vxe-table-column>
      <vxe-table-column fixed="left" field="goodsType" :formatter="goods_typeFmt" title="货种" width="70"></vxe-table-column>
      <vxe-table-column fixed="left" field="startPort" :formatter="publicFmt" title="起始港" width="70"></vxe-table-column>
      <vxe-table-column fixed="left" field="endPort" :formatter="publicFmt" title="目的港" width="70"></vxe-table-column>
      <vxe-table-column fixed="left" field="loadWharf" :formatter="publicFmt" title="装货码头" width="80"></vxe-table-column>
      <vxe-table-column fixed="left" field="finalWharf" :formatter="publicFmt" title="卸货码头" width="80"></vxe-table-column>
      <vxe-table-column fixed="left" field="wharfName" :formatter="publicFmt" title="终到地" width="70"></vxe-table-column>
      <vxe-table-column fixed="left" field="tonnage" :formatter="publicFmtnumber3" title="重量(吨)" width="100"></vxe-table-column>
      <vxe-table-column fixed="left" field="lighterTonnage" :formatter="publicFmtnumber3" title="到库吨位(吨)" width="100"></vxe-table-column>
      <vxe-table-colgroup title="运费收入(元/吨)" v-if="!isTaxIncluded" key="1">
        <vxe-table-column field="freightNo" :formatter="publicFmt" title="海运费" width="70"></vxe-table-column>
        <vxe-table-column field="dailiNo" :formatter="publicFmt" title="代理费" width="70"></vxe-table-column>
        <vxe-table-column field="sumYunCost" :formatter="publicFmtnumber" :title="'运费\n合计'" width="100"></vxe-table-column>
        <vxe-table-column field="sumAgentCost" :formatter="publicFmtnumber" :title="'代理费\n合计'" width="90"></vxe-table-column>
        <vxe-table-column v-if="stcode == '30'" :title="'是否\n开票'" width="50">
          <template slot-scope="scope">
            <span style="color: green;" v-if="scope.row.invoiceDaili !== 10 && scope.row.invoiceDaili !== null ">✓</span>
            <span style="color: red;" v-if="scope.row.invoiceDaili === 10">×</span>
            <span v-if="scope.row.invoiceDaili=== '' || scope.row.invoiceDaili === null ">--</span>
          </template>
        </vxe-table-column>
        <vxe-table-column v-if="stcode == '30'" field="contractCompany" :formatter="companyFmt" title="合同公司" width="80"></vxe-table-column>
        <vxe-table-column :title="'是否\n确认'" v-if="zhanshi && stcode == '30'"  width="50">
          <template slot-scope="scope">

            <span style="color: green;" v-if="scope.row.queren == 1">✓</span>
              <span style="color: red;" v-if="scope.row.queren == 0">×</span>

            <span v-if="scope.row.queren==99 || scope.row.queren==null ">--</span>
          </template>
        </vxe-table-column>
      </vxe-table-colgroup>
      <vxe-table-colgroup title="运费收入(元/吨)" v-if="isTaxIncluded" key="2">
        <vxe-table-column field="freight" :formatter="publicFmt" title="海运费" width="70"></vxe-table-column>
        <vxe-table-column field="daili" :formatter="publicFmt" title="代理费" width="70"></vxe-table-column>
        <vxe-table-column field="sumYunCost" :formatter="publicFmtnumber" title="运费合计" width="100"></vxe-table-column>
        <vxe-table-column field="sumAgentCost" :formatter="publicFmtnumber" title="代理费合计" width="90"></vxe-table-column>
        <vxe-table-column v-if="stcode == '30'"  :title="'是否\n开票'" width="50">
          <template slot-scope="scope">

            <span style="color: red;" v-if="scope.row.invoiceDaili !== 10 && scope.row.invoiceDaili !== null ">×</span>
            <span style="color: green;" v-if="scope.row.invoiceDaili === 10">✓</span>

            <span v-if="scope.row.invoiceDaili=== '' || scope.row.invoiceDaili === null ">--</span>
          </template>
        </vxe-table-column>
        <vxe-table-column v-if="stcode == '30'" field="contractCompany" :formatter="companyFmt" title="合同公司" width="80"></vxe-table-column>
        <vxe-table-column :title="'是否\n确认'" v-if="zhanshi && stcode == '30'" width="50">
          <template slot-scope="scope">

            <span style="color: red;" v-if="scope.row.queren == 0">×</span>
            <span style="color: green;" v-if="scope.row.queren == 1">✓</span>

            <span v-if="scope.row.queren==99 || scope.row.queren==null ">--</span>
          </template>
        </vxe-table-column>
      </vxe-table-colgroup>
      <vxe-table-colgroup title="运费支出(元/吨)" v-if="!isTaxIncluded" key="3" >
        <vxe-table-column v-if="!isTaxIncluded && stcode == '30'" field="shipPay" :formatter="publicFmt" title="船价" width="50"></vxe-table-column>
        <vxe-table-column   v-if="!isTaxIncluded && stcode != '30'" field="shipPay1" :formatter="publicFmt" title="船价" width="50"></vxe-table-column>
        <vxe-table-column  field="sumPric" :formatter="publicFmtnumber" title="总金额" width="95">
        </vxe-table-column>
        <vxe-table-column  v-if="stcode == '30'" :title="'是否\n开票'" width="50">
          <template slot-scope="scope">
            <span style="color: green;" v-if="scope.row.payBill !== 10 && scope.row.payBill !== null ">✓</span>
            <span style="color: red;" v-if="scope.row.payBill === 10">×</span>
            <span v-if="scope.row.payBill=== '' || scope.row.payBill === null ">--</span>
          </template>
        </vxe-table-column>
        <vxe-table-column v-if="stcode == '30'" field="shipcompany" :formatter="companyFmt" title="合同公司" width="82"></vxe-table-column>
        <vxe-table-column v-if="stcode == '30' && shipLine.dianMoney " field="dianCompany" :formatter="dian_companyFmt" title="垫资公司" width="82"></vxe-table-column>
        <vxe-table-column v-if="!isTaxIncluded && stcode == '30' && shipLine.dianMoney " field="ticketPrice" :formatter="publicFmtnumber" :title="'垫资\n开票价'" width="80">
          <template slot-scope="scope">
            <div v-if="scope.row.contractCompany === '20' || scope.row.contractCompany === '10'">{{publicFmtnumber2(null,null,scope.row.ticketPrice,null)}}</div>
            <div v-if="scope.row.contractCompany !== '20' && scope.row.contractCompany !== '10'">0</div>
          </template>
        </vxe-table-column>
        <vxe-table-column v-if="stcode == '30' && shipLine.maiMoney" title="卖票公司"  width="82">
          <template slot-scope="scope">
            <div v-if="scope.row.ticketPrice1">{{companyFmtn(scope.row.shipcompany)}}</div>
            <div v-if="!scope.row.ticketPrice1">{{"--"}}</div>
          </template>
        </vxe-table-column>
        <vxe-table-column v-if="!isTaxIncluded && stcode== '30' && shipLine.maiMoney" field="ticketPrice1" :formatter="publicFmtnumber" :title="'卖票\n开票价'"  width="50">
          <template slot-scope="scope">
            <div v-if="scope.row.contractCompany === '20' || scope.row.contractCompany === '10'">{{publicFmtnumber2(null,null,scope.row.ticketPrice1,null)}}</div>
            <div v-if="scope.row.contractCompany !== '20' && scope.row.contractCompany !== '10'">0</div>
          </template>
        </vxe-table-column>
        <vxe-table-column  :title="'是否确认'" v-if="zhanshi&&stcode == '30'" width="50">
          <template slot-scope="scope">
            <span style="color: green;" v-if="scope.row.slqueren == 1">✓</span>
              <span style="color: red;" v-if="scope.row.slqueren == 0">×</span>
            <span v-if="scope.row.slqueren==99 || scope.row.slqueren==null ">--</span>
          </template>
        </vxe-table-column>
      </vxe-table-colgroup>
      <vxe-table-colgroup  title="运费支出(元/吨)" v-if="isTaxIncluded" key="4">
        <vxe-table-column  v-if="isTaxIncluded && stcode == '30'" field="shipPay" :formatter="publicFmt" title="船价" width="50"></vxe-table-column>
        <vxe-table-column  v-if="!isTaxIncluded && stcode != '30'" field="shipPay1" :formatter="publicFmt" title="船价" width="50"></vxe-table-column>
        <vxe-table-column  field="sumPric" :formatter="publicFmtnumber" title="总金额" width="95">
        </vxe-table-column>
        <vxe-table-column v-if="stcode == '30'" :title="'是否\n开票'" width="50">
          <template slot-scope="scope">
            <span style="color: green;" v-if="scope.row.payBill !== 10 && scope.row.payBill !== null">✓</span>
            <span style="color: red;" v-if="scope.row.payBill === 10">×</span>
            <span v-if="scope.row.payBill=== '' || scope.row.payBill === null ">--</span>
          </template>
        </vxe-table-column>
        <vxe-table-column v-if="stcode == '30'" field="shipcompany" :formatter="companyFmt" title="合同公司" width="82"></vxe-table-column>
        <vxe-table-column v-if="stcode == '30' && shipLine.dianMoney " field="dianCompany" :formatter="dian_companyFmt" title="垫资公司" width="82"></vxe-table-column>
        <vxe-table-column v-if="isTaxIncluded && stcode == '30' && shipLine.dianMoney " field="ticketPriceNo" :formatter="publicFmtnumber" :title="'垫资\b开票价'" width="82">
          <template slot-scope="scope">
            <div v-if="scope.row.contractCompany === '20' || scope.row.contractCompany === '10'">{{publicFmtnumber2(null,null,scope.row.ticketPriceNo,null)}}</div>
            <div v-if="scope.row.contractCompany !== '20' && scope.row.contractCompany !== '10'">0</div>
          </template>        </vxe-table-column>
        <vxe-table-column v-if="stcode == '30' && shipLine.maiMoney "  title="卖票公司" :title="'卖票公司'" width="82">
          <template slot-scope="scope">
            <div v-if="scope.row.ticketPrice1">{{companyFmtn(scope.row.shipcompany)}}</div>
            <div v-if="!scope.row.ticketPrice1">{{"--"}}</div>
          </template>
        </vxe-table-column>
        <vxe-table-column v-if="isTaxIncluded&&stcode == '30' && shipLine.maiMoney" field="ticketPrice1No" :formatter="publicFmtnumber" :title="'卖票\n开票价'"  width="50">
          <template slot-scope="scope">
            <div v-if="scope.row.contractCompany === '20' || scope.row.contractCompany === '10'">{{publicFmtnumber2(null,null,scope.row.ticketPrice1No,null)}}</div>
            <div v-if="scope.row.contractCompany !== '20' && scope.row.contractCompany !== '10'">0</div>
          </template>
        </vxe-table-column>

        <vxe-table-column  :title="'是否\n确认'" v-if="zhanshi&&stcode == '30'" width="50">
          <template slot-scope="scope">
            <span style="color: green;" v-if="scope.row.slqueren == 1">✓</span>
              <span style="color: red;" v-if="scope.row.slqueren == 0">×</span>
            <span v-if="scope.row.slqueren==99 || scope.row.slqueren==null ">--</span>
          </template>
        </vxe-table-column>
        <!-- <el-table-column field="xnmoney" title="成本"></el-table-column> -->
      </vxe-table-colgroup>
      <vxe-table-colgroup  v-if="!isTaxIncluded" title="货物费用(元/吨)">
        <vxe-table-column
          v-for="(item,index)  in costNames"
          :key="index"
          :field="`customerGoodsCostDetail.${index}`"
          :formatter="cost_typeFmt"
          :title="cost_typeFmt({'cellValue':item})"
          width="130"
        ></vxe-table-column>
        <!--        :title="cost_typeFmt({null,null,item,null})"-->
      </vxe-table-colgroup>
      <vxe-table-colgroup v-if="isTaxIncluded" title="货物费用(元/吨)">
        <vxe-table-column
          v-for="(item,index)  in costNames"
          :key="index"
          :field="`customerGoodsCostDetailshui.${index}`"
          width="130"
          :formatter="cost_typeFmt"
          :title="cost_typeFmt({'cellValue':item})"
        ></vxe-table-column>
      </vxe-table-colgroup>
      <vxe-table-column v-if="stcode == '30'" field="inTaxSingleIncome"   :formatter="fmtTaxSingIncome"  title="不含税单吨利润" width="90"></vxe-table-column>
      <vxe-table-column v-if="stcode == '30'" field="findGoodsUser" :formatter="fmtUserName" title="业务员" width="90"></vxe-table-column>
      <vxe-table-column v-if="stcode == '30'" field="goodsSource" :formatter="goods_sourceFmt" title="货源类型" width="100"></vxe-table-column>
    </vxe-table>

    <div id="print-other-id">
    <div class="contract-list-box global-border-block" style="margin-top: 20px" v-if="shipPriceOutList.length>0 ">
      <!-- <span style="font-size: 20px;font-weight: 500">船上费用支出</span> -->
      <el-divider content-position="left" >船上费用支出 </el-divider>
      <div class="global-table-box">
        <table cellpadding="0" cellspacing="0" border="1" class="global-table">
          <thead>
          <tr>
            <th style="text-align: center;width:25%;">类型</th>
            <th style="text-align: center;width:25%;">金额/税率/供应商</th>
            <th style="text-align: center;width:25%;">承担方</th>
            <th style="text-align: center;width:25%;">付款方</th>
          </tr>
          </thead>
          <tbody>
          <tr v-for="(item,index)  in shipPriceOutList" :key="index+item.costProject+item.costPriceNo">
            <td   style="text-align: center;" >
              <span  >  {{items_occurredFmt(item.costProject)}} </span>

              <span style="color: red" v-if="item.costBear == 10 && item.incomeCompany==10" >  (网) </span>

            </td>
            <td   style="text-align: center;" >
              <span v-if="isTaxIncluded"> {{(item.costPriceNo/bill_taxFmt(item.tax)).toFixed(2)}}元/{{fmtsuNamen(item.sysSupplierId)}}</span>
              <span  v-else> {{item.costPriceNo}}元/{{bill_taxFmt(item.tax)}}/{{fmtsuNamen(item.sysSupplierId)}}</span>
            </td>
            <td   style="text-align: center;" >
              <span  >  {{ undertakerFmt(item.costBear)||'--' }}承担</span>
            </td>
            <td   style="text-align: center;" >
               <span  >  {{getpayer(item.payer)}}</span>
            </td>
          </tr>
          </tbody>
          <tfoot>
            <tr>
              <td style="text-align: center;" >
                合计
              </td>
              <td style="text-align: center;">{{ shipPriceOutSum }}</td>
              <td></td>
              <td></td>
            </tr>
          </tfoot>
        </table>
      </div>
    </div>
    <!-- <div class="contract-list-box global-border-block" style="margin-top: 20px" v-if="shipcostoutlist.length>0 && stcode === '30'"> -->
      <!-- <span style="font-size: 20px;font-weight: 500">船上费用支出</span> -->
      <!-- <el-divider content-position="left" >船上费用支出 </el-divider> -->
      <!-- <div class="global-table-box">
        <table cellpadding="0" cellspacing="0" border="1" class="global-table">
          <thead>
          <tr>
            <th v-if=" stcode !='30' ? undertakerFmt(item.costBear) == '公司' ? true : false :true "  style="text-align: center" v-for="(item,index)  in shipcostoutlist">
              <span v-if="item.isIncome === 0" >  {{items_occurredFmt(item.costProject)}} </span>
              <span v-else >  代理费(收入) </span>            </th>
          </tr>
          </thead>
          <tr>
            <td  v-if=" stcode !='30' ? undertakerFmt(item.costBear) == '公司' ? true : false :true "  style="text-align: center;height: 56px;" v-for="(item,index)  in shipcostoutlist">
              <span v-if="isTaxIncluded"> {{(item.costPriceNo/bill_taxFmt(item.tax)).toFixed(2)}}元/{{fmtsuNamen(item.sysSupplierId)}}</span>
              <span  v-else> {{item.costPriceNo}}元/{{bill_taxFmt(item.tax)}}/{{fmtsuNamen(item.sysSupplierId)}}</span>

            </td>
          </tr>
          <tr>
            <td v-if=" stcode !='30' ? undertakerFmt(item.costBear) == '公司' ? true : false :true "  style="text-align: center;height: 56px;" v-for="(item,index)  in shipcostoutlist">
              <span v-if="item.isIncome === 0" >  {{undertakerFmt(item.costBear)}}承担</span> </td>
          </tr>
          <tr>
            <td v-if=" stcode !='30' ? undertakerFmt(item.costBear) == '公司' ? true : false :true "  style="text-align: center;height: 56px;" v-for="(item,index)  in shipcostoutlist">
              <span v-if="item.isIncome === 0" >  {{getpayer(item.payer)}}</span> </td>
          </tr>
        </table>
      </div> -->
    <!-- </div> -->
    <div class="contract-list-box global-border-block hide-print" style="margin-top: 20px" id="hwfysr-wrapper" v-if="goodsPriceOutList && goodsPriceOutList.length>0">
      <!-- <span style="font-size: 15px;font-weight: 500">货物费用收入 </span> -->
      <el-divider content-position="left" >货物费用支出 </el-divider>
      <div class="global-table-box">
        <table cellpadding="0" cellspacing="0" border="1" class="global-table" id="huowufeiyong">
          <thead>
            <tr>
            <th style="text-align: center;width:25%;">类型</th>
            <th style="text-align: center;width:25%;">金额/税率/供应商</th>
            <th style="text-align: center;width:25%;">付款方</th>
            <th style="text-align: center;width:25%;">小计</th>
          </tr>
          <!-- <tr> -->
            <!-- <th   style="text-align: center" v-for="(item,index)  in goodsPriceOutList">
              <span >  {{cost_typeFmt({'cellValue':item.costName})}} </span>
            </th> -->
          <!-- </tr> -->
          </thead>
          <tbody>
            <template v-for="ditem in goodsPriceOutListCostList">
            <tr v-for="(item,index)  in ditem.list" :key="index+'-'+item.costName">
              <td style="text-align: center"> <span > {{cost_typeFmt({'cellValue':item.costName})}} </span></td>
              <td style="text-align: center">
                <span v-if="isTaxIncluded"> {{numbern(item.price)}}元/{{fmtsuNamen(item.companyName)}}</span>
              <span  v-else> {{numbern(item.priceNo)}}元/{{bill_taxFmt(item.invoiceType)}}/{{fmtsuNamen(item.companyName)}}</span>
              </td>
              <td style="text-align: center">
                <span >  {{getpayer(item.payer)||'--'}}</span>
              </td>
              <td style="text-align: center" v-if="index==0" :rowspan="getLenByNameProToLen(item.costName)">
                {{ modelGoodsOutSumPrice[item.costName] }}
              </td>
            </tr>
          </template>
          </tbody>
          <tfoot>
            <tr >
              <td style="text-align: center" >
                合计
              </td>
              <td></td>
              <td></td>
              <td style="text-align: center">{{ goodsPriceOutSum }}</td>
            </tr>
          </tfoot>
          <!-- <tr>
            <td    style="text-align: center;height: 56px;" v-for="(item,index)  in goodsPriceOutList">
              <span v-if="isTaxIncluded"> {{numbern(item.price)}}元/{{fmtsuNamen(item.companyName)}}</span>
              <span  v-else> {{numbern(item.priceNo)}}元/{{bill_taxFmt(item.invoiceType)}}/{{fmtsuNamen(item.companyName)}}</span>

            </td>
          </tr>
          <tr>
            <td  style="text-align: center;height: 56px;" v-for="(item,index)  in goodsPriceOutList">
              <span >  {{getpayer(item.payer)||'--'}}</span> </td>
          </tr> -->
          <!-- <tr> -->
            <!-- :colspan="getLenByNamePro('goodsPriceOutList','costName',item,goodsPriceOutList)" -->
            <!-- <td style="text-align: center;height: 56px;" :colspan="getLenByNameProToLen(item)"  v-for="item in goodsPriceOutListCostDis" >
              {{cost_typeFmt({'cellValue':item})}} 合计：{{ modelGoodsOutSumPrice[item] }}
            </td> -->
          <!-- </tr> -->
          <!-- <tr>
            <td style="text-align: center;height: 56px;" :colspan="goodsPriceOutList.length">
              合计：{{ goodsPriceOutSum }}
            </td>
          </tr> -->
        </table>

      </div>
    </div>

    <!-- <div class="contract-list-box global-border-block" style="margin-top: 20px" v-if="shipcostinlist.length>0 && stcode !== '30'"> -->
      <!-- <span style="font-size: 20px;font-weight: 500">船上费用收入</span> -->
      <!-- <el-divider content-position="left" >船上费用收入 </el-divider>
      <div class="global-table-box">
        <table cellpadding="0" cellspacing="0" border="1" class="global-table">
          <thead>
          <tr>
            <th v-if="item.tax !== '10' && undertakerFmt(item.costBear) == '公司'"   style="text-align: center" v-for="(item,index)  in shipcostinlist">
              <span>  {{items_occurredFmt(item.costProject)}} </span>
            </th>
          </tr>
          </thead>
          <tr>
            <td v-if="item.tax !== '10' && undertakerFmt(item.costBear) == '公司'"   style="text-align: center;height: 56px;" v-for="(item,index)  in shipcostinlist">
              <span v-if="isTaxIncluded"> {{(item.costPriceNo/bill_taxFmt(item.tax)).toFixed(2)}}元/{{fmtsuNamen(item.sysSupplierId)}}</span>
              <span  v-else> {{item.costPriceNo}}元/{{bill_taxFmt(item.tax)}}/{{fmtsuNamen(item.sysSupplierId)}}</span>

            </td>
          </tr>
          <tr>
            <td v-if="item.tax !== '10' && undertakerFmt(item.costBear) == '公司'"  style="text-align: center;height: 56px;" v-for="(item,index)  in shipcostinlist">
              <span v-if="item.isIncome === 1" >  公司承担</span> </td>
          </tr>
          <tr>
            <td v-if="item.tax !== '10' && undertakerFmt(item.costBear) == '公司'"  style="text-align: center;height: 56px;" v-for="(item,index)  in shipcostinlist">
              <span v-if="item.isIncome === 1" >  {{getpayer(item.payer)}}</span> </td>
          </tr>
        </table>
        <div>合计：</div>
      </div> -->
    <!-- </div> -->

    <div class="contract-list-box global-border-block" style="margin-top: 20px" v-if="shipPriceInList.length>0 ">
      <!-- <span style="font-size: 20px;font-weight: 500">船上费用收入</span> -->
      <el-divider content-position="left" >船上费用收入 </el-divider>
      <div class="global-table-box">
        <table cellpadding="0" cellspacing="0" border="1" class="global-table">
          <!-- <thead>
          <tr>
            <th   style="text-align: center" v-for="(item,index)  in shipPriceInList">
              <span>  {{items_occurredFmt(item.costProject)}} </span>
            </th>
          </tr>
          </thead>
          <tr>
            <td    style="text-align: center;height: 56px;" v-for="(item,index)  in shipPriceInList">
              <span v-if="isTaxIncluded"> {{(item.costPriceNo/bill_taxFmt(item.tax)).toFixed(2)}}元/{{fmtsuNamen(item.sysSupplierId)}}</span>
              <span  v-else> {{item.costPriceNo}}元/{{bill_taxFmt(item.tax)}}/{{fmtsuNamen(item.sysSupplierId)}}</span>

            </td>
          </tr>
          <tr>
            <td   style="text-align: center;height: 56px;" v-for="(item,index)  in shipPriceInList">
              <span  >  {{undertakerFmt(item.costBear)}}承担</span> </td>
          </tr>
          <tr>
            <td   style="text-align: center;height: 56px;" v-for="(item,index)  in shipPriceInList">
              <span  >  {{getpayer(item.payer)}}</span> </td>
          </tr>

          <tr>
            <td   style="text-align: center;height: 56px;" :colspan="shipPriceInList.length">
              <span  > 合计：{{  shipPriceInSum }}</span> </td>
          </tr> -->
          <thead>
            <tr>
                <th style="text-align: center;width:25%;">类型</th>
                <th style="text-align: center;width:25%;">金额/税率/供应商</th>
                <th style="text-align: center;width:25%;">承担方</th>
                <th style="text-align: center;width:25%;">付款方</th>
              </tr>
            </thead>
            <tbody>
            <tr v-for="(item,index)  in shipPriceInList" :key="index+'-'+item.costProject+item.costPriceNo">
              <td style="text-align: center;" >
                <span>  {{items_occurredFmt(item.costProject)}} </span>
              </td>
              <td style="text-align: center;">
                <span v-if="isTaxIncluded"> {{(item.costPriceNo/bill_taxFmt(item.tax)).toFixed(2)}}元/{{fmtsuNamen(item.sysSupplierId)}}</span>
              <span  v-else> {{item.costPriceNo}}元/{{bill_taxFmt(item.tax)}}/{{fmtsuNamen(item.sysSupplierId)}}</span>
              </td>
              <td style="text-align: center;">
                <span  >  {{undertakerFmt(item.costBear)}}承担</span>
              </td>
              <td style="text-align: center;">
                <span  >  {{getpayer(item.payer)}}</span>
              </td>
            </tr>
            </tbody>
            <tfoot>
              <tr>
                <td style="text-align: center;">合计</td>
                <td style="text-align: center;">{{  shipPriceInSum }}</td>
                <td></td>
                <td></td>

              </tr>
            </tfoot>
        </table>
      </div>
    </div>
    <div class="contract-list-box global-border-block" style="margin-top: 20px" id="hwfysr-wrapper" v-if="goodsPriceInList && goodsPriceInList.length>0 ">
      <!-- <span style="font-size: 15px;font-weight: 500">货物费用收入 </span> -->
      <el-divider content-position="left" >货物费用收入 </el-divider>
      <div class="global-table-box">
        <table cellpadding="0" cellspacing="0" border="1" class="global-table" id="huowufeiyong">
          <!-- <thead>
          <tr>
            <th   style="text-align: center" v-for="(item,index)  in goodsPriceInList">
              <span >  {{costInFmtn(item.costName)}} </span>
            </th>
          </tr>
          </thead>
          <tr>
            <td    style="text-align: center;height: 56px;" v-for="(item,index)  in goodsPriceInList">
              <span v-if="isTaxIncluded"> {{numbern(item.price)}}元/{{fmtkeNamen(item.companyName)}}</span>
              <span  v-else> {{numbern(item.priceNo)}}元/{{bill_taxFmt(item.invoiceType)}}/{{fmtkeNamen(item.companyName)}}</span>

            </td>
          </tr>
          <tr> -->
            <!-- :colspan="getLenByNamePro('goodsPriceInList','costName',item,goodsPriceInList)" -->
            <!-- <td style="text-align: center;height: 56px;" :colspan="getLenByNameInProToLen(item)"  v-for="item in goodsPriceInListCostDis" >
              {{costInFmtn(item)}} 合计：{{ modelGoodsInSumPrice[item]||'--' }}
            </td>
          </tr>
          <tr>
            <td style="text-align: center;height: 56px;" :colspan="goodsPriceInList.length">
              <span  > 合计：{{  goodsPriceInSum }}</span> </td>
          </tr> -->
          <thead>
            <tr>
                <th style="text-align: center;width:33%;">类型</th>
                <th style="text-align: center;width:34%;">金额/税率/供应商</th>
                <th style="text-align: center;width:33%;">小计</th>
              </tr>
          </thead>
          <tbody>
          <template v-for="ditem in goodsPriceInListCostList">
            <template v-for="(item,index) in ditem.list">
            <tr  :key="index+'-'+item.costName">
              <td style="text-align: center;"><span >  {{costInFmtn(item.costName)}} </span></td>
              <td style="text-align: center;"><span v-if="isTaxIncluded"> {{numbern(item.price)}}元/{{fmtkeNamen(item.companyName)}}</span>
              <span  v-else> {{numbern(item.priceNo)}}元/{{bill_taxFmt(item.invoiceType)}}/{{fmtkeNamen(item.companyName)}}</span></td>
              <td style="text-align: center;" v-if="index==0" :rowspan="getLenByNameInProToLen(item.costName)">
                {{ modelGoodsInSumPrice[item.costName]||'--' }}
              </td>
            </tr>
            </template>
          </template>
          </tbody>
          <tfoot>
            <tr>
              <td style="text-align: center;">合计</td>
              <td></td>
              <td style="text-align: center;">{{  goodsPriceInSum }}</td>
            </tr>
          </tfoot>
        </table>
      </div>
    </div>
    <div id="sp-list-id" style="margin-top:20px;display:flex;justify-content: space-around;font-size:14px;">
      <div v-for="litem in spLogList" :key="litem.id">
      <span v-if="litem.nodeLabel">{{ litem.nodeLabel }}:{{ litem.userName||'--'}}</span>
      </div>
    </div>
  </div>
  </div>
</template>


<script>
  import 'xe-utils'
  import 'vxe-table/lib/style.css'
  import {
    fmtDictionary
  } from "@/utils/util";
  import {
    calcMergeOfColumns
  } from "@/utils/utils";
  import {getShipLineById, getStowageDetil} from "../../../api/system/onAccount";
  import {getDictionaryList} from "../../../api/system/baseInit";
import { getWuLiuSpPerspnListByProcessId } from '@/api/system/process'
import  currency from 'currency.js'

  export default {
    name: "StowageDetail",
    data() {
      return {
        mergeCells: [],
        taxIncomnei:null,
        noTaxIncomnei: null,
        taxIncomwai:null,
        noTaxIncomwai: null,
        businessTypeName:null,
        name:null,
        findShipUser: null,
        isTaxIncluded:false,//是否是含税详情
        shipLineId: "",
        biaoloading:false,
        receiveTypelist:[
          {
            name:"成功",
            code:"10"
          },
          {
            name:"和盛",
            code:"20"
          },
          {
            name:"总单",
            code:"30"
          },
        ],
        userList: [],
        zhanshi:true,
        detailin: [],
        detailout: [],
        stcode:"30",
        dictionaryLists: {},
        datalist: [],
        costNames: [],
        shipName: "--",
        shipLine: {},
        shipcostlist: [],
        allshipcostlist: [],
        shipcostoutlist:[],
        shipcostinlist:[],
        allhuowulist:[],
        profits: "--",
        queren: "",
        preStatus: "",
        heshengprofits:0,
        chenggongprofits:0,
        supplierList:[],
        zhanshilist:[],
        allcostName:[],
        shiplinezz:[],
        shippaylist:[],
        yun:0,
        dai:0,
        allCustomerList: [],
        spLogList: [],
        startPort:'',
        endPort:'',
        loadWharf:'',
        finalWharf:'',
        goodsType:'',
        totalTon: '',
        deptCode:''
      };
  },
  computed: {
    modelGoodsOutSumPrice() {
      if (this.stcode == '30') {
        if (this.isTaxIncluded) {
          return this.detailOutModelSum
        } else {
          return this.detailOutModelSumNo
        }
      } else {
        if (this.isTaxIncluded) {
          return this.detailOutModelSumIs1
        } else {
          return this.detailOutModelSumIs1No
        }
      }
    },
    modelGoodsInSumPrice() {
      if (this.stcode == '30') {
        if (this.isTaxIncluded) {
          return this.detailInModelSum
        } else {
          return this.detailInModelSumNo
        }
      } else {
        if (this.isTaxIncluded) {
          return this.detailInModelSumIs1
        } else {
          return this.detailInModelSumIs1No
        }
      }
    },
    goodsPriceOutListCostList() {
      // list 转换 [key:[list]]
      if (this.goodsPriceOutList && this.goodsPriceOutList.length > 0) {
        const list = []
        this.goodsPriceOutListCostDis.forEach(it => {
          list.push({
            costName: it,
            list: this.goodsPriceOutList.filter(item => item.costName == it)
          })
        })
        return list
      }
      return []
    },
    goodsPriceOutListCostDis() {
      if (this.goodsPriceOutList && this.goodsPriceOutList.length > 0) {
        // costName 取唯一值
        return this.goodsPriceOutList.map(item => item.costName).filter((item, index, arr) => arr.indexOf(item) === index)
      }
      return []
    },
    getLenByNameProToLen() {
      return (it) => {
        return this.goodsPriceOutList.filter(item => item.costName == it).length
      }
    },
    getLenByNameInProToLen() {
      return (it) => {
        return this.goodsPriceInList.filter(item => item.costName == it).length
      }
    },
    goodsPriceInListCostDis() {
      if (this.goodsPriceInList && this.goodsPriceInList.length > 0) {
        // costName 取唯一值
        return this.goodsPriceInList.map(item => item.costName).filter((item, index, arr) => arr.indexOf(item) === index)
      }
      return []
    },
    goodsPriceInListCostList() {
      // list 转换为 key:[list]
      if (this.goodsPriceInList && this.goodsPriceInList.length > 0) {
        const obj = {}
        this.goodsPriceInList.forEach(item => {
          if (obj[item.costName]) {
            obj[item.costName].push(item)
          } else {
            obj[item.costName] = [item]
          }
        })
        // 转换为数组
        const arr = []
        for (const key in obj) {
          arr.push({
            costName: key,
            list: obj[key]
          })
        }
        return arr
      }
      return []
    },
    shipPriceOutList() {
      if (this.shipcostoutlist && this.shipcostoutlist.length > 0) {
        if (this.stcode == '30') {
          // return this.shipcostoutlist.filter(item => item.isIncome === 1)
          return this.shipcostoutlist
        } else {
          if (this.stcode == '10') {
            // return this.shipcostoutlist.filter(item => item.tax != 10 && (this.undertakerFmt(item.costBear) == '公司' ||this.items_occurredFmt(item.costProject)=='加油费' ||(this.items_occurredFmt(item.costProject)=='港使费' && (this.fmtsuNamen(item.sysSupplierId)=='鞍钢码头' || this.fmtsuNamen(item.sysSupplierId)=='鲅鱼圈港使费'))))
            return this.shipcostoutlist.filter(item => item.tax != 10 && (this.undertakerFmt(item.costBear) == '公司' ||this.items_occurredFmt(item.costProject)=='加油费' ||this.items_occurredFmt(item.costProject)=='加油运费' ||(this.items_occurredFmt(item.costProject)=='港使费' && this.shipName!='和泰通5') ||  (this.items_occurredFmt(item.costProject)==='港使费' && this.shipName==='和泰通5' && this.fmtsuNamen(item.sysSupplierId)!='上海港使费' )))
          } else {
            // 和盛
            // return this.shipcostoutlist.filter(item => item.tax != 10 && (this.fmtsuNamen(item.sysSupplierId)=='海南和盛' || this.fmtsuNamen(item.sysSupplierId)=='盛业加油款' || this.fmtsuNamen(item.sysSupplierId)=='上海港使费'))
            return this.shipcostoutlist.filter((this.fmtsuNamen(item.sysSupplierId)=='海南和盛' || this.fmtsuNamen(item.sysSupplierId)=='盛业加油款' || this.fmtsuNamen(item.sysSupplierId)=='上海港使费'))
          }

        }
      }
      return []
    },
    shipPriceOutSum() {
      let sum = 0;
      // currency
      this.shipPriceOutList.forEach(item => {
        // sum += item.price;
        sum = currency(sum).add(item.costPriceNo).value
      });
      return sum;
    },
    shipPriceInList() {
      if(this.shipcostinlist && this.shipcostinlist.length>0){
        if(this.stcode=='30'){
          return this.shipcostinlist
        }else{
          return this.shipcostinlist.filter(item => item.tax != 10)
        }
      }
      return []
    },
    shipPriceInSum() {
      let sum = 0;
      // currency
      this.shipPriceInList.forEach(item => {
        // sum += item.price;
        sum = currency(sum).add(item.costPriceNo).value
      });
      return sum;
    },
    goodsPriceInList() {
      console.log('in list')
      // 计算 类型合计
      if (this.detailin && this.detailin.length > 0) {
        if (this.stcode == 30) {
          return this.detailin
        } else {
          return this.detailin.filter(item => {
            return item.invoiceType != 10
          })
        }
      }
      return []
    },
    goodsPriceInSum() {
      let sum = 0;
      // currency
      this.goodsPriceInList && this.goodsPriceInList.forEach(item => {
        // sum += item.price;
        sum = currency(sum).add(this.isTaxIncluded? item.price: item.priceNo).value
      });
      return sum;
    },
    goodsPriceOutList() {
      if (this.detailout && this.detailout.length > 0) {
        if (this.stcode == 30) {
          return this.detailout
        } else {
          return this.detailout.filter(item => {
            return item.invoiceType != 10
          })
        }
      }
      return []
    },
    goodsPriceOutSum() {
      let sum = 0;
      // currency
      this.goodsPriceOutList && this.goodsPriceOutList.forEach(item => {
        // sum += item.price;
        sum = currency(sum).add(this.isTaxIncluded? item.price: item.priceNo).value
      });
      return sum;
    }

  },
    created() {
      this.shipLineId = this.$route.params.shiplineId
      if (this.$route.query.deptCode) {
        this.deptCode = this.$route.query.deptCode
      } else {
        this.deptCode = ''
      }
      if (this.deptCode == 'HNHS') {
        this.$store.dispatch('data/getAllUserListSaveInVuexByHs').then(res=>{
        if(res){
          this.userList = res.allUser
        }
      })
      } else {
        this.$store.dispatch('data/getAllUserListSaveInVuex').then(res=>{
        if(res){
          this.userList = res.allUser
        }
      })
      }

      if (this.$route.query.pid) {
        this.loadSpListByProcessId(this.$route.query.pid)
      } else {
        this.spLogList = []
      }
      var dics = getDictionaryList("cost_in,cost_type,goods_type,goods_source,undertaker,items_occurred,bill_tax,dian_company,contract_company,Payer",this.deptCode);
      var getSupplier = this.$store.dispatch('data/getSupplierListSaveInVuex')
      var shipLineById = getShipLineById(this.shipLineId,this.deptCode);
      var getCustomer = this.$store.dispatch('data/getCustomerListSaveInVuex')
      Promise.all([dics, getSupplier, shipLineById, getCustomer]).then(values => {
        let res  = values[0];
        if (res != undefined) {
          this.dictionaryLists = res.map;
        }
        res = values[1];
        if (res!=undefined) {
          this.supplierList = res.supplierList
        }

        res = values[2];
        if (res!=undefined) {
          this.shiplinezz.push(res.data)
          this.taxIncomnei = res.taxIncomnei
          this.noTaxIncomnei = res.noTaxIncomnei
          this.taxIncomwai = res.taxIncomwai
          this.noTaxIncomwai = res.noTaxIncomwai
          this.businessTypeName = res.businessTypeName
          this.name = res.name
          this.findShipUser = res.findShipUser
          this.startPort = res.startPort
          this.endPort = res.endPort
          this.loadWharf = res.loadWharf
          this.finalWharf = res.finalWharf
          this.goodsType = res.goodsType
          this.totalTon = res.totalTon
        }
        res = values[3]
        if (res!=undefined) {
          this.allCustomerList = res.customerList
        }
        this.onQuery()
      });
    },
    mounted() {},
  methods: {
    footerMethod1(param) {
      if(this.stcode=='30'){
        return []
      }
      const { columns, data } = param;
      if (!data || data.length == 0) {
        return [[]];
      }
      const sums = [];
      columns.forEach((column, index) => {
        if (index === 0) {
          sums[index] = '合计';
          return;
        }
        if('tonnage' == column.property || 'lighterTonnage' == column.property){
          const values = data.map(item => Number(item[column.property]));
          if (!values.every(value => isNaN(value))) {
            sums[index] = values.reduce((prev, curr) => {
              const value = Number(curr);
              if (!isNaN(value)) {
                // 保留3位小数
                return currency(prev,{precision:3}).add(currency(curr,{precision:3})).value;
              } else {
                return prev;
              }
            }, 0);
            // sums[index] = currency(sums[index], { symbol: '', precision:3 }).format();
          } else {
            sums[index] = '';
          }
          return
        }
        if('sumYunCost' == column.property || 'sumAgentCost' == column.property ||'sumPric' == column.property  ){
          const values = data.map(item => Number(item[column.property]));
          if (!values.every(value => isNaN(value))) {
            sums[index] = values.reduce((prev, curr) => {
              const value = Number(curr);
              if (!isNaN(value)) {
                // 保留3位小数
                return currency(prev,{precision:2}).add(currency(curr,{precision:2})).value;
              } else {
                return prev;
              }
            }, 0);
            // sums[index] = currency(sums[index], { symbol: '', precision:2 }).format();
          } else {
            sums[index] = '';
          }
          return
        }

        sums[index] = '--';
      });
      return [sums];
    },
    getLenByNamePro(name, prom,v,list) {
      if (!this.glen) {
        this.glen = {}
      }
      if(!this.glen[name]){
        this.glen[name] = {}
      }
      if(!this.glen[name][v]){
        this.glen[name][v] = 0
      } else {
        return this.glen[name][v]
      }
      let len = 0
      list.forEach(item => {
        if (item[prom] == v) {
          if (this.stcode != '30' && item.invoiceType == 10) {
            return true
          }
          len += 1
        }
      })
      this.glen[name][v] = len
      return len
    },
    printPz() {
      const loading = this.$loading({
        lock: true,
        text: '正在打印',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      });
      let divEl = document.getElementById("peizaidantitle").outerHTML;
      divEl += `<div style="margin-top:10px;"></div>`
      divEl += document.getElementById("hangci-biaoti-id").outerHTML;
      this.printContentByRef('xTable').then(res => {
        if (res.content) {
          divEl += `<div style="margin-top:20px;"></div>`
          divEl += res.content
        }
        divEl += `<div style="margin-top:10px;"></div>`
        divEl += document.getElementById("print-other-id").outerHTML;
        divEl += `<style>
        .hide-print{
          display: none;
        }
        .global-table th, .global-table td {
          padding: 5px 5px;
          border: 1px solid #000;
          border-collapse: collapse;
          text-align: center;
        }
        </style>`
        // 调用打印方法
        this.$XPrint({
          sheetName: '打印配置单 挂账号：'+this.publicFmtn(this.shipLine.onAccountNum),
          content: divEl
        })
      }).finally(() => {
        loading.close();
      })
    },
    printContentByRef(refName) {
      return this.$refs[refName] && this.$refs[refName].exportData({
        original: false,
        type: 'html',
        download: false,
        remote: false,
        print: true,
        isMerge: true
      })
    },
    loadSpListByProcessId(processId) {
      this.spLogList = []
      getWuLiuSpPerspnListByProcessId(processId,this.deptCode).then(res => {
        if (res.data) {
          this.spLogList = res.data
        }
      })
    },
    checkst(code) {
      // console.log('1015-code',code,'istax',this.isTaxIncluded)
      this.stcode = code
      this.datalist.forEach(item => {
        let teshu_shipPay
        if(this.isTaxIncluded == false) { // 是否显示含税
          if(item.lighter == 0) { // 流向 0 正常 1 到库
            item.shipPay1 = (item.ticketPrice == null || item.ticketPrice == '' || item.ticketPrice == undefined)
              ? (item.ticketPrice1 == null || item.ticketPrice1 == '' || item.ticketPrice1 == undefined) ? item.shipPay : item.ticketPrice1
              : item.ticketPrice
          } else {
            if(item.id == '6b6f7cb62f53473e8dffd41fb6a21623'){
              item.shipPay1 = item.shipPay;
              teshu_shipPay = item.shipPay;
            }else {
              item.shipPay = "--"
              item.shipPay1 = "--"
            }
            item.payBill = null
            item.shipcompany = "--"
            item.ticketPrice = ""
            item.ticketPriceNo = ""
            item.ticketPrice1 = ""
            item.ticketPrice1No = ""
          }
          if(item.lighter == 0) { // 流向 0 正常 1 到库
            // 含税 运费合计
            // item.sumYunCost = (item.freightNo?item.freightNo:0) * item.tonnage
            item.sumYunCost = currency((item.freightNo?item.freightNo:0) * item.tonnage).value
            // 含税代理费合计
            // item.sumAgentCost = (item.dailiNo?item.dailiNo:0) * item.tonnage
            item.sumAgentCost = currency((item.dailiNo?item.dailiNo:0) * item.tonnage).value
            // 总金额
            // item.sumPric =  item.shipPay1 * item.tonnage
            item.sumPric =  currency(item.shipPay1 * item.tonnage).value
          } else {
            // item.sumYunCost = (item.freightNo?item.freightNo:0) * item.lighterTonnage
            item.sumYunCost = currency((item.freightNo?item.freightNo:0) * item.lighterTonnage).value
            // item.sumAgentCost = (item.dailiNo?item.dailiNo:0) * item.lighterTonnage
            item.sumAgentCost = currency((item.dailiNo?item.dailiNo:0) * item.lighterTonnage).value
            item.sumPric =  0
            if(item.id == '6b6f7cb62f53473e8dffd41fb6a21623'){
              item.sumPric = currency(teshu_shipPay * item.lighterTonnage).value
            }
          }
        }else { // 是否显示含税
          if(item.lighter == 0) {
            item.shipPay1 = (item.ticketPriceNo == null || item.ticketPriceNo == '' || item.ticketPriceNo == undefined )? (item.ticketPrice1No == null || item.ticketPrice1No == '' || item.ticketPrice1No == undefined ) ? item.shipPayNo : item.ticketPrice1No : item.ticketPriceNo
          } else {
            item.shipPay = "--"
            item.shipPay1 = "--"
            item.payBill = null
            item.shipcompany = "--"
            item.ticketPrice = ""
            item.ticketPriceNo = ""
            item.ticketPrice1 = ""
            item.ticketPrice1No = ""
          }
          if(item.lighter == 0) {
            // 不含税运费合计
            // item.sumYunCost = (item.freight?item.freight:0) * item.tonnage
            item.sumYunCost = currency((item.freight?item.freight:0) * item.tonnage).value
            // 不含税代理费合计
            // item.sumAgentCost = (item.daili?item.daili:0) * item.tonnage
            item.sumAgentCost = currency((item.daili?item.daili:0) * item.tonnage).value
            // 总金额
            // item.sumPric =  item.shipPay1 * item.tonnage
            item.sumPric =  currency(item.shipPay1 * item.tonnage).value
          } else {
            // item.sumYunCost = (item.freight?item.freight:0) * item.lighterTonnage
            item.sumYunCost = currency((item.freight?item.freight:0) * item.lighterTonnage).value
            // item.sumAgentCost =(item.daili?item.daili:0) * item.lighterTonnage
            item.sumAgentCost = currency((item.daili?item.daili:0) * item.lighterTonnage).value
            item.sumPric = 0
          }
        }
        // console.log('customerGoodsCostDetail',item.customerGoodsCostDetail)
        if(this.stcode == 10 && item.customerGoodsCostDetail && item.customerGoodsCostDetail.length>0) {
          // var newDetailList = item.customerGoodsCostDetail.filter(str=>str.indexOf("不开") ==-1)
          // 替换不开，一条记录、分隔 ['--', '1.999990/6%/天津人寿', '28.500000/6%/杂码', '70.000000/9%/尊骞', '--', '0.981832/不开/代理-垫料、19.000000/6%/上海和泰通', __ob__: Observer]
          var newDetailList = []
          item.customerGoodsCostDetail.forEach(str => {
            // console.log('customerGoodsCostDetail-str',str)
            if(str.indexOf("不开") ==-1) {
              newDetailList.push(str)
            } else {
              if (str == '--') {
                newDetailList.push(str)
                return
              }
              var arr = str.split("、")
              let str3 = ""
              arr.forEach(str1=>{
                if(str1.indexOf("不开") ==-1) {
                  // newDetailList.push(str1)
                  str3 += str1 + "、"
                }
              })
              if(str3) {
                newDetailList.push(str3.substring(0,str3.length-1))
              } else {
                newDetailList.push("--")
              }
            }

          })
          // console.log('customerGoodsCostDetail-newDetailList',newDetailList)
          item.customerGoodsCostDetail = newDetailList
        }
        // console.log('customerGoodsCostDetail-end',item.customerGoodsCostDetail)
      })
      this.biaoloading = true
      this.isTaxIncluded = !this.isTaxIncluded
      this.datalist = []
      this.costNames = []
      this.shipcostlist =[]
      if (code !== "30") { // 30 总 10 成功 20 和盛
        var z = null
        var i = 0
        var q = 0
        var p = 0
        if (this.allcostName) {
          for(i=0;i<this.allcostName.length;i++) {
            res = this.allcostName[i]
            if (res !== "6") { // 6 返点
              this.costNames.push(res)
            } else {
              z = i
            }
          }
        }
        var lists = Object.assign([], this.zhanshilist)
        for (var l = 0; l < lists.length; l++) {
          var res = Object.assign({}, lists[l])
          var list = []
          var listshui = []
          if (res.customerGoodsCostDetail) {
            for (q = 0; q < res.customerGoodsCostDetail.length; q++) {
              if (q != z) {
                list.push(res.customerGoodsCostDetail[q])
              }
            }
            for (p = 0; p < res.customerGoodsCostDetailshui.length; p++) {
              if (p != z) {
                listshui.push(res.customerGoodsCostDetailshui[p])
              }
            }
          }
          if (res.contractCompany === code) {
            this.datalist.push(res)
            this.datalist[this.datalist.length - 1].customerGoodsCostDetail = list
            this.datalist[this.datalist.length - 1].customerGoodsCostDetailshui = listshui
          }
        }

        this.shipcostoutlist = []
        this.shipcostinlist = []
        // console.log('allshipcostlist',this.allshipcostlist)
        // if (this.datalist.length > 0) {
        this.allshipcostlist.forEach(res=>{
          this.shipcostlist.push(res)
          if (res.isIncome == 0) { // 0 支出 1 收入
            this.shipcostoutlist.push(res)
          } else {
            if (res.tax !== "10") { // 10 不开票
              this.shipcostinlist.push(res)
            }
          }
        })
        this.allhuowulist.forEach(res=>{
          var xunlist = []
          res.list.forEach(resz=>{
            if (resz.tax !== "10") { // 10 不开票
              xunlist.push(resz)
            }
          })
        })
        // }
      } else { // 30 总帐 重新查询
        this.onQuery()
      }
      setTimeout(() => {
        this.isTaxIncluded = !this.isTaxIncluded
        this.biaoloading = false

        setTimeout(() => {
          this.mergeCells = calcMergeOfColumns(this.$refs.xTable, this.datalist, ['onename', 'customerName', 'goodsType']);
        }, 10);
      }, 100);
    },
      hsbhs(v){
        //alert(v)
        getStowageDetil(this.shipLineId,this.isTaxIncluded,this.deptCode).then((res) => {
          if (res != undefined ) {
            this.datalist = res.list;
            this.detailin = res.detailin;
            this.sumModeByDetailIn(res.detailin)
            this.detailout = res.detailout;
            this.sumModeByDetailOut(this.detailout)
            this.shipName = res.shipName;
            this.costNames = res.costNames;
            this.shipLine = res.shipLine;
            this.shipcostlist = res.shipcostlist;
            this.allshipcostlist = res.shipcostlist;
            this.shipcostoutlist = res.shipcostlist.filter(item=>item.isIncome === 0)
            this.shipcostinlist = res.shipcostlist.filter(item=>item.isIncome === 1)
            this.profits = res.profits;
            this.queren = res.shipLine.queren;
            this.preStatus = res.shipLine.preStatus;
            var yun = 0
            var dai = 0
            var zong = 0
            this.datalist.forEach(item=>{
              if(this.isTaxIncluded == false){
                if(item.lighter == 0){
                  item.shipPay1 = (item.ticketPrice == null || item.ticketPrice == '' || item.ticketPrice == undefined )? (item.ticketPrice1 == null || item.ticketPrice1 == '' || item.ticketPrice1 == undefined ) ? item.shipPay : item.ticketPrice1 : item.ticketPrice
                } else {
                  item.shipPay = "--"
                  item.shipPay1 = "--"
                  item.payBill = null
                  item.shipcompany = "--"
                  item.ticketPrice = ""
                  item.ticketPriceNo = ""
                  item.ticketPrice1 = ""
                  item.ticketPrice1No = ""
                }

                if(item.lighter == 0){
                  // 含税 运费合计
                  item.sumYunCost = currency((item.freightNo?item.freightNo:0) * item.tonnage).value
                  // 含税代理费合计
                  item.sumAgentCost = currency((item.dailiNo?item.dailiNo:0) * item.tonnage).value
                  // 总金额
                  item.sumPric =  currency(item.shipPay1 * item.tonnage).value
                  zong = currency(zong).add(currency(item.sumPric)).value
                } else {
                  item.sumYunCost = currency((item.freightNo?item.freightNo:0) * item.lighterTonnage).value
                  item.sumAgentCost = currency((item.dailiNo?item.dailiNo:0) * item.lighterTonnage).value
                  item.sumPric = 0
                }
                yun = currency(yun).add(currency(item.sumYunCost)).value
                // dai += item.sumAgentCost
                dai = currency(dai).add(currency(item.sumAgentCost)).value
              }else {
                if(item.lighter == 0){
                  item.shipPay1 = (item.ticketPriceNo == null || item.ticketPriceNo == '' || item.ticketPriceNo == undefined )? (item.ticketPrice1No == null || item.ticketPrice1No == '' || item.ticketPrice1No == undefined ) ? item.shipPayNo : item.ticketPrice1No : item.ticketPriceNo
                } else {
                  item.shipPay = "--"
                  item.shipPay1 = "--"
                  item.payBill = null
                  item.shipcompany = "--"
                  item.ticketPrice = ""
                  item.ticketPriceNo = ""
                  item.ticketPrice1 = ""
                  item.ticketPrice1No = ""
                }
                if(item.lighter == 0){
                  // item.sumYunCost = (item.freight?item.freight:0) * item.tonnage
                  item.sumYunCost = currency((item.freight?item.freight:0) * item.tonnage).value
                  // 不含税代理费合计
                  // item.sumAgentCost = (item.daili?item.daili:0)* item.tonnage
                  item.sumAgentCost = currency((item.daili?item.daili:0)* item.tonnage).value
                  // 总金额
                  // item.sumPric =  item.shipPay1 * item.tonnage
                  item.sumPric =  currency(item.shipPay1 * item.tonnage).value
                  // zong += item.sumPric
                  zong = currency(zong).add(currency(item.sumPric)).value
                } else {
                  // item.sumYunCost = (item.freight?item.freight:0) * item.lighterTonnage
                  item.sumYunCost = currency((item.freight?item.freight:0) * item.lighterTonnage).value
                  // item.sumAgentCost = (item.daili?item.daili:0) * item.lighterTonnage
                  item.sumAgentCost = currency((item.daili?item.daili:0) * item.lighterTonnage).value
                  item.sumPric =0
                }
                // yun += item.sumYunCost
                yun = currency(yun).add(currency(item.sumYunCost)).value
                // dai += item.sumAgentCost
                dai = currency(dai).add(currency(item.sumAgentCost)).value
              }
              if(this.stcode == 10 && item.customerGoodsCostDetail && item.customerGoodsCostDetail.length>0){
                // var newDetailList = item.customerGoodsCostDetail.filter(str=>str.indexOf("不开") ==-1)
                var newDetailList = []
                item.customerGoodsCostDetail.forEach(str => {
                  // console.log('customerGoodsCostDetail-str',str)
                  if(str.indexOf("不开") ==-1) {
                    newDetailList.push(str)
                  } else {
                    if (str == '--') {
                      newDetailList.push(str)
                      return
                    }
                    var arr = str.split("、")
                    let str3 = ""
                    arr.forEach(str1=>{
                      if(str1.indexOf("不开") ==-1) {
                        // newDetailList.push(str1)
                        str3 += str1 + "、"
                      }
                    })
                    if(str3) {
                      newDetailList.push(str3.substring(0,str3.length-1))
                    } else {
                      newDetailList.push("--")
                    }
                  }
                })
                item.customerGoodsCostDetail = newDetailList
              }
            })
            var zzz = this.datalist[this.datalist.length - 1]
            zzz.sumYunCost = yun
            zzz.sumAgentCost = dai
            zzz.sumPric = zong
            // this.mergeCells = [{ row: 1, col: 1, rowspan: 2, colspan: 2 }];
            setTimeout(() => {
              this.mergeCells = calcMergeOfColumns(this.$refs.xTable, this.datalist, ['onename', 'customerName', 'goodsType']);
            }, 100);
          }
        });
      },
      companyFmt({row, column, cellValue, index}) {
        return fmtDictionary(cellValue,this.dictionaryLists['contract_company']);
      },
      companyFmtn(cellValue) {
        return fmtDictionary(cellValue,this.dictionaryLists['contract_company']);
      },
      publicFmt({row, column, cellValue, index}) {
        var v = "--";
        if (cellValue != undefined && cellValue != null && cellValue != "") {
          v = cellValue
        }
        return v;
      },
      publicFmtn(cellValue) {
        var v = "--";
        if (cellValue != undefined && cellValue != null && cellValue != "") {
          v = cellValue
        }
        return v;
      },
      publicFmtnumber({row, column, cellValue, index}) {
        var v = 0;
        if (cellValue != undefined && cellValue != null && cellValue != "") {
          v = cellValue.toFixed(2);

        }
        return v;
      },
      publicFmtnumber3({row, column, cellValue, index}) {
        var v = 0;
        if (cellValue != undefined && cellValue != null && cellValue != "") {
          v = cellValue.toFixed(3);

        }
        return v;
      },
      publicFmtnumber2(row, column, cellValue, index) {
        return this.publicFmtnumber({row, column, cellValue, index});
      },
      numbern(cellValue){
        var v = 0;
        if (cellValue != undefined && cellValue != null && cellValue != "") {
          v = cellValue.toFixed(2);

        }
        return v;
      },
      fmtTaxSingIncome({cellValue }){
        return cellValue  == 0 ? "--" : cellValue
      },
      fmtUserName({row, column, cellValue, index}) {
        var v = "--";
        for (var i = 0; i < this.userList.length; i++) {
          var tmp = this.userList[i];
          if (cellValue == tmp.id) {
            v = tmp.name;
            break;
          }
        }
        return v;
      },
      fmtUserNamen(cellValue) {
        // console.log(this.userList)
        var v = "--";
        for (var i = 0; i < this.userList.length; i++) {
          var tmp = this.userList[i];
          if (cellValue == tmp.id) {
            v = tmp.name;
            // console.log(v )
            break;
          }
        }
        return v;
      },
      fmtsuName({row, column, cellValue, index}) {
        var v = "--";
        for (var i = 0; i < this.supplierList.length; i++) {
          var tmp = this.supplierList[i];
          if (cellValue == tmp.id) {
            v = tmp.simpleName;
            // console.log(tmp)
            break;
          }
        }
        return v;
      },
      fmtsuNamen(cellValue) {
        var v = "--";
        if(cellValue){
          v=cellValue
        }
        for (var i = 0; i < this.supplierList.length; i++) {
          var tmp = this.supplierList[i];
          if (cellValue == tmp.id) {
            v = tmp.simpleName;
            // console.log(tmp)
            break;
          }
        }
        return v;
      },
      fmtkeNamen(cellValue) {
        var v = "--";
        if(cellValue){
          v=cellValue
        }
        for (var i = 0; i < this.allCustomerList.length; i++) {
          var tmp = this.allCustomerList[i];
          if (cellValue == tmp.id) {
            v = tmp.abbreviationName;
            // console.log(tmp)
            break;
          }
        }
        return v;
      },
      dian_companyFmt({row, column, cellValue, index}){
        return fmtDictionary(cellValue, this.dictionaryLists["dian_company"]);
      },
      items_occurredFmt(cellValue){
        if(!isNaN(cellValue)){
          return fmtDictionary(Number(cellValue), this.dictionaryLists["items_occurred"]);
        } else {
          return cellValue
        }
      },
    getpayer(cellValue) {
        if(!isNaN(cellValue)){
          return fmtDictionary(Number(cellValue), this.dictionaryLists["Payer"]);
        } else {
          return cellValue
        }
        // return fmtDictionary(Number(cellValue), this.dictionaryLists["Payer"]);
      },
      items_occurredFmty({row, column, cellValue, index}){
        return fmtDictionary(cellValue, this.dictionaryLists["items_occurred"]);
      },
      undertakerFmt(cellValue){
        return fmtDictionary(cellValue, this.dictionaryLists["undertaker"]);
      },
      cost_typeFmt({row, column, cellValue, index}) {
        return fmtDictionary(cellValue, this.dictionaryLists["cost_type"]);
      },
      costInFmtn(cellValue){
        return fmtDictionary(cellValue,this.dictionaryLists['cost_in']);
      },
      goods_typeFmt({row, column, cellValue, index}) {
        return fmtDictionary(cellValue, this.dictionaryLists["goods_type"]);
      },
      goods_sourceFmt({row, column, cellValue, index}) {
        return fmtDictionary(cellValue, this.dictionaryLists["goods_source"]);
      },
      bill_taxFmt(cellValue) {
        let valNum;
        if (!this.isTaxIncluded){
          valNum =fmtDictionary(cellValue, this.dictionaryLists["bill_tax"])
        }else{
          let val = fmtDictionary(cellValue, this.dictionaryLists["bill_tax"]).substring(0,1)
          let valStr = val == "不" ? "0" : val
          valNum = (Number(valStr)* 0.01 + 1.0)
        }

        return valNum;
      },
      bill_taxFmtt({row, column, cellValue, index}) {
        return fmtDictionary(cellValue, this.dictionaryLists["bill_tax"]);
    },
    sumModeByDetailOut(list) {
      this.detailOutModelSum = {
      }
      this.detailOutModelSumNo = {

      },
      this.detailOutModelSumIs1 = {

      }
      this.detailOutModelSumIs1No = {

      }
      // 求和
      this.goodsPriceOutListCostDis.forEach(it => {
        this.detailOutModelSum[it] = 0
        this.detailOutModelSumNo[it] = 0
        this.detailOutModelSumIs1[it] = 0
        this.detailOutModelSumIs1No[it] = 0
        const mits = list.filter(item => item.costName == it)
        mits.forEach(item => {
          if (item.invoiceType != 10) {
            // this.detailOutModelSum[it] = this.detailOutModelSum[it] + item.price

            // this.detailOutModelSumNo[it] = this.detailOutModelSumNo[it] + item.priceNo

            this.detailOutModelSumIs1[it] = currency(this.detailOutModelSumIs1[it]).add(currency(item.price)).value
            this.detailOutModelSumIs1No[it] = currency(this.detailOutModelSumIs1No[it]).add(currency(item.priceNo)).value
          }
          this.detailOutModelSum[it] = currency(this.detailOutModelSum[it]).add(currency(item.priceNo)).value
          this.detailOutModelSumNo[it] = currency(this.detailOutModelSumNo[it]).add(currency(item.priceNo)).value
        })
      })
      // if (list && list.length) {
      //   let costid = ''
      //   let sum = 0;
      //   let sumNo = 0;
      //   let sumIs1 = 0;
      //   let sumIs1No = 0;
      //   let cTmp =1;
      //   for (let i = 0; i < list.length;) {
      //     let item = list[i]
      //     if (!costid) {
      //       costid = item.costName
      //       cTmp=1
      //       if (item.invoiceType != 10) {
      //         // sumIs1 = sumIs1 + item.price
      //         sumIs1 = currency(sumIs1).add(currency(item.price)).value
      //         // sumIs1No = sumIs1No + item.priceNo
      //         sumIs1No = currency(sumIs1No).add(currency(item.priceNo)).value
      //         sum = currency(sum).add(currency(item.price)).value
      //       }

      //       sumNo = currency(sumNo).add(currency(item.priceNo)).value
      //       console.log('costid', costid, 'sum', sum)
      //       console.log('costid',costid,'sumNo',sumNo)
      //       console.log('costid',costid,'sumIs1',sumIs1)
      //       console.log('costid',costid,'sumIs1No',sumIs1No)
      //       // 最后一条 isAdd=true
      //       if(i==list.length-1){
      //         this.detailOutModelSum[costid] = sum
      //         this.detailOutModelSumNo[costid] = sumNo
      //         this.detailOutModelSumIs1[costid] = sumIs1
      //         this.detailOutModelSumIs1No[costid] = sumIs1No
      //         break;
      //       }
      //     }
      //     for (let j = i + cTmp; j < list.length; j++){

      //       if (costid == list[j].costName) {
      //         console.log('j-73',list[j].costName)
      //         if (list[j].invoiceType != 10) {
      //           // sumIs1 = sumIs1 + list[j].price
      //           sumIs1 = currency(sumIs1).add(currency(list[j].price)).value
      //           // sumIs1No = sumIs1No + list[j].priceNo
      //           sumIs1No = currency(sumIs1No).add(currency(list[j].priceNo)).value
      //           sum = currency(sum).add(currency(list[j].price)).value
      //         }

      //           sumNo = currency(sumNo).add(currency(list[j].priceNo)).value
      //           console.log('1488---')
      //           console.log('costid', costid, 'sum', sum)
      //           console.log('costid',costid,'sumNo',sumNo)
      //           console.log('costid',costid,'sumIs1',sumIs1)
      //           console.log('costid',costid,'sumIs1No',sumIs1No)
      //           cTmp++;
      //           if(j==list.length-1) {
      //               // 最后一条
      //               break;
      //           }
      //         }else{
      //             break;
      //         }
      //     }
      //     i = i + cTmp

      //     this.detailOutModelSum[costid] = sum
      //     this.detailOutModelSumNo[costid] = sumNo
      //     this.detailOutModelSumIs1[costid] = sumIs1
      //     this.detailOutModelSumIs1No[costid] = sumIs1No
      //     costid = ''
      //     sum = 0;
      //     sumNo = 0;
      //     sumIs1 = 0;
      //     sumIs1No = 0;
      //   }
      //   console.log('detailOutModelSum，1501', this.detailOutModelSum)
      //   console.log('detailOutModelSumNo，1502', this.detailOutModelSumNo)
      //   console.log('1503', this.detailOutModelSumIs1)
      //   console.log('1504', this.detailOutModelSumIs1No)
      // }
    },
    sumModeByDetailIn(list) {
      // getSumByNamePro('goodsPriceInList','costName',item,isTaxIncluded?'price':'priceNo',goodsPriceInList)
      this.detailInModelSum = {
      }
      this.detailInModelSumNo = {

      },
      this.detailInModelSumIs1 = {

      }
      this.detailInModelSumIs1No = {

      }
      this.goodsPriceInListCostDis.forEach(it => {
        this.detailInModelSum[it] = 0
        this.detailInModelSumNo[it] = 0
        this.detailInModelSumIs1[it] = 0
        this.detailInModelSumIs1No[it] = 0
        const tmpList = list.filter(item => {
          return item.costName == it
        })
        tmpList.forEach(item => {
          if (item.invoiceType != 10) {
            this.detailInModelSumIs1[it] = currency(this.detailInModelSumIs1[it]).add(currency(item.price)).value
            this.detailInModelSumIs1No[it] = currency(this.detailInModelSumIs1No[it]).add(currency(item.priceNo)).value

          }
          this.detailInModelSum[it] = currency(this.detailInModelSum[it]).add(currency(item.priceNo)).value
          this.detailInModelSumNo[it] = currency(this.detailInModelSumNo[it]).add(currency(item.priceNo)).value
        })

      })

      // if (list && list.length) {
      //   let costid = ''
      //   let sum = 0;
      //   let sumNo = 0;
      //   let sumIs1 = 0;
      //   let sumIs1No = 0;
      //   let cTmp =1;
      //   for (let i = 0; i < list.length;) {
      //     let item = list[i]
      //     if (!costid) {
      //       costid = item.costName
      //       cTmp=1
      //       if (item.invoiceType != 10) {
      //         // sumIs1 = sumIs1 + item.price
      //         sumIs1 = currency(sumIs1).add(currency(item.price)).value
      //         // sumIs1No = sumIs1No + item.priceNo
      //         sumIs1No = currency(sumIs1No).add(currency(item.priceNo)).value
      //       }
      //       sum = currency(sum).add(currency(item.price)).value
      //       sumNo = currency(sumNo).add(currency(item.priceNo)).value
      //       // 最后一条 isAdd=true
      //       if(i==list.length-1){
      //         this.detailInModelSum[costid] = sum
      //         this.detailInModelSumNo[costid] = sumNo
      //         this.detailInModelSumIs1[costid] = sumIs1
      //         this.detailInModelSumIs1No[costid] = sumIs1No
      //         break;
      //       }
      //     }
      //     for (let j = i + cTmp; j < list.length; j++){
      //       if (costid == list[j].costName) {
      //         if (list[j].invoiceType != 10) {
      //           // sumIs1 = sumIs1 + list[j].price
      //           sumIs1 = currency(sumIs1).add(currency(list[j].price)).value
      //           // sumIs1No = sumIs1No + list[j].priceNo
      //           sumIs1No = currency(sumIs1No).add(currency(list[j].priceNo)).value
      //         }
      //           sum = currency(sum).add(currency(list[j].price)).value
      //           sumNo = currency(sumNo).add(currency(list[j].priceNo)).value
      //           cTmp++;
      //           if(j==list.length-1) {
      //               // 最后一条
      //               break;
      //           }
      //         }else{
      //             break;
      //         }
      //     }
      //     i = i + cTmp

      //     this.detailInModelSum[costid] = sum
      //     this.detailInModelSumNo[costid] = sumNo
      //     this.detailInModelSumIs1[costid] = sumIs1
      //     this.detailInModelSumIs1No[costid] = sumIs1No
      //     costid = ''
      //     sum = 0;
      //     sumNo = 0;
      //     sumIs1 = 0;
      //     sumIs1No = 0;
      //   }
      // }
    },
    listDataFor() {
      this.datalist.forEach(item =>{
              if(item.lighter == 0){
                item.shipPay1 = (item.ticketPrice == null || item.ticketPrice == '' || item.ticketPrice == undefined )? (item.ticketPrice1 == null || item.ticketPrice1 == '' || item.ticketPrice1 == undefined ) ? item.shipPay : item.ticketPrice1 : item.ticketPrice
              } else {
                item.shipPay = "--"
                item.shipPay1 = "--"
                item.payBill = null
                item.shipcompany = "--"
                item.ticketPrice = ""
                item.ticketPriceNo = ""
                item.ticketPrice1 = ""
                item.ticketPrice1No = ""
              }
              if(item.lighter == 0){
                // item.sumYunCost = (item.freightNo?item.freightNo:0) * item.tonnage
                item.sumYunCost = currency((item.freightNo?item.freightNo:0) * item.tonnage).value
                // item.sumAgentCost = (item.dailiNo?item.dailiNo:0) * item.tonnage
                item.sumAgentCost = currency((item.dailiNo?item.dailiNo:0) * item.tonnage).value
                // item.sumPric =  item.shipPay * item.tonnage
                item.sumPric = currency(item.shipPay * item.tonnage).value
                // zong += item.sumPric
                zong = currency(zong).add(item.sumPric).value
              } else {
                // item.sumYunCost = (item.freightNo?item.freightNo:0) * item.lighterTonnage
                item.sumYunCost = currency((item.freightNo?item.freightNo:0) * item.lighterTonnage).value
                // item.sumAgentCost = (item.dailiNo?item.dailiNo:0) * item.lighterTonnage
                item.sumAgentCost = currency((item.dailiNo?item.dailiNo:0) * item.lighterTonnage).value
              }
              // yun += item.sumYunCost
              yun = currency(yun).add(item.sumYunCost).value
              // dai += item.sumAgentCost
              dai = currency(dai).add(item.sumAgentCost).value
              if(this.stcode == 10 && item.customerGoodsCostDetail && item.customerGoodsCostDetail.length>0){
                // var newDetailList = item.customerGoodsCostDetail.filter(str=>str.indexOf("不开") ==-1)
                var newDetailList = []
                item.customerGoodsCostDetail.forEach(str => {
                  // console.log('customerGoodsCostDetail-str',str)
                  if(str.indexOf("不开") ==-1) {
                    newDetailList.push(str)
                  } else {
                    if (str == '--') {
                      newDetailList.push(str)
                      return
                    }
                    var arr = str.split("、")
                    let str3 = ""
                    arr.forEach(str1=>{
                      if(str1.indexOf("不开") ==-1) {
                        // newDetailList.push(str1)
                        str3 += str1 + "、"
                      }
                    })
                    if(str3) {
                      newDetailList.push(str3.substring(0,str3.length-1))
                    } else {
                      newDetailList.push("--")
                    }
                  }

                })
                item.customerGoodsCostDetail = newDetailList
              }
            })
    },
      onQuery() {
        this.isTaxIncluded = !this.isTaxIncluded
        getStowageDetil(this.shipLineId,this.isTaxIncluded,this.deptCode).then((res) => {
          if (res != undefined) {
            this.costNames = res.costNames;
            this.allcostName = res.costNames;
            this.zhanshilist = res.list;
            this.datalist = res.list;
            this.detailin = res.detailin;
            this.sumModeByDetailIn(this.detailin)
            this.detailout = res.detailout;
            this.sumModeByDetailOut(this.detailout)
            var yun = 0
            var dai = 0
            var zong = 0
            res.list.forEach(item =>{
              let teshu_shipPay
              if(item.lighter == 0){
                item.shipPay1 = (item.ticketPrice == null || item.ticketPrice == '' || item.ticketPrice == undefined )? (item.ticketPrice1 == null || item.ticketPrice1 == '' || item.ticketPrice1 == undefined ) ? item.shipPay : item.ticketPrice1 : item.ticketPrice
              } else {
                if(item.id == '6b6f7cb62f53473e8dffd41fb6a21623'){
                  item.shipPay1 = item.shipPay;
                  teshu_shipPay = item.shipPay;
                }else {
                  item.shipPay = "--"
                  item.shipPay1 = "--"
                }
                item.payBill = null
                item.shipcompany = "--"
                item.ticketPrice = ""
                item.ticketPriceNo = ""
                item.ticketPrice1 = ""
                item.ticketPrice1No = ""
              }
              if(item.lighter == 0){
                // item.sumYunCost = (item.freightNo?item.freightNo:0) * item.tonnage
                item.sumYunCost = currency((item.freightNo?item.freightNo:0) * item.tonnage).value
                // item.sumAgentCost = (item.dailiNo?item.dailiNo:0) * item.tonnage
                item.sumAgentCost = currency((item.dailiNo?item.dailiNo:0) * item.tonnage).value
                // item.sumPric =  item.shipPay * item.tonnage
                item.sumPric = currency(item.shipPay * item.tonnage).value
                // zong += item.sumPric
                zong = currency(zong).add(item.sumPric).value
              } else {
                // item.sumYunCost = (item.freightNo?item.freightNo:0) * item.lighterTonnage
                item.sumYunCost = currency((item.freightNo?item.freightNo:0) * item.lighterTonnage).value
                // item.sumAgentCost = (item.dailiNo?item.dailiNo:0) * item.lighterTonnage
                item.sumAgentCost = currency((item.dailiNo?item.dailiNo:0) * item.lighterTonnage).value
                if(item.id == '6b6f7cb62f53473e8dffd41fb6a21623'){
                  item.sumPric = currency(teshu_shipPay * item.lighterTonnage).value
                }
              }
              // if(item.id == '6b6f7cb62f53473e8dffd41fb6a21623') {
              //   item.inTaxSingleIncome = 0;
              // }
              // yun += item.sumYunCost
              yun = currency(yun).add(item.sumYunCost).value
              // dai += item.sumAgentCost
              dai = currency(dai).add(item.sumAgentCost).value
              if(this.stcode == 10 && item.customerGoodsCostDetail && item.customerGoodsCostDetail.length>0){
                // var newDetailList = item.customerGoodsCostDetail.filter(str=>str.indexOf("不开") ==-1)
                var newDetailList = []
                item.customerGoodsCostDetail.forEach(str => {
                  // console.log('customerGoodsCostDetail-str',str)
                  if(str.indexOf("不开") ==-1) {
                    newDetailList.push(str)
                  } else {
                    if (str == '--') {
                      newDetailList.push(str)
                      return
                    }
                    var arr = str.split("、")
                    let str3 = ""
                    arr.forEach(str1=>{
                      if(str1.indexOf("不开") ==-1) {
                        // newDetailList.push(str1)
                        str3 += str1 + "、"
                      }
                    })
                    if(str3) {
                      newDetailList.push(str3.substring(0,str3.length-1))
                    } else {
                      newDetailList.push("--")
                    }
                  }

                })
                item.customerGoodsCostDetail = newDetailList
              }
            })
            var zzz = res.list[res.list.length - 1]
            zzz.sumYunCost = yun
            zzz.sumAgentCost = dai
            zzz.sumPric = zong
            // console.log(res.list)
            this.shipName = res.shipName;

            this.shipLine = res.shipLine;
            var shipinnum = 0
            var shipinnumno = 0
            var shipintax = ""
            var shipinsu= ""
            this.shipcostlist =res.shipcostlist
            this.allshipcostlist =res.shipcostlist
            this.shipcostoutlist = res.shipcostlist.filter(item=>item.isIncome === 0)
            this.shipcostinlist = res.shipcostlist.filter(item=>item.isIncome === 1)
            this.profits = res.profits;
            this.chenggongprofits = res.chenggongprofits;
            this.heshengprofits = res.heshengprofits;
            this.queren = res.shipLine.queren;
            res.shippay.forEach(res =>{
              if (res.tax){
                this.shippaylist.push(res)
              }
            });
            this.preStatus = res.shipLine.preStatus;
            this.yun = res.yunfeishui
            this.allhuowulist = res.costlist;
            this.zhanshi = !res.queren;
            var that = this
            setTimeout(() => {
              that.isTaxIncluded = !that.isTaxIncluded
              setTimeout(() => {
                // that.mergeCells = [{ row: 1, col: 1, rowspan: 2, colspan: 2 }];
                that.mergeCells = calcMergeOfColumns(this.$refs.xTable, this.datalist, ['onename', 'customerName', 'goodsType']);
              }, 1);
            }, 100);
          }
        });
      },
    },
  };
</script>


<!--<style scoped src="../assets/css/mytable-scrollbar.css" ></style>-->
<style scoped>

  /* --------------------------------------------------------------- */
  .showTag{
    height: 120px;
  }
  .el-tag {
    cursor: pointer;
  }

  .choose-contract-type >>> .el-button {
    width: 120px;
  }

  .choose-contract-type >>> .el-form-item {
    margin-left: 1px;
    margin-right: 1px;
  }

  .contracrt-scrollbar {
    height: 100%;
    margin-bottom: 20px;
  }

  .show-contracts-image {
    display: flex;
    flex-wrap: wrap;
    width: 765px;
    height: 55vh;
    margin: 0 auto;
  }

  .show-contract-image {
    width: 220px;
    height: 340px;
    margin: 5px 15px;
    border-radius: 4px;
  }

  .show-contract-image >>> .contract-img {
    width: 210px;
    height: 297px;
    margin-top: 5px;
  }

  .show-contract-image >>> .yixuanze-img {
    width: 20px;
    height: 20px;
    border-radius: 60%;
    position: absolute;
    top: -5px;
    right: -5px;
    display: none;
  }

  .show-contract-image >>> p {
    color: #55575c;
    margin: 7px 0;
  }

  .chakan-img {
    display: none;
    width: 40px;
    height: 40px;
    position: absolute;
    top: 40%;
    right: 40%;
  }

  .selected {
    background-color: #5992f5;
  }

  .selected >>> .yixuanze-img {
    display: block;
  }

  .selected >>> p {
    color: #ffffff;
  }

  .contract-iframe >>> p {
    position: absolute;
    left: 10px;
  }

  .contract-iframe >>> .el-button {
    position: absolute;
    right: 70px;
    margin-top: 10px;
  }

  /* ------------------------------------------------------------------------------------     */

  .hangci-biaoti-yupeizai {
    color: #409eff;
  }

  .hangci-biaoti {
    text-align: left;
    margin: 6px 0px;
  }

  .hangci-biaoti11 {
  }

  .hangci-biaoti12 {
    margin-left: 30px;
  }

  .hangci-biaoti1 {
    font-size: 0.8rem;
  }

  .hangci-biaoti2 {
    font-size: 0.8rem;
    margin-left: 20px;
  }

  .query-form {
    text-align: left;
    padding-top: 10px;
  }

  .sp_add_left {
    width: 45%;
    margin-right: 5%;
  }

  .sp_add_right {
    width: 45%;
  }

  .el-table-info >>> .cell {
    text-align: center;
  }

  .el-table-info >>> th {
    background: #f5f7fa;
  }

  .mytable td {
    padding-top: 0.5rem;
    padding-bottom: 0.5rem;
  }

  .mytable td .cell {
    padding-left: 0.1rem;
    padding-right: 0.1rem;
  }

  .el-table-info >>> .warning-cell {
    color: red;
  }

  .el-table-info >>> .success-cell {
    color: #6dd400;
  }

  .dialog-info {
    text-align: left;
    margin-top: -40px;
    margin-bottom: 20px;
    font-weight: 500;
    font-size: 14px;
    width: 100%;
    display: inline-block;
  }

  .info-margin {
    margin-left: 5%;
    margin-right: 5%;
  }

  .info-title {
    display: inline-block;
    width: 5%;
    text-align: left;
  }

  .info-text {
    display: inline-block;
    width: 10%;
    text-align: left;
  }

  .myStep >>> .el-step__main {
    margin-top: 10px;
  }
  .hangci-biaoti2first {
    font-size: 0.8rem;
  }
  .hsbhs{
    margin-left: 20px;
  }
  .global-table-box>table {
    width: 100%;
    table-layout: fixed;
    border-spacing: 0;
    border-collapse: collapse;
    border: 1px solid #F5F6FA;
  }

  .global-table thead td,
  .global-table th {
    line-height: 20px;
    font-size: 13px;
    padding: 15px 0;
    background-color: #F5F6FA;
    text-align: left;
    font-weight: bold;
    font-style: normal;
    margin: 0;
    color: #757b84;
    white-space: nowrap;
    border-bottom: 1px solid #EBEEF5;
  }
  .global-table tbody td {
    padding-top: 10px;
    padding-bottom: 10px;
    background-color: #fff;
    color: #81858e;
    border-bottom: 1px solid #F5F4F5;
    font-size: 13px;
    height:30px;
    white-space: normal;
    word-break: break-all;
  }
  .global-table th {
    padding-left: 15px;
    padding-right: 5px;
  }

  .global-table tbody td.global-strong-td {
    font-weight: bold;
    color: #333;
    border-right: 1px solid #F5F4F5;
    background-color: #FAFBFD;
    padding: 0 20px;
  }

  .global-border-td {
    border-right: 1px solid #F5F4F5;
  }

  .global-table tbody td.globla-orange-td {
    color: #FF8446;
  }

  .global-table tr {
    cursor: default;
  }

  .global-table tr.selected td {
    background-color: #e0f0ff;
  }

  .global-table tr:nth-child(2n):not(.global-total-tr) td {
    background-color: #fbfbfb;
  }

  .global-table tr:hover td {
    background-color: #F5F8FF !important;
  }

  .global-table~.ui-loading {
    height: 300px;
  }

  .global-table .el-button--mini {
    font-size: 13px;
  }

  .global-table a {
    padding-right: 10px;
    font-size: 13px;
  }

  .global-table tbody tr.global-total-tr,
  .global-table tbody .global-total-tr td {
    font-size: 14px;
    font-weight: bold;
    color: #65696d;
  }

  .global-table tbody tr.global-subtotal-tr {
    font-weight: 500;
  }
  .global-table tfoot td {
    padding: 10px 0;
    background-color: #F5F6FA;
    color: #81858e;
    border-bottom: 1px solid #EBEEF5;
    font-size: 13px;
    height:30px;
    white-space: normal;
    word-break: break-all;
  }
  .el-input{
    width:100%;
  }

  .el-table-info >>> .vxe-cell{
    text-align: center;
  }

</style>
