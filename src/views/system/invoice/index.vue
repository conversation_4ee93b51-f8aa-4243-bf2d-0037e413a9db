<template>
  <div class="app-container">
    <!-- 筛选条件 -->
    <div style="text-align: left;">
      <el-input style="width: unset" v-model="queryParams.sellerName" size="medium" placeholder="请输入开票公司"></el-input>
      <el-input style="width: unset" v-model="queryParams.shipName" size="medium" placeholder="请输入船舶名称"></el-input>
      <el-input style="width: unset" v-model="queryParams.invoiceNo" size="medium" placeholder="请输入发票号"></el-input>
      <el-input style="margin: 0 10px;width: unset;" v-model="queryParams.costTypeName" size="medium" placeholder="请输入费用类型"></el-input>
      <el-date-picker
        v-model="queryParams.invoiceDate"
        type="date"
        size="medium"
        value-format="yyyy年MM月dd日"
        placeholder="开票日期">
      </el-date-picker>
        <el-radio-group v-model="queryParams.type" size="small">
          <el-radio-button label="10">网联</el-radio-button>
          <el-radio-button label="20">汽运</el-radio-button>
        </el-radio-group>
    </div>

    <!-- 工具栏 -->
    <vxe-toolbar ref="xToolbar1" custom refresh>
      <template #buttons>
        <div style="text-align: left;">
          <el-tooltip class="item" effect="dark" content="录入挂帐月" placement="left">
            <vxe-button status="primary" @click="updateBatchBilMonth">{{ toRegister }}</vxe-button>
          </el-tooltip>
          <vxe-button status="primary" content="搜索" @click="getList"></vxe-button>
          <vxe-button status="info" content="重置" @click="resetQuery"></vxe-button>
          <vxe-button @click="openExportEvent">导出</vxe-button>
        </div>
      </template>
      <template #tools>
<!--        <vxe-button @click="openShowApplyTicket" status="primary" :disabled="registerDisable">{{toRegister}}</vxe-button>-->
<!--        <el-tooltip class="item" effect="dark" content="请选择同一个开票公司，再进行进票登记" placement="top">-->
<!--          <i class="vxe-icon&#45;&#45;question" style="margin: 0 10px;"></i>-->
<!--        </el-tooltip>-->
      </template>
    </vxe-toolbar>

    <!-- 表格内容 -->
    <vxe-table
      border
      max-height="66%"
      ref="xTable1"
      show-footer
      @checkbox-all="selectChangeEvent"
      @checkbox-change="selectChangeEvent"
      :loading="tableLoading"
      :export-config="{}"
      :footer-method="footerMethod"
      :data="list">
      <vxe-column type="checkbox" width="60"></vxe-column>
      <vxe-column type="seq" width="60"></vxe-column>
      <vxe-column field="invoiceDate" title="开票日期"></vxe-column>
      <vxe-column field="sellerName" title="开票公司"></vxe-column>
      <vxe-column field="shipName" title="船舶名称"></vxe-column>
      <vxe-column field="invoiceNo" title="发票号"></vxe-column>
      <vxe-column field="invoiceCode" title="发票代码"></vxe-column>
      <vxe-column field="costTypeName" title="费用类型"></vxe-column>
      <vxe-column field="invoicePriceTaxSum" title="发票含税金额"></vxe-column>
      <vxe-column field="invoicePriceTax" title="进项税"></vxe-column>
      <vxe-column field="invoicePrice" title="不含税金额"></vxe-column>
      <vxe-column field="invoiceTax" title="税率"></vxe-column>
      <vxe-column field="serviceName" title="服务名称"></vxe-column>
      <vxe-column field="invoiceNo" title="标识">
        <template #default="{ row }">
          <div>
            <div style="color: #6AC144" v-if="row.type == 20">汽运公司</div>
            <div style="color: #5a9cf8" v-else>成功网联</div>
          </div>
        </template>
      </vxe-column>
      <vxe-column field="invoiceRemark" title="备注" show-overflow></vxe-column>
      <vxe-column title="操作">
        <template #default="{ row }">
          <el-tooltip class="item" effect="dark" content="点击可查看发票文件" placement="left">
            <vxe-button :disabled="!row.originFile" status="warning" type="text" content="发票" @click="showInvoicePictureHandle(row)"></vxe-button>
          </el-tooltip>
          <el-tooltip class="item" effect="dark" content="录入挂帐月" placement="left">
            <vxe-button status="warning" type="text" content="挂帐月" @click="updateBilMonth(row)"></vxe-button>
          </el-tooltip>
        </template>
      </vxe-column>
    </vxe-table>

    <!--分页-->
    <div style="text-align: right;">
      <pagination
        v-show="total>0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        :page-sizes="[10, 100, 200, 300, 500]"
        layout="total,sizes,prev, pager, next"
        @pagination="getList"
      />
    </div>

    <!-- 查看发票 -->
    <el-dialog
      title="查看发票"
      :visible.sync="showInvoicePicture"
      width="60%"
      :close-on-click-modal=false
    >
      <el-image
        class="preview-image"
        :src="invoicePictureUrl"
        :preview-src-list="[invoicePictureUrl]">
      </el-image>
    </el-dialog>
    <vxe-modal v-model="showBillMonth" title="录入挂帐月">
      <template #default>
        <div v-if="checkedItem" style="margin-bottom: 20px;">
          <div>发票号：{{checkedItem.invoiceNo}}</div>
          <div>发票日期：{{checkedItem.invoiceDate}}</div>
          <div>开票公司：{{checkedItem.sellerName}}</div>
          <div>船舶名称：{{checkedItem.shipName}}</div>
          <div>发票含税金额：{{checkedItem.invoicePriceTaxSum}}</div>
          <el-image
            v-if="checkedItem.originFile"
            style="width: 100px;height: 100px;margin-top: 10px;"
            @click="showInvoicePictureHandle(checkedItem)"
            :src="checkedItem.originFile">
          </el-image>
        </div>

        <el-date-picker
          v-model="form.recordAccountMonth"
          type="month"
          value-format="yyyy/M"
          format="yyyy/M"
          placeholder="选择月">
        </el-date-picker>
        <el-button @click="submitForm" type="primary" size="small" :loading="formLoading">{{formLoading?'loading...':'提交'}}</el-button>
      </template>
    </vxe-modal>
  </div>
</template>

<script>
import 'vxe-table/lib/style.css'
import VXETablePluginExportXLSX from 'vxe-table-plugin-export-xlsx';
import VXETable from 'vxe-table'
VXETable.use(VXETablePluginExportXLSX)
import Pagination from "@/components/Pagination";
import {getList, updateBilMonth} from "../../../api/system/invoice";
import currency from "currency.js";

export default {
  name: "Invoice",
  components: {Pagination},
  data() {
    return {
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        status: 2, // 2 = 待入账
        sellerName: null,
        shipName: null,
        invoiceDate: null,
        invoiceNo: null,
        costTypeName: null,
        type: 10
      },
      list: [],
      total: 0,
      tableLoading: false,

      checkedCount: 0,
      registerDisable: true,
      invoicePictureUrl: null,
      showInvoicePicture: false,

      showBillMonth: false,
      form: {
        recordAccountMonth: null,
        status: 3
      },
      formLoading: false,
      checkedItem: null
    }
  },
  computed: {
    toRegister() {
      let r = `挂帐月`
      if (this.checkedCount > 0) {
        r += `(${this.checkedCount})`
      }
      return r
    },
    checkedList() {
      try {
        return this.$refs.xTable1.getCheckboxRecords()
      } catch (err) {
        // nothing
      }
      return []
    },
  },
  created () {
    this.$nextTick(() => {
      // 手动将表格和工具栏进行关联
      this.$refs.xTable1.connect(this.$refs.xToolbar1)
    })
  },
  mounted() {
    this.getList()
  },
  methods: {
    getList() {
      this.tableLoading = true
      this.queryParams['type'] = this.queryParams['type']+''
      getList(this.queryParams).then(res => {
        this.list = res.data.rows
        this.total = res.data.total
      }).finally(() => {
        this.tableLoading = false
      })
    },
    openExportEvent () {
      this.$refs.xTable1.openExport()
    },
    resetQuery() {
      this.queryParams = this.$options.data().queryParams
      this.getList()
    },
    selectChangeEvent ({ checked }) {
      const records = this.$refs.xTable1.getCheckboxRecords()
      this.checkedCount = records.length
      this.registerDisable = new Set(records.map(item => item.sellerName)).size !== 1
    },
    showInvoicePictureHandle(row) {
      this.invoicePictureUrl = row.originFile
      this.showInvoicePicture = true
    },
    submitForm() {
      this.formLoading = true
      updateBilMonth(this.form).then(res => {
        this.showBillMonth = false
        this.getList()
      }).catch(err => {
        // nothing
      }).finally(() => {
        this.formLoading = false
      })
    },
    updateBatchBilMonth() {
      this.form.id = null
      this.form.ids = this.checkedList.map(item=>item.id)
      this.showBillMonth = true
      // this.updateBilMonthDialog({
      //   ids: this.checkedList.map(item=>item.id)
      // })
    },
    updateBilMonth(row) {
      this.form.ids = null
      this.form.id = row.id
      this.showBillMonth = true
      this.checkedItem = row
      // this.updateBilMonthDialog({
      //   id: row.id
      // })
    },
    updateBilMonthDialog(idObj) {
      this.$prompt('请输入挂帐月(1～12)', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        closeOnClickModal: false,
        closeOnPressEscape: false,
        inputPattern: /^(?:1[0-2]|[1-9])$/,
        inputErrorMessage: '请输入整数(1～12)',
        beforeClose: (action, instance, done) => {
          if (action === 'confirm') {
            instance.confirmButtonLoading = true;
            instance.confirmButtonText = 'loading...';
            updateBilMonth(Object.assign({
              recordAccountMonth: Number(instance.inputValue),
              status: 3
            },idObj)).then(res => {
              done()
            }).catch(err => {
              // nothing
            }).finally(() => {
              instance.confirmButtonLoading = false;
              instance.confirmButtonText = '确定';
            })
          } else {
            done();
          }
        }
      }).then(({ value }) => {
        // nothing
      }).catch(() => {
        // nothing
      });
    },
    sumNum (list, field) {
      let count = 0
      list.forEach(item => {
        count += Number(item[field])
      })
      return count.toFixed(2)
    },
    footerMethod ({ columns, data }) {
      return [
        columns.map((column, columnIndex) => {
          if (columnIndex === 0) {
            return '合计'
          }
          if (['invoicePriceTaxSum', 'invoicePriceTax','invoicePrice'].includes(column.property)) {
            let m = this.sumNum(data, column.property)
            let m_format = currency(m, { symbol: "" }).format()
            return m_format
          }
          return null
        })
      ]
    },
  }
}
</script>

<style scoped>

</style>
