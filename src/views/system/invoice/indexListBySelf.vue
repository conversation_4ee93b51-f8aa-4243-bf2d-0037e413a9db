<template>
  <div class="app-container">
    <AccModel @updateAccModel="updateAccModel"></AccModel>

  <!-- 筛选条件 -->
  <div style="text-align: left;position: absolute; right: 15px;z-index: 20;">
            <el-input style="width: 120px;" v-model="queryParams.sellerName" @change="getList" size="medium" placeholder="开票公司" clearable></el-input>
            <!-- <el-input style="width: unset" v-model="queryParams.shipName" size="medium" placeholder="请输入船舶名称"></el-input> -->
            <el-input style="width: 120px;" v-model="queryParams.invoiceNo" @change="getList" size="medium" placeholder="发票号" clearable></el-input>
            <!-- <el-input style="margin: 0 10px;width: unset;" v-model="queryParams.costTypeName" @change="getList" size="medium" placeholder="请输入费用类型" clearable></el-input> -->
            <el-date-picker
              v-model="queryParams.invoiceDate"
              @change="getList"
              type="date"
              size="medium"
              style="width: 120px;"
              value-format="yyyy年MM月dd日"
              placeholder="开票日期">
            </el-date-picker>
            <el-date-picker
              v-model="queryParams.billMonth"
              @change="getList"
              style="width: 120px;"
              value-format="yyyy/M"
              type="month"
              placeholder="挂账月"
            >
            </el-date-picker>
              <el-radio-group v-model="queryParams.status" @change="getList" size="small">
                <el-radio-button label="" >全部</el-radio-button>
                <el-radio-button label="0" >未挂帐</el-radio-button>
                <el-radio-button label="3" >已挂帐</el-radio-button>
              </el-radio-group>

              <el-radio-group v-model="queryParams.isItRepeated" @change="getList" size="small">
                <el-radio-button label="null" >全部</el-radio-button>
                <el-radio-button label="0" >未重复</el-radio-button>
                <el-radio-button label="1" >重复</el-radio-button>
              </el-radio-group>

              <!-- <el-radio-group v-model="queryParams.outInputInvoice" @change="getList" size="small">
                <el-radio-button label="" >全部</el-radio-button>
                <el-radio-button label="in" >进项</el-radio-button>
                <el-radio-button label="out" >销项</el-radio-button>
              </el-radio-group> -->
              <!-- <el-radio-group v-model="queryParams.type" size="small">
                <el-radio-button label="10">网联</el-radio-button>
                <el-radio-button label="20">汽运</el-radio-button>
              </el-radio-group> -->
          </div>

    <el-tabs v-model="queryParams.outInputInvoice" type="card" @tab-click="getList">
      <el-tab-pane v-for="mItem in modelList" :label="mItem.value" :name="mItem.code">

        <!-- 工具栏 -->
        <vxe-toolbar ref="xToolbar1" custom refresh>
          <template #buttons>
            <div style="text-align: left;">
              <el-tooltip  effect="dark" content="录入挂帐月" placement="left">
                <vxe-button status="primary" @click="updateBatchBilMonth">{{ toRegister }}</vxe-button>
              </el-tooltip>
              <vxe-button @click="delInvoices" :loading="delLoading" status="danger">删除</vxe-button>
              <!-- <vxe-button status="primary" content="搜索" @click="getList"></vxe-button>
              <vxe-button status="info" content="重置" @click="resetQuery"></vxe-button> -->
              <vxe-button @click="openExportEvent">导出</vxe-button>
              <vxe-button @click="openUploadInvoce(1)" v-if="queryParams.outInputInvoice=='out'">导入excel</vxe-button>
              <vxe-button @click="openUploadInvoce(2)" v-if="queryParams.outInputInvoice=='in'">导入excel</vxe-button>
              <vxe-button @click="openUploadInvoce(3)" v-if="queryParams.outInputInvoice=='in_receipt'">导入excel</vxe-button>
              <!-- v-if="queryParams.outInputInvoice=='out'" -->
               <el-upload
                 style="display: inline-block;margin-left: 10px;"
                 :show-file-list="false"
                :action="upload"
                :on-preview="handlePreview"
                :on-remove="handleRemove"
                :before-remove="beforeRemove"
                :on-success="savefileList"
                :before-upload="beforeUpload"
                :on-error="handleError"
                multiple
                :on-exceed="handleExceed"
                :file-list="fileList"
              >
                <vxe-button  type="primary">上传</vxe-button>
              </el-upload>
            </div>
          </template>
          <template #tools>
    <!--        <vxe-button @click="openShowApplyTicket" status="primary" :disabled="registerDisable">{{toRegister}}</vxe-button>-->
    <!--        <el-tooltip class="item" effect="dark" content="请选择同一个开票公司，再进行进票登记" placement="top">-->
    <!--          <i class="vxe-icon&#45;&#45;question" style="margin: 0 10px;"></i>-->
    <!--        </el-tooltip>-->
          </template>
        </vxe-toolbar>
      </el-tab-pane>
    </el-tabs>

    <!-- 表格内容 -->
    <vxe-table
      border
      max-height="66%"
      ref="xTable1"
      show-footer
      @checkbox-all="selectChangeEvent"
      @checkbox-change="selectChangeEvent"
      :loading="tableLoading"
      :export-config="{}"
      :footer-method="footerMethod"
      :data="list">
      <vxe-column type="checkbox" width="40"></vxe-column>
      <vxe-column type="seq" width="40"></vxe-column>
      <vxe-column field="invoiceDate" width="108" title="开票日期" ></vxe-column>
      <vxe-column field="sellerName" show-overflow title="开票公司"></vxe-column>
      <vxe-column field="buyerName"  show-overflow title="收票公司"></vxe-column>
      <vxe-column field="invoiceNo"  title="发票号"></vxe-column>
      <vxe-column field="invoiceType" width="80" show-overflow title="发票类型">
        <template #default="{ row }">
          <div>
            <div  v-if="row.invoiceType">{{ invoiceTypeObj[row.invoiceType] || row.invoiceType }}</div>
          </div>
        </template>
      </vxe-column>
      <vxe-column field="invoicePriceTaxSum" width="90" title="价税合计" ></vxe-column>
      <vxe-column field="invoicePriceTax" width="60" title="税额" ></vxe-column>
      <!-- <vxe-column field="invoicePrice" title="税前金额" ></vxe-column> -->
      <vxe-column field="invoiceTax" width="60" title="税率" ></vxe-column>
      <!-- <vxe-column field="outInputInvoice" width="60" title="进/销">
        <template #default="{ row }">
          <div>
            <div v-if="row.outInputInvoice == 'in'">进项</div>
            <div v-if="row.outInputInvoice == 'out'">销项</div>
          </div>
        </template>
      </vxe-column> -->

      <!-- <vxe-column field="invoiceNo" title="标识">
        <template #default="{ row }">
          <div>
            <div style="color: #6AC144" v-if="row.type == 20">汽运公司</div>
            <div style="color: #5a9cf8" v-else>成功网联</div>
          </div>
        </template>
      </vxe-column> -->
      <vxe-column field="invoiceRemark" width="80" title="备注" show-overflow></vxe-column>
      <vxe-column field="recordAccountMonth" width="70" title="挂帐月" show-overflow></vxe-column>
      <vxe-column field="serviceName" width="50" title="重复">
        <template #default="{ row }">
          <div>
            <div style="color:red" v-if="row.isItRepeated">重复</div>
          </div>
        </template>
      </vxe-column>
      <vxe-column title="操作">
        <template #default="{ row }">
          <el-tooltip class="btn" effect="dark" content="点击可查看发票文件" placement="left">
            <vxe-button :disabled="!row.originFile" status="warning" type="text" content="发票" @click="showInvoicePictureHandle(row)"></vxe-button>
          </el-tooltip><el-tooltip class="btn" effect="dark" content="录入挂帐月" placement="left"><vxe-button status="warning" type="text" content="挂帐月" @click="updateBilMonth(row)"></vxe-button></el-tooltip>
          <el-button v-if="!row.associatedProcessId && row.outInputInvoice == 'out' && row[codeListKey] && row[codeListCount]==0" :class="{btnCol:isCertModel=='2'}" class="btn" type="text"  @click="showYacc(row)">生成应收凭证</el-button>
          <el-button v-if="!row.associatedProcessId && row.outInputInvoice == 'out' && row[codeListKey] && row[codeListCount]>0" :class="{btnCol:isCertModel=='2'}" class="btn" type="text"  @click="showTranByCode(row,codeKey)">查看应收凭证</el-button>
          <el-button v-if="!row.associatedProcessId && row.outInputInvoice == 'in' && row[codeListKey] && row[codeListCount]==0" :class="{btnCol:isCertModel=='2'}" class="btn" type="text"  @click="showYacc(row,codeKeyYf)">生成应收凭证</el-button>
          <el-button v-if="!row.associatedProcessId && row.outInputInvoice == 'in' && row[codeListKey] && row[codeListCount]>0" :class="{btnCol:isCertModel=='2'}" class="btn" type="text"  @click="showTranByCode(row,codeKeyYf)">查看应收凭证</el-button>
        </template>
      </vxe-column>
    </vxe-table>

    <!--分页-->
    <div style="text-align: right;">
      <pagination
        v-show="total>0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        :page-sizes="[10, 100, 200, 300, 500]"
        layout="total,sizes,prev, pager, next"
        @pagination="getList"
      />
    </div>

    <!-- 查看发票 -->
    <el-dialog
      title="查看发票"
      :visible.sync="showInvoicePicture"
      width="60%"
      :close-on-click-modal=false
    >
      <!-- <el-image
        class="preview-image"
        :src="invoicePictureUrl"
        :preview-src-list="[invoicePictureUrl]">
      </el-image> -->
      <!-- 图片、pdf等发票文件 -->
       <el-image
       class="preview-image"
       :src="invoicePictureUrl"
       v-if="invoicePictureUrl && invoicePictureUrl.indexOf('.pdf') === -1 && invoicePictureUrl.indexOf('.PDF') === -1 "
       :preview-src-list="[invoicePictureUrl]">
      </el-image>
      <!-- pdf -->
       <iframe
        v-else
       :src="invoicePictureUrl"
       width="100%"
       height="500px"
       frameborder="0"
       scrolling="auto"
       ></iframe>
    </el-dialog>
    <vxe-modal v-model="showBillMonth" title="录入挂帐月" width="800" height="80%" resize destroy-on-close>
      <template #default>
        <div v-if="checkedItem" style="margin-bottom: 20px;">
          <div>发票号：{{checkedItem.invoiceNo}}</div>
          <div>发票日期：{{checkedItem.invoiceDate}}</div>
          <div>开票公司：{{checkedItem.sellerName}}</div>
          <!-- <div>船舶名称：{{checkedItem.shipName}}</div> -->
          <div>价税金额：{{checkedItem.invoicePriceTaxSum}}</div>
          <template v-if="checkedItem && checkedItem.originFile">
            <el-image
              v-if="checkedItem.originFile.indexOf('.pdf') === -1 && checkedItem.originFile.indexOf('.PDF') === -1"
              style="width: 100px;height: 400px;margin-top: 10px;"
              @click="showInvoicePictureHandle(checkedItem)"
              :src="checkedItem.originFile">
            </el-image>
            <iframe
              v-else
              style="width: 100%;height: 400px;margin-top: 10px;"
              :src="checkedItem.originFile">
            </iframe>
          </template>
          <div>备注：{{checkedItem.invoiceRemark}}</div>
        </div>

        <el-date-picker
          v-model="form.recordAccountMonth"
          type="month"
          value-format="yyyy/M"
          format="yyyy/M"
          placeholder="选择月">
        </el-date-picker>
        <el-button @click="submitForm" type="primary" size="small" :loading="formLoading">{{formLoading?'loading...':'提交'}}</el-button>
      </template>
    </vxe-modal>
    <el-dialog :title="invoiceTypeUpload==1?'销项发票上传':'进项发票上传'"  :visible.sync="dialogVisible" width="580" :before-close="dialogBeforeClose">
        <div style="max-height: 50vh;overflow: scroll;">
          <upload-excel-component :on-success="handleSuccess" :before-upload="beforeUpload" />
          <el-table :data="tableData" border highlight-current-row style="width: 100%;margin-top:20px;" >
          <el-table-column v-for="item of tableHeader" :key="item" :prop="item" :label="item" />
        </el-table>
        </div>
        <div slot="footer">
            <el-button :loading="uploadLoading" @click="dialogVisible = false">取 消</el-button>
            <el-button :loading="uploadLoading" type="primary" @click="saveInvoiceList(invoiceTypeUpload)">确 定</el-button>
        </div>
    </el-dialog>
    <el-dialog
    :visible.sync="dialogTarnShow"
    title="查看凭证"
    width="65%"
    :before-close="closeTarnShow"
  >
    <resultTrans v-for="item in showTableTrans" :tableData="item" :classList="kjClassList"></resultTrans>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialogTarnShow = false">关闭</el-button>
      </div>
    </template>
  </el-dialog>
    <el-dialog
    :visible.sync="dialogYacc"
    title="生成凭证"
    width="80%"
    :before-close="handleYacc"
  >
    <span>&nbsp;&nbsp;账期:&nbsp;<el-date-picker
      v-model="monthDateAcc"
      type="month"
      value-format="yyyyMM"
      placeholder="选择日期">
    </el-date-picker>
    </span>
    <Subacc :tableData.sync="tableDataAcc"   :accounts="ledgerAccountList" @ledgerAccount="v=> ledgerAccount = v"  :classList="kjClassList"></Subacc>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialogYacc = false">取消</el-button>
        <el-button type="primary" @click="saveYacc()">
          提交
        </el-button>
      </div>
    </template>
  </el-dialog>
  <el-dialog title="" :visible.sync="dialogInvoiceView" width="80%" :before-close="dialogInvoiceViewBeforeClose">
      <div>
        <el-table  v-loading="!loadInvoiceStatus"
        element-loading-text="识别中" :data="invoiceTable" border highlight-current-row style="width: 100%;margin-top:20px;" >
          <el-table-column v-for="item of invoiceTableHeader" :key="item.prop" :prop="item.prop" :label="item.label" />
          <el-table-column
            fixed="right"
            label="操作"
            width="100">
            <template slot-scope="scope">
              <el-button @click="downloadFileByUrl(scope.row.FileUrl)" type="text" size="small">查看</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div slot="footer">
          <el-button :disabled="isLoadImport" @click="dialogInvoiceView = false">取 消</el-button>
          <el-button type="primary" :loading="isLoadImport" @click="importInvoiceByFile">导 入</el-button>
      </div>
  </el-dialog>
  </div>
</template>

<script>
import 'vxe-table/lib/style.css'
import VXETable from 'vxe-table'
import VXETablePluginExportXLSX from 'vxe-table-plugin-export-xlsx';
VXETable.use(VXETablePluginExportXLSX)
import {getAcctgTransListByCode, getHaokjSub,getInvoiceVocherListByCode,getIsCodeByPidAndCode,saveVoucher} from '@/api/business/processapi'
import { delInvoiceDialog, invoiceTypes,listBySelf, updateBilMonthSlef, uploadPurchaseInvoice,uploadSalesInvoice,uploadReceiptInvoice} from "@/api/system/invoice";
import { recognizeOcrByImgurl } from '@/api/system/ocr'
import Pagination from "@/components/Pagination";
import UploadExcelComponent from '@/components/UploadExcel/index.vue'
import { UPLOAD_URL } from '@/utils/config'
import resultTrans from '@/views/system/businessPayment/resultTrans.vue';
import Subacc from '@/views/system/businessPayment/subacc.vue'
import currency from 'currency.js'
import dayjs from 'dayjs'
import AccModel from '@/components/AccModel/index.vue'
export default {
  name: "Invoice",
  components: {Pagination,UploadExcelComponent,Subacc,resultTrans,AccModel},
  data() {
    return {
      isLoadImport:false,
      loadInvoiceStatus:false,
      dialogInvoiceView:false,
      // 购买方 BuyerName|Buyer|
    // 购买方纳税人识别号 BuyerTaxID
    // 发票名称 Title
    // 发票号码 Number|ReceiptNumber
    // 开票日期 Date
    // 价税合计 Total|Fare
    // 税额 Tax|TaxAmount
    // 税率 TaxRate
    // 备注 Remark
    // 销售方 Seller
    // 销售方纳税人识别号 SellerTaxID
    // 开票人 Issuer
    // 复核人 Reviewer
    // 发票消费类型|业务类型标志|种类  Kind|ServiceTypeLabel|Category
      // 税前金额 PretaxAmount
      invoiceTableHeader:[{label:"开票日期",prop:"Date"},{label:"开票公司",prop:"Seller"},{label:"收票公司",prop:"Buyer"},{label:"发票号",prop:"Number"},{label:"发票类型",prop:"Title"},{label:"价税合计",prop:"Total"},{label:"税额",prop:"Tax"},{label:"税率",prop:"TaxRate"}],
      invoiceTable:[],
      upload: UPLOAD_URL,
      fileList: [],
      codeKey: 'receivableVouchers_receipt',
      codeKeyYf:'receivableVouchers_receiptyf',
      codeListKey: 'rKey',
      codeListCount:'rCount',
      // modelList:[{"code":"in","value":"进项"},{"code":"out","value":"销项"},{"code":"receipt","value":"费用小票"}],
      modelList:[{"code":"in","value":"进项"},{"code":"out","value":"销项"},{"code":"in_receipt","value":"费用小票"}],
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        status: 0, // 2 = 待入账
        sellerName: null,
        shipName: null,
        invoiceDate: null,
        invoiceNo: null,
        costTypeName: null,
        isItRepeated:null,
        type: 10,
        outInputInvoice:'in',
      },
      list: [],
      total: 0,
      tableLoading: false,
      monthDateAcc: dayjs().format('YYYYMM'),
      checkedCount: 0,
      registerDisable: true,
      invoicePictureUrl: null,
      showInvoicePicture: false,

      showBillMonth: false,
      form: {
        recordAccountMonth: null,
        status: 3
      },
      formLoading: false,
      checkedItem: null,
      invoiceTypeUpload: 1, // 1 销项 2 进项
      dialogVisible: false,
      uploadLoading: false,
      delLoading:false,
      invoiceTypeList: [],
      tableData: [],
      // tableHeader: [],
      invoiceTypeUploadList: [],
      kjClassList: [],
      tableDataAcc: [],
      dialogYacc: false,
      showTableTrans: [],
      dialogTarnShow: false,
      ledgerAccount:'',
      ledgerAccountList:[],
      isCertModel:'1',
    }
  },
  computed: {
    tableHeaderMap() {
      const map1 = new Map();
      if (this.invoiceTypeUploadList) {
        this.invoiceTypeUploadList.forEach(item => {
          map1.set(item.name, item.code)
        })
      }
      return map1
    },
    tableHeader() {
      if (this.invoiceTypeUploadList) {
        return this.invoiceTypeUploadList.map(item => item.name)
      }
      return []
    },
    toRegister() {
      let r = `挂帐月`
      if (this.checkedCount > 0) {
        r += `(${this.checkedCount})`
      }
      return r
    },
    checkedList() {
      try {
        return this.$refs.xTable1.getCheckboxRecords()
      } catch (err) {
        // nothing
      }
      return []
    },
    invoiceTypeObj() {
      if (this.invoiceTypeList) {
        return this.invoiceTypeList.reduce((obj, item) => {
          obj[item.code] = item.name
          return obj
          }, {})
      }
      return {}
    },
    totalDebit() {
      return this.tableDataAcc.reduce((sum, row) => sum + Number((row.debit||0)), 0);
    },
    totalCredit() {
      return this.tableDataAcc.reduce((sum, row) => sum + Number((row.credit||0)), 0);
    },
  },
  created () {
    this.$nextTick(() => {
      // 手动将表格和工具栏进行关联
      this.$refs.xTable1.connect(this.$refs.xToolbar1)
    })
    invoiceTypes().then(res => {
      this.invoiceTypeList = res && res.content || []
    })
    invoiceTypes('upload').then(res => {
      this.invoiceTypeUploadList = res && res.content || []
    })
    this.loadKeMuList()
    this.getList()
  },
  mounted() {

  },
  methods: {
    updateAccModel(type){
      this.isCertModel = type
      this.uplistByListPz()
    },
    invoiceTableData(){
     return  this.invoiceTable.map(item=>{
        return {invoiceNo:item.Number,
          invoiceDate:item.Date,
          invoicePrice:item.PretaxAmount,
          invoicePriceTax:item.Tax,
          invoicePriceTaxSum:item.Total,
          invoiceTax:item.TaxRate,
          buyerName:item.Buyer,
          buyerTaxpayerNo:item.BuyerTaxID,
          sellerName:item.Seller,
          sellerTaxpayerNo:item.SellerTaxID,
          originFile:item.FileUrl,
          invoiceDrawer:item.Issuer,
          invoiceReview:item.Reviewer,
          invoiceRemark:item.Remark,
          invoiceType:item.invoiceType || item.subTypeName,
          title:item.Title,
          subTypeName:item.type
        }

      })
    },
    importInvoiceByFile(){
      this.isLoadImport=true
      // invoiceTable
      // uploadSalesInvoice
      // uploadPurchaseInvoice
      if(this.queryParams.outInputInvoice === 'in'){
        uploadPurchaseInvoice(this.invoiceTableData()).then(()=>{
          this.getList()
          this.dialogInvoiceViewBeforeClose()
        }).finally(()=>{
          this.isLoadImport=false
        })
      }else if(this.queryParams.outInputInvoice === 'in_receipt'){
        uploadReceiptInvoice(this.invoiceTableData()).then(()=>{
          this.getList()
          this.dialogInvoiceViewBeforeClose()
        }).finally(()=>{
          this.isLoadImport=false
        })
      }else{
        uploadSalesInvoice(this.invoiceTableData()).then(()=>{
          this.getList()
          this.dialogInvoiceViewBeforeClose()
        }).finally(()=>{
          this.isLoadImport=false
        })
      }

    },
    dialogInvoiceViewBeforeClose(){
      this.dialogInvoiceView = false
    },
    handlePreview(file) {
      // let { href } = this.$router.resolve({ path: file.response.url })
      if (file.response.url){
        this.downloadFileByUrl(file.response.url, file.name)
      }
    },
    downloadFileByUrl(url, name='') {
      if(!name){
        name = url
      }
      if (name.indexOf('.pdf') !== -1 || name.indexOf('.png') !== -1 || name.indexOf('.jpeg') !== -1 || name.indexOf('.jpg') !== -1) {
        window.openWindow(url, name, 800, 600)
        return
      }
      this.getBlob(url, (blob) => {
        this.saveAs(blob, name)
      })
    },
    getBlob(url, cb) {
      var xhr = new XMLHttpRequest()
      xhr.open('GET', url, true)
      xhr.responseType = 'blob'
      xhr.onload = () => {
        if (xhr.status === 200) {
          cb(xhr.response)
        }
      }
      xhr.send()
    },
    saveAs(blob, filename) {
      if (window.navigator.msSaveOrOpenBlob) {
        navigator.msSaveBlob(blob, filename)
      } else {
        var link = document.createElement('a')
        var body = document.querySelector('body')

        link.href = window.URL.createObjectURL(blob)
        link.download = filename

        // fix Firefox
        link.style.display = 'none'
        body.appendChild(link)

        link.click()
        body.removeChild(link)

        window.URL.revokeObjectURL(link.href)
      }
    },
    handleRemove(file, fileList) {
    },
    beforeRemove(file, fileList) {
      return this.$confirm(`确定移除 ${file.name}？`)
    },
    savefileList(response, file, fileList) {
      this.dialogInvoiceView = true
      // console.log('success-savefileList', response, file, fileList)
      this.fileList = fileList

      if (!response || response.resultCode != 0 || !response.url) {
        // if (!this.fileListg[this.groupindex]) {
        //   this.fileListg[this.groupindex] = []
        // }
        // this.fileListg[this.groupindex].push(file)
        this.fileList.pop()
        this.$message.error('上传失败，请重新上传')
      }
      this.loadChaiLv(file)
    },
    loadChaiLv(mfile) {
      this.loadInvoiceStatus = false
      // const invoiceObj = mfile ? this.invoiceData : []
      // console.log('chailv', this.fileListg)
      this.invoiceTable = []
      if (this.fileList && Object.keys(this.fileList).length > 0 && mfile && mfile.response && mfile.response.url) {
            recognizeOcrByImgurl(mfile.response.url).then(res => {
              if (res) {
                this.invoiceTable.push(...this.ppResNe(JSON.parse(res), mfile.response.url))
                // this.dialogInvoiceView = true
                this.loadInvoiceStatus = true
              }
            }).finally(() => {
            })
      }
    },
    invoiceOcr(item,fileUrl,invoiceType) {
      // 所需信息 ， 相关key，多个|分割

    // 购买方 BuyerName|Buyer|
    // 购买方纳税人识别号 BuyerTaxID
    // 发票名称 Title
    // 发票号码 Number|ReceiptNumber
    // 开票日期 Date
    // 价税合计 Total|Fare
    // 税额 Tax|TaxAmount
    // 税率 TaxRate
    // 备注 Remark
    // 销售方 Seller
    // 销售方纳税人识别号 SellerTaxID
    // 开票人 Issuer
    // 复核人 Reviewer
    // 发票消费类型|业务类型标志|种类  Kind|ServiceTypeLabel|Category
      // 税前金额 PretaxAmount
      const subs = item.VatInvoiceItemInfos || item.VatElectronicItems || item.GeneralMachineItems || item.Items
      const obj = {}
      obj.Buyer = item.BuyerName || item.Buyer
      obj.BuyerTaxID = item.BuyerTaxID
      obj.Title = item.Title
      obj.Number = item.Number || item.ReceiptNumber
      obj.Date = item.Date
      obj.Total = item.Total || item.Fare
      obj.Tax = item.Tax || item.TaxAmount
      obj.TaxRate = item.TaxRate || (subs && subs.map(i=>i.TaxRate||'').join(','))
      obj.Remark = item.Remark
      obj.Seller = item.Seller
      obj.SellerTaxID = item.SellerTaxID
      obj.Issuer = item.Issuer
      obj.Reviewer = item.Reviewer
      obj.Kind = item.Kind || item.ServiceTypeLabel || item.Category
      obj.PretaxAmount = item.PretaxAmount
      obj.FileUrl = fileUrl
      obj.InvoiceType= invoiceType


      return obj
    },
    shuiFax(price,fax) {
      return currency(price / (1+fax) * fax).value
    },
    ppResNe(res, fileUrl) {
      const data = []
      // this.invoiceData = []
      const day = dayjs().format('YYYY-MM-DD')
      if (res.MixedInvoiceItems) {
        res.MixedInvoiceItems.forEach(item => {
          const obj = {}
          const invoice = this.invoiceOcr(item.SingleInvoiceInfos[item.SubType],fileUrl,item.SubType)
          // if (invoice.Number) {
          //   this.invoiceData.push(invoice)
          // }
          // type item.TypeDescription
          // 铁路电子客票 9%
          const v9 = ['TrainTicket','ElectronicTrainTicketFull','ElectronicFlightTicketFull','AirTransport']
          // if (item.SingleInvoiceInfos.TrainTicket) {
          // obj.type = 'TrainTicket'
          if (v9.includes(item.SubType)) {
            obj.type = item.SubType
            obj.typeName = item.TypeDescription
            // obj.author = this.obj.name
            obj.subTypeName = item.SubTypeDescription
            obj.total = item.SingleInvoiceInfos[item.SubType].Total || item.SingleInvoiceInfos[item.SubType].Fare
            obj.sum = obj.total
            obj.documentDate = day
            obj.invoiceIssuer = item.SingleInvoiceInfos[item.SubType].Buyer
            obj.invoiceQuantity = item.Page
            obj.totalCn = item.SingleInvoiceInfos[item.SubType].TotalCn
            obj.userId = item.SingleInvoiceInfos[item.SubType].UserId
            obj.userName = item.SingleInvoiceInfos[item.SubType].UserName || item.SingleInvoiceInfos[item.SubType].Name || ''
            obj.code = item.SingleInvoiceInfos[item.SubType].Number
            // 0.09
            if (obj.userName && this.isChaiLvBX) {
              if (item.SingleInvoiceInfos[item.SubType].Tax) {
                obj.tax = item.SingleInvoiceInfos[item.SubType].Tax
                obj.taxRate = item.SingleInvoiceInfos[item.SubType].TaxRate
                obj.pretaxAmount = currency(obj.total - obj.tax).value
              // } else {
              //   obj.tax = this.shuiFax(obj.total, 0.09)
              //   obj.taxRate = '9%'
              //   obj.pretaxAmount = currency(obj.total - obj.tax).value
              }
            }

          }
          // 公路、水运客票 3%
          const v3 = ['TollInvoice', 'ShippingInvoice', 'BusInvoice', 'TaxiTicket']
          if (v3.includes(item.SubType)) {
            obj.type = item.SubType
            obj.typeName = item.TypeDescription
            obj.subTypeName = item.SubTypeDescription
            obj.total = item.SingleInvoiceInfos[item.SubType].Total
            obj.sum = obj.total
            // obj.author = this.obj.name
            obj.documentDate = day
            obj.invoiceQuantity = item.Page
            obj.invoiceIssuer = item.SingleInvoiceInfos[item.SubType].Buyer
            obj.totalCn = item.SingleInvoiceInfos[item.SubType].TotalCn
            obj.userName = item.SingleInvoiceInfos[item.SubType].UserName
            obj.userId = item.SingleInvoiceInfos[item.SubType].UserId
            obj.code = item.SingleInvoiceInfos[item.SubType].Number
            if (obj.userName && this.isChaiLvBX) {
              if (item.SingleInvoiceInfos[item.SubType].Tax) {
                obj.tax = item.SingleInvoiceInfos[item.SubType].Tax
                obj.taxRate = item.SingleInvoiceInfos[item.SubType].TaxRate
                obj.pretaxAmount = currency(obj.total - obj.tax).value
              // } else {
              //   obj.tax = this.shuiFax(obj.total, 0.03)
              //   obj.taxRate = '3%'
              //   obj.pretaxAmount = currency(obj.total - obj.tax).value
              }
            }
          }
          const vs = ['VatSpecialInvoice', 'VatCommonInvoice', 'VatElectronicCommonInvoice', 'VatElectronicSpecialInvoice', 'VatElectronicInvoiceToll'
            ,'VatElectronicSpecialInvoiceFull', 'VatElectronicInvoiceFull', 'VatInvoiceRoll'
            ,'MachinePrintedInvoice','NonTaxIncomeGeneralBill','QuotaInvoice'
          ]
          const zp = ['VatSpecialInvoice', 'VatElectronicSpecialInvoice', 'VatElectronicSpecialInvoiceFull']
          const pp = ['VatCommonInvoice','VatElectronicCommonInvoice','VatElectronicInvoiceToll','VatElectronicInvoiceFull']
          if (vs.includes(item.SubType)) {
            obj.type = item.SubType
            obj.typeName = item.TypeDescription
            obj.subTypeName = item.SubTypeDescription
            obj.userId = item.SingleInvoiceInfos[item.SubType].UserId
            obj.userName = item.SingleInvoiceInfos[item.SubType].UserName || item.SingleInvoiceInfos[item.SubType].Name || ''
            obj.total = item.SingleInvoiceInfos[item.SubType].Total
            obj.sum = obj.total
            // obj.author = this.obj.name
            if (zp.includes(item.SubType)) {
              obj.invoiceType = '专票'
            }
            if (pp.includes(item.SubType)) {
              obj.invoiceType = '普票'
            }
            obj.documentDate = day
            obj.invoiceQuantity = item.Page
            obj.invoiceIssuer = item.SingleInvoiceInfos[item.SubType].Buyer
            obj.totalCn = item.SingleInvoiceInfos[item.SubType].TotalCn
            if ('专票' == obj.invoiceType || obj.userName) {
              obj.tax = item.SingleInvoiceInfos[item.SubType].Tax
              const arr = item.SingleInvoiceInfos[item.SubType].VatInvoiceItemInfos || item.SingleInvoiceInfos[item.SubType].VatElectronicItems || item.SingleInvoiceInfos[item.SubType].GeneralMachineItems || []
              const uniqueTaxRates = [...new Set(arr.map(obj => obj.TaxRate))];
              obj.taxRate = uniqueTaxRates.join(',')
              if (!obj.taxRate && item.SingleInvoiceInfos[item.SubType].TaxRate) {
                obj.taxRate = item.SingleInvoiceInfos[item.SubType].TaxRate
              }
              obj.pretaxAmount = item.SingleInvoiceInfos[item.SubType].PretaxAmount
            }


            obj.code = item.SingleInvoiceInfos[item.SubType].Code || item.SingleInvoiceInfos[item.SubType].Number
          }
          if (Object.keys(obj).length > 0) {
            if (this.isYeWuZhaoDai) {
              obj.costType = '业务招待费'
              if (obj.tax) {
                obj.taxOut = obj.tax
              }
            }
            obj.fileUrl = fileUrl
            // data.push(obj)
            if (invoice && invoice.Number) {
              data.push(Object.assign({},obj,invoice))
            } else {
              data.push(obj)
            }
          }
        })
      }
      return data
    },
    // beforeUpload(file) {
    //   // console.log('beforeUpload', file, this.groupindex)
    //   if (!this.tmpFileMap) {
    //     this.tmpFileMap = {}
    //   }
    //   this.tmpFileMap[file.uid] = this.groupindex
    //   this.beforeUploadStatus++
    // },
    handleError() {

    },
    handleExceed(files, fileList) {
      this.$message.warning(`当前限制选择 3 个文件，本次选择了 ${files.length} 个文件，共选择了 ${files.length + fileList.length} 个文件`)
    },
    modelHandleClick(){
      console.log('406')
    },
    closeTarnShow() {
      this.dialogTarnShow = false
    },
    loadTranListByProcessId(pid, type,func=undefined) {
      const loading = this.$loading({
        lock: true,
        text: 'Loading',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.2)'
      })
      this.showTableTrans = []
      getAcctgTransList(pid, type).then(res => {
        this.showTableTrans = res.result
        func && func()
      }).finally(() => {
        loading.close()
      })
    },
    handleYacc() {
      this.dialogYacc=false
    },
    showYacc(data,code='') {
      this.ctypeCode = code || this.codeKey
      this.loadAccTable(data.id, this.ctypeCode, () => {
        this.dialogYacc = true
        // 刷新
      })
    },
    showTranByCode(data,code) {
      this.loadTranListByProcessIdAndCode(data.id, code, () => {
        this.dialogTarnShow = true
      })
    },
    loadTranListByProcessIdAndCode(pid, code,func=undefined) {
      const loading = this.$loading({
        lock: true,
        text: 'Loading',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.2)'
      })
      this.showTableTrans = []
      getAcctgTransListByCode(pid, code).then(res => {
        this.showTableTrans = res.result
        func && func()
      }).finally(() => {
        loading.close()
      })
    },
    saveYacc() {
      this.saveAccTable(this.ctypeCode, () => {
        this.handleYacc()
      })
    },
    saveAccTable(type = '1', func = undefined) {
      // 验证数据，
      if (this.totalCredit != this.totalDebit) {
        this.$message.error('借方金额与贷方金额不一致')
        return
      }
      // if (type == '-1' && this.totalCredit > this.paymentTotal) {
      //   this.$message.error('借贷金额大于付款金额')
      //   return
      // }
      const loading = this.$loading({
        lock: true,
        text: 'Loading',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.2)'
      })

      saveVoucher(this.tableProcessIdAcc, type, this.monthDateAcc,this.tableDataAcc,this.sarAccSingle?'1':'0',this.ledgerAccount).then((data) => {
        if (data.result) {
          this.$message.success('保存成功')
          func && func()
        } else {
          this.$message.error(data.msg || '保存失败')
        }
          this.loadIsCodeByPidAndCode(this.tableProcessIdAcc, type, (res) => {
            this.upPzCodeToList(res,this.codeListKey,this.codeListCount)
          })
        loading.close()
      }).catch((err) => {
        this.$message.error('保存失败')
        loading.close()
      })
    },
    upPzCodeToList(res,key,countKey) {
      if (!res) {
          return
      }
      for (let i = 0; i < this.list.length; i++) {
          const item = this.list[i]
          if (res[item.id]) {
              item[key] = res[item.id].code
              item[countKey] = res[item.id].count
          }
      }
      this.list = this.list.slice()
    },
    loadIsCodeByPidAndCode(pids,code,func=undefined) {
      getIsCodeByPidAndCode(pids,code).then(res => {
        func && func(res)
      })
    },
    loadAccTable(pid, type='1',func=undefined) {
      const loading = this.$loading({
        lock: true,
        text: 'Loading',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.2)'
      })
      this.tableDataAcc = []
      this.tableProcessIdAcc = pid
      this.voucherCode = ''
      // if (type == '-1' && !this.paymentTotal) {
      //   loading.close()
      //   return
      // }
      // getInvoiceVocherListByCode(pid, type,this.sarAccSingle?'1':'',this.paymentTotal).then((res) => {
      getInvoiceVocherListByCode(pid, type).then((res) => {
        // console.log('res', res)
        // let list = []
        // list = res && res?.data ? res.data : []
        this.tableDataAccCode(res)
        func && func()
        loading.close()
      }).catch((err) => {
        this.$message.error(err.message||'暂不支持此类型')
        loading.close()
      })
    },
    tableDataAccCode(res) {
        const list = res && res.data ? res.data : []
        this.voucherCode = res && res.code
        this.tableDataAcc = list || []
        this.ledgerAccountList = res && res.accounts
        this.upSubIdByFind()
    },
    loadKeMuList() {
      getHaokjSub().then(res => {
        if (res && res.data) {
          this.kjClassList = res.data || []
          this.upSubIdByFind()
        }
      })
    },
    upSubIdByFind() {
      if (this.tableDataAcc && this.tableDataAcc.length > 0 && this.kjClassList && this.kjClassList.length > 0) {
        // tableDataAcc 中 findSub 获取 kjClassList glAccountName 相等 ，treePath(2241^********^) = subject (2241)^
        //tableDataAcc -> findSub,subject
        // kjClassList -> glAccountName,treePath, glAccountCode
         // 承兑 最后一个科目改成1121
        //  if (3 == this.paymentType) {
        //   this.tableDataAcc[this.tableDataAcc.length - 1].findSubId = '1121'
        //   this.tableDataAcc[this.tableDataAcc.length - 1].subject = '1121'
        //  }
        //   if (1 == this.paymentType) {
        //     this.tableDataAcc[this.tableDataAcc.length - 1].findSubId = '1001'
        //     this.tableDataAcc[this.tableDataAcc.length - 1].subject = '1001'
        //   }

        for (let i = 0; i < this.tableDataAcc.length; i++) {
          if (this.tableDataAcc[i].findSub && !this.tableDataAcc[i].findSubId) {
            try {
              const fundsCode = this.kjClassList.find(item => item.treePath.startsWith(this.tableDataAcc[i].subject + '^')  && item.glAccountName == this.tableDataAcc[i].findSub)
              if (fundsCode) {
                this.tableDataAcc[i].findSubId = fundsCode.glAccountCode
                this.tableDataAcc[i].subject = fundsCode.glAccountCode
              }
            } catch (error) {
              console.log('error', error)
            }
          }
        }
      }
    },
    saveInvoiceList(type) {
      this.uploadLoading = true
      if (type == 1) {
        uploadSalesInvoice(this.typeUploadName()).then(res => {
          this.dialogVisible = false
          this.uploadLoading = false
          this.getList()
        }).catch((e) => {
          this.uploadLoading = false
          this.$message.error(e)
        })
      } else if(type == 2) {
        uploadPurchaseInvoice(this.typeUploadName()).then(res => {
          this.dialogVisible = false
          this.uploadLoading = false
          this.getList()
        }).catch((e) => {
          this.uploadLoading = false
          this.$message.error(e)
        })
      }else{
        uploadReceiptInvoice(this.typeUploadName()).then(res => {
          this.dialogVisible = false
          this.uploadLoading = false
          this.getList()
        }).catch((e) => {
          this.uploadLoading = false
          this.$message.error(e)
        })
      }
    },
    typeUploadName() {
      if (this.tableData) {
        return this.tableData.map(item => {
          const a = {}
          Object.keys(item).forEach(key => {
            if (item[key] != '' && this.tableHeaderMap.has(key)) {
              // item[key] = this.tableHeaderMap.get(key)
              a[this.tableHeaderMap.get(key)] = item[key]
            }
          })
          return a
        })
      }
      return []
    },
    dialogBeforeClose() {
      this.dialogVisible=false
    },
    openUploadInvoce(type = 1) {
      this.dialogVisible = true
      this.invoiceTypeUpload = type
      this.tableData = []
      // this.tableHeader = []

    },
    uploadInvoceFile(type){
      // 上传文件
    },
    beforeUpload(file) {
      const isLt1M = file.size / 1024 / 1024 < 5
      if (isLt1M) {
        return true
      }

      this.$message({
        message: '请不要上传大于5m的文件.',
        type: 'warning'
      })
      return false
    },
    handleSuccess({ results, header }) {
      this.tableData = results
      // this.tableHeader = header
      console.log('handleSuccess',results, header)
    },
    getList() {
      this.tableLoading = true
      this.queryParams['type'] = this.queryParams['type'] + ''
      if (this.queryParams['billMonth']) {
        this.queryParams['status'] = 3
      }
      listBySelf(this.queryParams).then(res => {
        this.list = res.data
        this.total = res.total
        this.uplistByListPz()
      }).finally(() => {
        this.tableLoading = false
      })
    },
    uplistByListPz(){
      // pids this.dataList
      const pids = this.list && this.list.map(item => item.id).join(',')
      if (!pids) {
        return
      }
      this.loadIsCodeByPidAndCode(pids, this.codeKey, (res) => {
          this.upPzCodeToList(res,this.codeListKey,this.codeListCount)
      })
    },
    openExportEvent () {
      this.$refs.xTable1.openExport()
    },
    resetQuery() {
      this.queryParams = this.$options.data().queryParams
      this.getList()
    },
    selectChangeEvent ({ checked }) {
      const records = this.$refs.xTable1.getCheckboxRecords()
      this.checkedCount = records.length
      this.registerDisable = new Set(records.map(item => item.sellerName)).size !== 1
    },
    showInvoicePictureHandle(row) {
      this.invoicePictureUrl = row.originFile
      this.showInvoicePicture = true
    },
    submitForm() {
      this.formLoading = true
      updateBilMonthSlef(this.form).then(res => {
        this.showBillMonth = false
        this.getList()
      }).catch(err => {
        // nothing
      }).finally(() => {
        this.formLoading = false
      })
    },
    updateBatchBilMonth() {
      this.form.id = null
      this.form.ids = this.checkedList.map(item => item.id)

      this.showBillMonth = true
      // this.updateBilMonthDialog({
      //   ids: this.checkedList.map(item=>item.id)
      // })
    },
    delInvoices() {
      if (!this.checkedList || this.checkedList.length == 0) {
        this.$message.warning('请选择要删除的发票')
        return
      }
      this.delLoading = true
      delInvoiceDialog(
         this.checkedList.map(item => item.id)
      ).then(() => {
        this.getList()
      }).finally(() => {
        this.delLoading = false
      })
    },
    updateBilMonth(row) {
      // this.form.ids = null
      // this.form.id = row.id
      this.showBillMonth = true
      this.checkedItem = row
      this.form = {
        recordAccountMonth: null,
        status: 3,
        id: row.id,
        ids: null
      }

      // this.updateBilMonthDialog({
      //   id: row.id
      // })
    },
    updateBilMonthDialog(idObj) {
      this.$prompt('请输入挂帐月(1～12)', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        closeOnClickModal: false,
        closeOnPressEscape: false,
        inputPattern: /^(?:1[0-2]|[1-9])$/,
        inputErrorMessage: '请输入整数(1～12)',
        beforeClose: (action, instance, done) => {
          if (action === 'confirm') {
            instance.confirmButtonLoading = true;
            instance.confirmButtonText = 'loading...';
            updateBilMonthSlef(Object.assign({
              recordAccountMonth: Number(instance.inputValue),
              status: 3
            },idObj)).then(res => {
              done()
            }).catch(err => {
              // nothing
            }).finally(() => {
              instance.confirmButtonLoading = false;
              instance.confirmButtonText = '确定';
            })
          } else {
            done();
          }
        }
      }).then(({ value }) => {
        // nothing
      }).catch(() => {
        // nothing
      });
    },
    sumNum (list, field) {
      let count = 0
      list.forEach(item => {
        count += Number(item[field])
      })
      return count.toFixed(2)
    },
    footerMethod ({ columns, data }) {
      return [
        columns.map((column, columnIndex) => {
          if (columnIndex === 0) {
            return '合计'
          }
          if (['invoicePriceTaxSum', 'invoicePriceTax','invoicePrice'].includes(column.property)) {
            return this.sumNum(data, column.property)
          }
          return null
        })
      ]
    },
  }
}
</script>

<style scoped>
 .btnCol{
    color: #13ce66;
  }
  .btnCol:hover{
    color:#10b85a;
  }
</style>
