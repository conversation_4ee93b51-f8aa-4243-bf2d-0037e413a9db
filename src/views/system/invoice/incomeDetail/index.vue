<template>
   <div class="app-container">
    <div>
      <el-form
        :inline="true"
        :model="params"
        class="query-form demo-form-inline"
      >
        <el-form-item label="挂账月">
          <el-date-picker
            v-model="params.billMonth"
            value-format="yyyy-MM"
            type="month"
            placeholder="选择月"
          >
          </el-date-picker>
        </el-form-item>
        <!-- <el-form-item label="发票状态">
          <el-select v-model="params.status" placeholder="">
             <el-option label="全部" style="padding-left: 20px;" value="">全部</el-option>
             <el-option label="未核销" style="padding-left: 20px;" value="0">未核销</el-option>
             <el-option label="已核销" style="padding-left: 20px;" value="1">已核销</el-option>
          </el-select>
        </el-form-item>
          <el-form-item label="税率">
          <el-select v-model="params.tax" placeholder="">
             <el-option label="全部" style="padding-left: 20px;" value="">全部</el-option>
             <el-option label="6%" style="padding-left: 20px;" value="6%">6%</el-option>
             <el-option label="9%" style="padding-left: 20px;" value="9%">9%</el-option>
          </el-select>
        </el-form-item> -->
        <el-form-item>
          <el-button @click="onQuery" type="primary">查询</el-button>
        </el-form-item>
      </el-form>
    </div>
    <el-table
      :data="list"
      border
      stripe
      show-summary
      :summary-method="getSummaries"
      ref="table"
      style="width: 100%"
    >
     <el-table-column
        align="center"
        type="index"
        width="55"
      >
      </el-table-column>
      <el-table-column
        prop="invoiceDate"
        label="日期"
        width="65px"
        :formatter="formatDef"
        align="center"
      >
      </el-table-column>
      <!-- <el-table-column
        prop="invoiceCode"
        align="center"
        :formatter="formatDef"
        label="发票代码"
      >
      </el-table-column> -->
      <el-table-column
        prop="invoiceNo"
        align="center"
        :formatter="formatDef"
        label="发票号码"
      >
      </el-table-column>
      <el-table-column
        prop="shipName"
        align="center"
        :formatter="formatDef"
        label="船名"
      >
      <template slot-scope="info">
        <div >
          {{ info.row.shipName||info.row.invoiceRemark }}
        </div>
      </template>
      </el-table-column>
      <el-table-column
        prop="buyerName"
        align="center"
        :formatter="formatDef"
        label="收货人"
      >
      </el-table-column>
      <!-- <el-table-column
        prop="senderName"
        align="center"
        :formatter="formatDef"
        label="发货人"
      >
      </el-table-column> -->
      <el-table-column
        prop="sellerName"
        align="center"
        :formatter="formatDef"
        label="客户名称"
      >
      </el-table-column>

      <el-table-column
        prop="invoiceRemark"
        align="center"
        :formatter="formatDef"
        label="备注"
      >
      </el-table-column>
      <!-- <el-table-column
        align="center"
        :formatter="formatDefShuiE"
        label="含税运价"
      >
      </el-table-column> -->
      <el-table-column
        prop="invoicePriceTaxSum"
        align="center"
        :formatter="formatDefNo"
        label="金额"
      >
      </el-table-column>
      <el-table-column
        prop="invoicePriceTax"
        align="center"
        :formatter="formatDefNo"
        label="税额"
      >
      </el-table-column>
      <el-table-column
        prop="invoicePrice"
        align="center"
        :formatter="formatDefNo"
        label="不含税"
      >
      </el-table-column>
      <el-table-column prop="invoiceTax" align="center" width="40px" label="税率">
      </el-table-column>
    </el-table>
    <el-pagination
      background
      layout="prev, pager, next,sizes"
      :total="total"
      :page-size="params.pageSize"
      :current-page.sync="params.pageNum"
      :page-sizes="[10, 100, 200, 300, 500]"
      @size-change="handleSizeChange"
      @current-change="eventPage"
      style="text-align: right;padding: 10px 0px;background-color: #fff"
    >
    </el-pagination>
  </div>
</template>
<script>
import { invoiceShipIncomeList} from "@/api/system/invoice";
import dayjs from 'dayjs'
import currency from 'currency.js'
export default{
  name: 'estimatedRevenue',
  props:{
    type:{
      type:String,
      default:''
    }
  },
  data(){
    return {
      params:{
        billMonth:dayjs().format('YYYY-MM'),
        status:'',
        pageNum:1,
        pageSize:10,
        tax:''
      },
      list:[],
      total:0
    }
  },
  created(){
    this.loadList()
  },
  methods:{
    handleSizeChange(v){
      this.params.pageSize = v;
      this.loadList();
    },
    eventPage(){
      this.loadList()
    },
    shuiLv(v,p){
      if(!p){
        return 0
      }
      if(!v){
        return p
      }
      if(v.indexOf('%')>-1){
        return p / (1+Number(v.replace('%',''))/100)
      }
      return p
    },
    formatDate(row, column, cellValue, index) {
      if(cellValue){
        return dayjs(cellValue).format('YYYY-MM-DD')
      }
      return '--'
    },
    formatDef(row, column, cellValue, index) {
      if (cellValue) {
        return cellValue + "";
      }
      return "--";
    },
     fmtMoney(v) {
      if (!v && v != 0) {
        return "--";
      } else {
        return currency(v, { symbol: "" }).format();
      }
    },
    formatDefNo(row, column, cellValue, index) {
      if (cellValue) {
        return this.fmtMoney(cellValue);
      }
      return "--";
    },
    onQuery(){
      this.loadList()
    },
    loadList(){
      const data = {
        pageNum:this.params.pageNum,
        pageSize:this.params.pageSize,
        billMonth:this.params.billMonth,
        status:this.params.status,
        invoiceTax:this.params.tax,
        type:this.type
      }
      invoiceShipIncomeList(data).then(res=>{
        console.log('19',res)
        this.list = res.data.content
        this.total = res.data.totalElements
      })
    },
    getSummaries(param) {
      const { columns, data } = param;
      const sums = [];
      columns.forEach((column, index) => {
        if (index === 0) {
          sums[index] = "合计";
          return;
        }
         if (column.label==='日期'  || column.label==='发票号码'  ||column.label==='离港日期' || column.label==='航次号'  || column.label==='税率'|| column.label==='发票状态') {
          sums[index] = ""
          return
        }
        // if (index != 14 && index != 12 && index != 13) {
        //   sums[index] = ""
        //   return
        // }
        const values = data.map(item => Number(item[column.property]));
        if (!values.every(value => isNaN(value))) {
          sums[index] = values.reduce((prev, curr) => {
            const value = Number(curr);
            if (!isNaN(value)) {
              return prev + curr;
            } else {
              return prev;
            }
          }, 0);
          sums[index] = this.fmtMoney(sums[index]);
        } else {
          sums[index] = "";
        }
      });

      return sums;
    },
  }
}

</script>
