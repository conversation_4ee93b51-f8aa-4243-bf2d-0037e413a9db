<template>
  <section>
    <el-tabs v-model="activeName" type="card">
      <el-tab-pane label="物流" name="incomeWuliu"><wuliu></wuliu></el-tab-pane>
      <el-tab-pane label="船舶" name="incomeShip"><ship type="ship"></ship></el-tab-pane>
      <el-tab-pane label="油品" name="incomeOil"><ship type="oil"></ship></el-tab-pane>
      <el-tab-pane label="油品运费" name="incomeOilCost"><ship type="oilCost"></ship></el-tab-pane>
    </el-tabs>
  </section>
</template>
<script>
import wuliu from '../incomeDetail/wuliu.vue'
import ship from '../incomeDetail/index.vue'
export default{
  components: { wuliu,ship },
  data(){
    return {
      activeName:'incomeWuliu'
    }
  },
}
</script>
<style scoped></style>
