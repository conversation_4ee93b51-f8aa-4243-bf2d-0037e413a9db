<template>
  <section>
     <el-tabs v-model="activeName" type="card">
        <el-tab-pane label="海运费" name="wuliu">
          <estimated-cost-wuliu></estimated-cost-wuliu>
        </el-tab-pane>
        <el-tab-pane label="船上费用" name="ship">
          <estimated-ship-cost></estimated-ship-cost>
        </el-tab-pane>
        <el-tab-pane label="油品费用" name="shipOil">
          <estimated-ship-oil-cost></estimated-ship-oil-cost>
        </el-tab-pane>
        <el-tab-pane label="油品运费" name="shipOilFreight">
          <estimated-ship-oil-freight-cost></estimated-ship-oil-freight-cost>
        </el-tab-pane>
      </el-tabs>
  </section>
</template>
<script>
import estimatedCostWuliu from './estimatedCostWuliu.vue';
import estimatedShipCost from './estimatedShipCost.vue';
import estimatedShipOilCost from './estimatedShipOilCost.vue';
import estimatedShipOilFreightCost from './estimatedShipOilFreightCost.vue';
export default{
  name: 'estimatedCost',
  components: { estimatedCostWuliu,estimatedShipCost,estimatedShipOilCost,estimatedShipOilFreightCost },
   data(){
    return {
      activeName:'wuliu'
    }
  },
}
</script>
<style scoped></style>
