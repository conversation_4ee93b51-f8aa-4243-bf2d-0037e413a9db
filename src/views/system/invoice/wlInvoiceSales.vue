<template>
  <div class="app-container">
    <div @keyup.enter="onQuery">
      <el-form
        :inline="true"
        :model="params"
        class="query-form demo-form-inline"
      >
        <el-form-item label="客户名称:">
          <el-input
            v-model="params.sellerName"
            placeholder="客户名称"
            clearable
          ></el-input>
        </el-form-item>
        <el-form-item label="船舶名称:">
          <el-input
            v-model="params.shipName"
            placeholder="船舶名称"
            clearable
          ></el-input>
        </el-form-item>
        <el-form-item label="费率:">
          <el-input
            v-model="params.invoiceTax"
            placeholder="费率"
            clearable
          ></el-input>
        </el-form-item>
        <!-- <el-form-item label="发票号:">
          <el-input
            v-model="params.invoiceNo"
            placeholder="发票号"
            clearable
          ></el-input>
        </el-form-item> -->
        <!-- <el-form-item label="挂账月">
          <el-date-picker
            v-model="params.billMonth"
            value-format="yyyy-MM"
            type="month"
            placeholder="选择月"
          >
          </el-date-picker>
        </el-form-item> -->
        <!-- <el-form-item label="标识">
          <el-radio-group v-model="params.type" size="small">
            <el-radio-button label="10">网联</el-radio-button>
            <el-radio-button label="20">汽运</el-radio-button>
          </el-radio-group>
        </el-form-item> -->
        <!-- <br/> -->
        <el-form-item>
          <el-button @click="onQuery" type="primary">查询</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div style="float:left;margin-bottom: 5px;">
      <!-- <el-button @click="batchDown" type="success" size="mini"
        >批量下载</el-button
      > -->
      <el-button style="float:right;" @click="toExcel" size="mini"
        >导出Excel</el-button
      >
      <!-- <el-popover
        placement="right"
        width="230"
        trigger="click"
        v-model="showBatchBill"
      >
        挂账月
        <el-date-picker
          v-model="billMonth"
          size="mini"
          style="width:130px;"
          value-format="yyyy-MM"
          clearable
          type="month"
          placeholder="选择月"
        >
        </el-date-picker>
        <el-button @click="showBatchBill = false">取消</el-button>
        <el-button @click="batchBill" type="primary">确认</el-button>
        <el-button slot="reference" type="primary" size="mini"
          >批量挂账</el-button
        >
      </el-popover> -->
    </div>
    <el-table
      :data="list"
      border
      stripe
      show-summary
      :summary-method="getSummaries"
      ref="table"
      @selection-change="handleSelectionChange"
      style="width: 100%"
    >
      <el-table-column
        align="center"
        type="selection"
        class-name="excel-hidden"
        :selectable="selectable"
        width="55"
      >
      </el-table-column>
        <el-table-column
        prop="sponsorTime"
        label="申请日期"
        width="65px"
        :formatter="formatDef"
        align="center"
      >
      </el-table-column>
      <el-table-column
        prop="invoiceDate"
        label="开票日期"
        width="65px"
        :formatter="formatDef"
        align="center"
      >
      </el-table-column>
      <el-table-column
        prop="invoiceNo"
        align="center"
        :formatter="formatDef"
        label="发票号码"
      >
      </el-table-column>
      <el-table-column
        prop="shipName"
        align="center"
        :formatter="formatDef"
        label="船名"
      >
      <template slot-scope="info">
        <div >
          {{ info.row.shipName||info.row.invoiceRemark }}
        </div>
      </template>
      </el-table-column>
      <el-table-column
        prop="buyerName"
        align="center"
        :formatter="formatDef"
        label="收货人"
      >
      </el-table-column>
      <el-table-column
        prop="sellerName"
        align="center"
        :formatter="formatDef"
        label="发货人"
      >
      </el-table-column>
      <el-table-column
        prop="customerName"
        align="center"
        :formatter="formatDef"
        label="客户名称"
      >
      </el-table-column>

      <el-table-column
        prop="goodsName"
        align="center"
        :formatter="formatDef"
        label="货名"
      >
      </el-table-column>
      <!-- <el-table-column
        prop="goodsWeight"
        align="center"
        :formatter="formatDef"
        label="吨数"
      >
      </el-table-column> -->
      <el-table-column
        prop="goodsPrice"
        align="center"
        :formatter="formatDef"
        label="运价"
      >
      </el-table-column>
      <el-table-column
        prop="invoicePriceTaxSum"
        align="center"
        :formatter="formatDefNo"
        label="金额"
      >
      </el-table-column>
      <el-table-column
        prop="invoicePriceTax"
        align="center"
        :formatter="formatDefNo"
        label="税额"
      >
      </el-table-column>
      <el-table-column
        prop="invoicePrice"
        align="center"
        :formatter="formatDefNo"
        label="不含税"
      >
      </el-table-column>
       <el-table-column
        prop="invoiceTax"
        align="center"
        label="不含税"
      >
      </el-table-column>
      <el-table-column label="操作" class-name="excel-hidden" align="center">
        <template slot-scope="scope">
<!--          <el-button-->
<!--            @click="invoiceShow(scope.row)"-->
<!--            :disabled="!scope.row.originFile"-->
<!--            type="text"-->
<!--            size="small"-->
<!--            >发票</el-button>-->

          <template v-for="(item,idx) in scope.row.originFiles">
            <el-button
              @click="invoiceShowNew(item.url)"
              :disabled="!scope.row.originFile"
              type="text"
              size="small"
            >{{ item.name }}{{ idx===0?'':idx }}
            </el-button>
          </template>
          <el-button type="text" size="small" @click="handleRowClick(scope.row)">详情</el-button>


          <!-- <el-popover placement="right" width="230" trigger="click">
            <el-date-picker
              v-model="billMonth"
              type="month"
              size="mini"
              value-format="yyyy-MM"
              style="width:130px;"
              clearable
              placeholder="选择月"
            >
            </el-date-picker>
            <el-button @click="billMonthAdd(scope.row.id)" type="primary"
              >确认</el-button
            >
            <el-button slot="reference">挂账</el-button>
          </el-popover> -->
          <!-- <el-button  @click="showDetail(scope.row)" type="text" size="small">详情</el-button> -->
          <!-- <el-button  @click="showOcrData(scope.row)" type="text" size="small">ocr识别</el-button> -->
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      background
      layout="prev, pager, next,sizes"
      :total="total"
      :page-size="params.pageSize"
      :current-page.sync="params.pageNum"
      :page-sizes="[10, 100, 200, 300, 500]"
      @size-change="handleSizeChange"
      @current-change="eventPage"
      style="text-align: right;padding: 10px 0px;background-color: #fff"
    >
    </el-pagination>
    <el-image-viewer
      :zIndex="9999"
      v-if="imgViewerVisible"
      :on-close="closeImgViewer"
      :url-list="imgList"
    />
    <el-drawer title="开票申请" size="80%" :visible.sync="detailDrawer" direction="rtl" :before-close="detailDrawerClose">
        <iframe :src="WULIU_BASE_URL+'/showProcess?processId=1700000441380634624'" style="width: 100%;height: 100%;" frameborder="0"></iframe>
    </el-drawer>
  </div>
</template>
<script>
// import { getProcessInstanceIdByBillOutApplyId } from '@/api/PaymentApply'
import {
  invoiceSalesApplyList,
  invoiceListBySalesAccount
} from "@/api/system/invoice";
import {WULIU_BASE_URL} from '@/utils/config'
// import {list,ocrData} from '@/api/invoiceOcrSales'
const ERR_OK = "0";
import { saveAs } from "file-saver";
import XLSXStyle from "xlsx-style";
import XLSX from "xlsx";
import axios from 'axios'
import currency from 'currency.js'
import JSZip from 'jszip'

export default {
  name: "wlInvoiceSales",
  components: {
    ElImageViewer: () => import("element-ui/packages/image/src/image-viewer")
  },
  data() {
    return {
      list: [],
      WULIU_BASE_URL:WULIU_BASE_URL,
      billMonth: "",
      imgViewerVisible: false,
      imgShow: false,
      showBatchBill: false,
      imgList: [],
      detailDrawer:false,
      params: {
        sellerName: "",
        shipName: "",
        invoiceNo: "",
        invoiceTax: "",
        billMonth: "",
        pageNum: 1,
        pageSize: 10,
        type: 10
      },
      multipleSelection: [],
      total: 0
    };
  },
  created() {
    this.loadList();
  },
  methods: {
    detailDrawerClose(){
      this.detailDrawer = false
    },
    handleRowClick(item){

        // this.$refs.processDrawer.processId = item.processInstanceId
        this.detailDrawer=true
    },
    getSummaries(param) {
      const { columns, data } = param;
      const sums = [];
      columns.forEach((column, index) => {
        if (index === 0) {
          sums[index] = "合计";
          return;
        }
        if (index != 14 && index != 12 && index != 13) {
          sums[index] = ""
          return
        }
        const values = data.map(item => Number(item[column.property]));
        if (!values.every(value => isNaN(value))) {
          sums[index] = values.reduce((prev, curr) => {
            const value = Number(curr);
            if (!isNaN(value)) {
              return prev + curr;
            } else {
              return prev;
            }
          }, 0);
          sums[index] = this.fmtMoney(sums[index]);
        } else {
          sums[index] = "";
        }
      });

      return sums;
    },
    billMonthAdd(id) {
      if (this.billMonth) {
        // loading
        const loading = this.$loading({
          lock: true,
          text: "Loading",
          spinner: "el-icon-loading",
          background: "rgba(0, 0, 0, 0.7)"
        });
        const data = {
          ids: id,
          billMonth: this.billMonth
        };
        invoiceListBySalesAccount(data)
          .then(res => {
            if (res.resultCode == ERR_OK) {
              this.$message.success("挂账成功");
              this.loadList();
            } else {
              this.$message.error(res.msg);
            }
          })
          .finally(() => {
            loading.close();
          });
      } else {
        this.$message.error("请选择挂账月份");
      }
    },
    batchBill() {
      if (this.multipleSelection && this.multipleSelection.length) {
        if (!this.billMonth) {
          this.$message.error("请选择挂账月份");
        }
        // loading
        const loading = this.$loading({
          lock: true,
          text: "Loading",
          spinner: "el-icon-loading",
          background: "rgba(0, 0, 0, 0.7)"
        });
        const data = {
          ids: this.multipleSelection.map(item => item.id).join(","),
          billMonth: this.billMonth
        };
        invoiceListBySalesAccount(data)
          .then(res => {
            if (res.resultCode == ERR_OK) {
              this.$message.success("挂账成功");
              this.loadList();
            } else {
              this.$message.error(res.msg);
            }
          })
          .finally(() => {
            loading.close();
          });
      } else {
        this.$message.error("请选择挂账数据");
      }
    },
    toExcel() {
      function parseElement(str) {
        var o = document.createElement("thead");
        o.innerHTML = str;
        return o.childNodes[0];
      }
      function lineHeight(sheet, skipFist = false, height = 30) {
        if (sheet["!rows"]) {
          sheet["!rows"] = [sheet["!rows"][0]];
        } else {
          sheet["!rows"] = [];
        }
        let range = XLSX.utils.decode_range(sheet["!ref"]);
        for (var i = skipFist ? 1 : 0; i <= range.e.r; i++) {
          sheet["!rows"].push({ hpt: height });
        }
      }
      function formatCell(worksheel, titleReg) {
        const borderAll = {
          //单元格外侧框线
          top: {
            style: "thin"
          },
          bottom: {
            style: "thin"
          },
          left: {
            style: "thin"
          },
          right: {
            style: "thin"
          }
        };

        let range = XLSX.utils.decode_range(worksheel["!ref"]);

        for (let C = range.s.c; C <= range.e.c; ++C) {
          for (let R = range.s.r; R <= range.e.r; ++R) {
            let cell = { c: C, r: R };
            let i = XLSX.utils.encode_cell(cell);

            if (i == "!ref" || i == "!cols" || i == "!rows") {
            } else {

              if (!worksheel[i]) {
                worksheel[i] = { t: "s", v: "" };
              }
              if (titleReg && titleReg.test(i)) {
                worksheel[i].s = {
                  // border: borderAll,
                  font: {
                    name: "宋体",
                    sz: 12,
                    color: { rgb: "000000" },
                    bold: false,
                    italic: false,
                    underline: false
                  },
                  alignment: {
                    horizontal: "left",
                    vertical: "center",
                    wrapText: true
                  }
                };
              } else {
                worksheel[i].s = {
                  border: borderAll,
                  font: {
                    name: "宋体",
                    sz: 12,
                    color: { rgb: "000000" },
                    bold: false,
                    italic: false,
                    underline: false
                  },
                  alignment: {
                    horizontal: "center",
                    vertical: "center",
                    wrapText: true
                  }
                };
              }

              // if (/^(-\d+)(\.?\d+)?$/g.test(worksheel[i].v + "")) {
              //   console.log('492',worksheel[i])
              //    worksheel[i].t = "n";
              //  // worksheel[i].t= `@`
              // }
            }
          }
          // //给所以单元格加上边框
          // for (var i in worksheel) {
          //     if (i == '!ref' || i == '!cols' || i == '!rows') {
          //     } else if (i == 'A1') {
          //       worksheel[i + ''].s = {
          //         // border: borderAll,
          //         font: {
          //           name: '宋体',
          //           sz: 12,
          //           color: {rgb: "000000"},
          //           bold: false,
          //           italic: false,
          //           underline: false
          //         },
          //         alignment: {
          //           horizontal: "left",
          //           vertical: "center",
          //           wrapText: true
          //         }

          //       }
          //     } else {

          //        worksheel[i + ''].s = {
          //         // border: borderAll,
          //         font: {
          //           name: '宋体',
          //           sz: 12,
          //           color: {rgb: "000000"},
          //           bold: false,
          //           italic: false,
          //           underline: false
          //         },
          //         alignment: {
          //           horizontal: "center",
          //           vertical: "center"
          //         }

          //       }
          //     }
        }
      }
      function s2ab(s) {
        if (typeof ArrayBuffer !== "undefined") {
          const buf = new ArrayBuffer(s.length);
          const view = new Uint8Array(buf);
          for (let i = 0; i !== s.length; ++i) {
            view[i] = s.charCodeAt(i) & 0xff;
          }
          return buf;
        } else {
          const buf = new Array(s.length);
          for (let i = 0; i !== s.length; ++i) {
            buf[i] = s.charCodeAt(i) & 0xff;
          }
          return buf;
        }
      }

      var workbook = XLSX.utils.book_new();

      // var table = document.querySelector('.excel-main table.el-table__body').cloneNode(true);
      var table = this.$refs.table.$el
        .querySelector("table.el-table__body")
        .cloneNode(true);
      // var thead = document.querySelector('.excel-main table.el-table__header thead').cloneNode(true);
      var thead = this.$refs.table.$el
        .querySelector("table.el-table__header thead")
        .cloneNode(true);
      // var footer = document.querySelector('.excel-main table.vxe-table--footer tfoot').cloneNode(true);

      while (thead.querySelector("th.gutter")) {
        thead.querySelector("th.gutter").remove();
      }

      var cc = 0;
      thead.querySelectorAll("th[colspan]").forEach(element => {
        // console.log(element)
        cc += parseInt(element.getAttribute("colspan"));
      });
      cc +=
        thead.querySelector("tr").querySelectorAll("th").length -
        thead.querySelectorAll("th[colspan]").length;

      while (thead.querySelector("th.excel-hidden")) {
        thead.querySelector("th.excel-hidden").remove();
      }
      while (table.querySelector("td.excel-hidden")) {
        table.querySelector("td.excel-hidden").remove();
      }
      // let headContentStr=`销项发票明细表`
      // // var headContentStr = document.getElementById('peizaidantitle').innerHTML + document.getElementById('peizaidan').innerHTML;
      // // headContentStr = headContentStr.replace(/(\<\/span\>)(\<span\s+)/g, '$1&nbsp;&nbsp;&nbsp;$2').replace(/(\<\/div\>)(\<div\s+)/g, '$1<br/>$2')
      // var headContent = parseElement(`<tr><th colspan='${cc}'>${headContentStr}</th></tr>`);
      // thead.insertBefore(headContent, thead.querySelector('tr'));
      table.insertBefore(thead, table.querySelector("tbody"));
      console.log('tabledata',table)
      // table.appendChild(footer);
      var sheet = XLSX.utils.table_to_sheet(table, { raw: true });
      // document.getElementById('pzd-title').innerText.replace(/\//g, '_')
      XLSX.utils.book_append_sheet(workbook, sheet, "销项发票");
      formatCell(sheet, /^[A-Z]+1$/g);

      // sheet['!rows'] = [{hpx: 250}]
      // console.log(sheet)

      sheet["!rows"] = [{ hpt: 65 }];

      lineHeight(sheet, true);

      // workbook.finalize();
      var wbOut = XLSXStyle.write(workbook, {
        bookType: "xlsx",
        bookSST: false,
        type: "binary"
      });

      saveAs(
        new Blob([s2ab(wbOut)], { type: "application/octet-stream" }),
        `销项发票明细表.xlsx`
      );
    },
    closeImgViewer() {
      this.imgViewerVisible = false;
    },
    selectable(row) {
      return row.originFile;
    },
    eventPage() {
      this.loadList();
    },
    handleSizeChange(v) {
      this.params.pageSize = v;
      this.loadList();
    },
    invoiceShow(item) {
      this.imgList = item.originFile ? item.originFile.split(",") : [];
      if (this.imgList.length == 0) {
        this.$message({
          message: "暂无发票",
          type: "warning"
        });
        return;
      }
      this.imgViewerVisible = true;
      // this.imgShow = true
    },
    invoiceShowNew(url) {
      // this.imgList = item.originFile ? item.originFile.split(",") : [];
      // if (this.imgList.length == 0) {
      //   this.$message({
      //     message: "暂无发票",
      //     type: "warning"
      //   });
      //   return;
      // }
      // this.imgViewerVisible = true;
      // this.imgShow = true

      if (
        url.indexOf(".jpg") > -1 ||
        url.indexOf(".png") > -1 ||
        url.indexOf(".jpeg") > -1
      ) {
        this.imgList = url ? [url] : [];
        if (this.imgList.length == 0) {
          this.$message({
            message: "暂无发票",
            type: "warning"
          });
          return;
        }
        this.imgViewerVisible = true;
      } else {
        window.open(url);
      }
    },
    splitByStr(str,sp=','){
      return str.split(sp)
    },
    arrByNex(urls){
      const url_list = [];
      for (let k = 0; k < urls.length; k++) {
        // let index_text = '';
        // if(k != 0){
        //   index_text = k
        // }
        url_list.push({
          name:'发票',
          url:urls[k],
        })
      }
      return url_list
    },
    arrBySpl(str){
      const arr = this.splitByStr(str,';')
      const url_list = []
      for(let i=0;i<arr.length;i++){
        url_list.push(...this.arrByNex(this.splitByStr(arr[i],',')))
      }
      return url_list
    },
    loadList() {
      this.params['type'] = this.params['type']+'';
      const data = {
        ...this.params
      };
      invoiceSalesApplyList(data).then(res => {
        if (
          res &&
          res.hasOwnProperty("resultCode") &&
          res.resultCode === ERR_OK
        ) {
          this.list = res.data;
          this.total = res.total;
          for (var i = 0; i < this.list.length; i++) {
            let info = this.list[i];
            if (info.originFile) {
              // let urls = info.originFile.split(',')
              // let url_list = [];
              // for (var k = 0; k < urls.length; k++) {
              //   let index_text = '';
              //   if(k != 0){
              //     index_text = k
              //   }
              //   url_list.push({
              //     name:'发票'+index_text,
              //     url:urls[k],
              //   })
              // }

              this.list[i].originFiles = this.arrBySpl(info.originFile);
            }else {
              this.list[i].originFiles =[];
            }
          }
          console.log("this.list=====>",this.list)
        }
      });
    },
    onQuery() {
      this.params.pageNum = 1;
      this.loadList();
    },
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    downloadFile(url, urlName) {
      axios({
        method: 'get',
        url: url,
        responseType: 'blob'
      }).then((res) => {
        const content = res.data
        const blob = new Blob([content])
        saveAs(blob, urlName)
      }).catch(() => {
        this.$message({
          message: '下载失败',
          type: 'error'
        })
      })
    },
    getFile(url) {
      // return axios.get(url, {responseType:'blob'})
      return new Promise((resolve, reject) => {
        axios.get(url, { responseType: 'arraybuffer' }).then(res => {
          resolve(res.data)
        }).catch(err => {
          this.$message({
            message: '下载失败',
            type: 'warning'
          });
          reject(err)
        })
      })
    },
    batchDown() {
      if(this.multipleSelection.length==0){
        this.$message({
          message: '请选择要下载的数据',
          type: 'warning'
        });
        return
      }
      const loading = this.$loading({
        lock: true,
        text: '下载中...',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      });
      // 关键字 url 为合同附件地址, goodsCost 和 goodsCostOut 为goodsCostContractUrls 的值
      const keyUrl =  'originFile'
      // 如果只有一个合同，直接下载
      if (this.multipleSelection.length == 1) {
        if (this.multipleSelection[0][keyUrl]) {
          let urlSplit = this.multipleSelection[0][keyUrl].split(',')
          for(let i=0;i<urlSplit.length;i++){
            let url = urlSplit[i]
            let urlName = (this.multipleSelection[0].customerName||this.multipleSelection[0].shipName||this.multipleSelection[0].invoiceNo)+'-'+this.multipleSelection[i].invoicePriceTaxSum+'-'+(i+1)+'.'+url.split('.')[url.split('.').length-1]
            this.downloadFile(url, urlName)
          }
          loading.close()
        } else {
          loading.close()
          this.$message({
            message: '该数据没有附件',
            type: 'warning'
          });
        }
        return
      }
      let urls = []
      let urlNames = []
      let imgBase64 = [];
      let imageSuffix = [];//图片后缀
      let urlToMulti = [] // 多个url与multipleSelection的对应关系
      let zip = new JSZip();
      // zip.file("readme.txt", "案件详情资料\n");
      // var img = zip.folder("images");
      for (let i = 0; i < this.multipleSelection.length; i++) {
        if (this.multipleSelection[i][keyUrl]) {
          this.multipleSelection[i][keyUrl].split(',').forEach((item, index) => {
            urls.push(item)
            urlToMulti.push(i + 1)
            // 货物流向 客户/离港日期
            // 货物费用 供应商 吨位
            // if (this.searchForm.contractType == 'goodsCostOut') {
            //   urlNames.push((this.multipleSelection[i].abbName + ' ' + this.multipleSelection[i].tonnage) + '-' + (i + 1))
            // } else if (this.searchForm.contractType == 'goodsCost') {
            //   urlNames.push((this.multipleSelection[i].twoName + ' ' + this.multipleSelection[i].tonnage + ' ' + this.defStr(this.multipleSelection[i].departureTimeDate)) + '-' + (i + 1))
            // } else {
              urlNames.push((this.multipleSelection[i].customerName||this.multipleSelection[i].shipName||this.multipleSelection[i].goodsWeight)+'-'+this.multipleSelection[i].invoicePriceTaxSum+ '-' + (i + 1))
            // }

          })
        }
      }
      // loading 文字修改 0/urls.length
      for (let i = 0; i < urls.length; i++){
        // console.log('urls[i]',urls[i])
        let imgName = urls[i].substring(urls[i].lastIndexOf('/') + 1, urls[i].length);
        let suffix = imgName.substring(imgName.lastIndexOf('.') + 1, imgName.length);
        imageSuffix.push(suffix);
        this.getFile(urls[i]).then(res => {
          imgBase64.push('1');
          zip.file(urlNames[i]+'-'+(i+1)+'.'+suffix, res,{binary:true});
          loading.text = `下载中 ${urlToMulti[i]}/${this.multipleSelection.length}`
        })
      }
      function tt() {
        if (imgBase64.length === urls.length) {
          zip.generateAsync({
            type: "blob",
            // type:"arraybuffer",
            // compression: "DEFLATE", // STORE：默认不压缩 DEFLATE：需要压缩
            // compressionOptions: {
            //   level: 9               // 压缩等级1~9    1压缩速度最快，9最优压缩方式
            // }
          }).then(function (content) {
            // yyyy-MM-dd HH:mm:ss
            let date = new Date()
            saveAs(content, `发票${date.toLocaleString()}.zip`);
          });
          loading.close();
        } else {
          setTimeout(() => {
            tt()
          }, 100)
        }
      }
      tt()
    },
    // batchDown() {
    //   if (this.multipleSelection && this.multipleSelection.length) {
    //     this.multipleSelection.forEach(item => {
    //       if (item.originFile) {
    //         item.originFile.split(",").forEach(subt => {
    //           this.downByUrl(subt);
    //         });
    //       }
    //     });
    //   } else {
    //     this.$message.info("请选择下载的发票");
    //   }
    // },
    downByUrl(url) {
      // 根据url 批量下载文件
      // const a = document.createElement('a')
      // a.href = url
      // a.download = ''
      // a.click()

      let xhr = new XMLHttpRequest();
      xhr.open("GET", url, true);
      xhr.responseType = "blob";
      xhr.onload = function() {
        if (this.status === 200) {
          let blob = this.response;
          saveAs(blob, url.split("/").pop());
          // let reader = new FileReader()
          // reader.readAsDataURL(blob)
          // reader.onload = function (e) {
          //   let a = document.createElement('a')
          //   a.download = ''
          //   a.href = e.target.result
          //   a.click()

          // }
        }
      };
      xhr.send();
    },
    imgClose() {
      this.imgShow = false;
    },
    formatDef(row, column, cellValue, index) {
      if (cellValue) {
        return cellValue + "";
      }
      return "--";
    },
    formatDefNo(row, column, cellValue, index) {
      if (cellValue) {
        if(cellValue.indexOf(';')>-1){
          let str = ''
          cellValue.split(';').forEach(item => {
            str += this.fmtMoney(item) + ';'
          })
          return str.slice(0, -1)
        }
        return this.fmtMoney(cellValue);
      }
      return "--";
    },

    formatDefShuiE(row, column, cellValue, index) {
      if (row.goodsWeight) {
        let val = currency(row.invoicePriceTaxSum).divide(row.goodsWeight).value
        return this.fmtMoney(val);
      }
      return "--";
    },

    fmtMoney(v) {
      if (!v && v != 0) {
        return "--";
      } else {
        return currency(v, { symbol: "" }).format();
      }
    }
  }
};
</script>
<style>
.no-num{
  mso-number-format:'\@';
}
</style>
