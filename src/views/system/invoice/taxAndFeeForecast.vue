<template>
  <section>
    <el-tabs v-model="activeName" type="card">
      <el-tab-pane label="税费预测合计" name="first"><total-tax-and-fee-forecast></total-tax-and-fee-forecast></el-tab-pane>
      <el-tab-pane label="已开发票明细" name="second"><received-invoice-details></received-invoice-details></el-tab-pane>
      <el-tab-pane label="实收发票明细" name="third"><invoice-details></invoice-details></el-tab-pane>
    </el-tabs>
  </section>
</template>
<script>
import receivedInvoiceDetails from './taxAndFee/receivedInvoiceDetails.vue'
import invoiceDetails from './taxAndFee/invoiceDetails.vue'
import totalTaxAndFeeForecast from './taxAndFee/totalTaxAndFeeForecast.vue'
export default{
  components: { receivedInvoiceDetails,invoiceDetails,totalTaxAndFeeForecast },
  data(){
    return {
      activeName:'first'
    }
  },
}
</script>
<style scoped></style>
