import {getAllCusTomer, getAllSysSupplier, getAllUser} from "../../api/system/baseInit";
import {getAllCompanyAccount} from "../../api/business/wxDepartmentapi";
import {getExternalAccount} from "../../api/business/sysExternalAccount";
import {getDicts} from "../../api/system/dict";
import {getHaokjSub} from '@/api/business/processapi'
const state = {
  supplierList: [],
  supplierListHs:[],
  customerList: [],
  customerListHs:[],
  fdbizCustomerList: [],

  allUser: [],
  allHsUser:[],
  danzhengList: [],
  hsDanzhengList:[],
  hsYewuList: [],
  yewuList:[],
  companyList: [],

  plateList: [],

  fcSelId:'',

  hkjClass: {},
  taxationCertificateMonth: '',
  taxationCertificateMonthSales: '',
}

const mutations = {
  setTaxationCertificateMonthSales(state,month){
    state.taxationCertificateMonthSales = month
  },
  setTaxationCertificateMonth(state,month){
    state.taxationCertificateMonth = month
  },
  setHkjClass(state,key,list) {
    state.hkjClass[key] = list
  },
  setSupplierList(state,supplierList){
    state.supplierList = supplierList
  },
  setSupplierListHs(state, supplierList) {
    state.supplierListHs = supplierList
  },
  setCustomerList(state,customerList){
    state.customerList = customerList
  },
  setCustomerListHs(state, customerList) {
    state.customerListHs = customerList
  },
  setFdbizCustomerList(state,fdbizCustomerList){
    state.fdbizCustomerList = fdbizCustomerList
  },

  setPlateList(state,plateList){
    state.plateList = plateList
  },

  setAllUser(state,allUser){
    state.allUser = allUser
  },
  setAllHsUser(state,allUser){
    state.allHsUser = allUser
  },
  setHsDanzhengList(state,danzhengList){
    state.hsDanzhengList = danzhengList
  },
  setHsYewuList(state,yewuList){
    state.hsYewuList = yewuList
  },
  setCompanyList(state,companyList){
    state.companyList = companyList
  },

  setFcSelId(state, fcSelId) {
    state.fcSelId = fcSelId
  },
}

const actions = {
  getHkjClassListByKey({state,commit},key){
    return new Promise((resolve,reject)=>{
      if (state.hkjClass[key] != null && state.hkjClass[key].length > 0){
        resolve({hkjClassList:state.hkjClass[key]})
      }else {
        getHaokjSub(key).then(res => {
          if (res!= undefined) {
            commit("setHkjClass", key, res.data)
            resolve({hkjClassList:state.hkjClass[key]})
            }else{
            reject(res.resultMsg || '获取失败')
          }
        }).catch(error => {
          reject(error)
        })
      }
    })
  },
  getSupplierListSaveInVuex({state,commit}){
    return new Promise((resolve,reject)=>{
      if (state.supplierList != null && state.supplierList.length > 0){
        resolve({supplierList:state.supplierList})
      }else {
        getAllSysSupplier().then(res => {
          if (res!= undefined) {
            commit("setSupplierList", res.list)
            resolve({supplierList:state.supplierList})
          }else{
            reject(res.resultMsg || '获取失败')
          }
        }).catch(error => {
          reject(error)
        })
      }
    })
  },
  getSupplierListSaveInVuexHs({ state, commit }) {
    return new Promise((resolve, reject) => {
      if (state.supplierListHs != null && state.supplierListHs.length > 0) {
        resolve({ supplierList: state.supplierListHs })
      } else {
        getAllSysSupplier("HNHS").then(res => {
          if (res != undefined) {
            commit("setSupplierListHs", res.list)
            resolve({ supplierList: state.supplierListHs })
          } else {
            reject(res.resultMsg || '获取失败')
          }
        }).catch(error => {
          reject(error)
        })
      }
    })
  },
  getCustomerListSaveInVuex({state,commit}){
    return new Promise((resolve,reject)=>{
      if (state.customerList != null && state.customerList.length > 0){
        resolve({customerList:state.customerList})
      }else {
        getAllCusTomer().then(res => {
          if (res!= undefined) {
            commit("setCustomerList", res.list)
            resolve({customerList:state.customerList})
          }else{
            reject(res.resultMsg || '获取失败')
          }
        }).catch(error => {
          reject(error)
        })
      }
    })
  },
  getCustomerListSaveInVuexHs({ state, commit }) {
    return new Promise((resolve, reject) => {
      if (state.customerListHs != null && state.customerListHs.length > 0) {
        resolve({ customerList: state.customerListHs })
      } else {
        getAllCusTomer("HNHS").then(res => {
          if (res != undefined) {
            commit("setCustomerListHs", res.list)
            resolve({ customerList: state.customerListHs })
          } else {
            reject(res.resultMsg || '获取失败')
          }
        }).catch(error => {
          reject(error)
        })
      }
    })
  },
  getFdbizCustomerListSaveInVuex({state,commit}){
    return new Promise((resolve,reject)=>{
      if (state.fdbizCustomerList != null && state.fdbizCustomerList.length > 0){
        resolve({fdbizCustomerList:state.fdbizCustomerList})
      }else {
        getExternalAccount().then(res => {
          if (res!= undefined) {
            commit("setFdbizCustomerList", res.data)
            resolve({fdbizCustomerList:state.fdbizCustomerList})
          }else{
            reject(res.resultMsg || '获取失败')
          }
        }).catch(error => {
          reject(error)
        })
      }
    })
  },
  getPlateListSaveInVuex: function ({state, commit}) {
    return new Promise((resolve, reject) => {
      if (state.plateList != null && state.plateList.length > 0) {
        resolve({plateList: state.plateList})
      } else {
        getDicts().then(res => {
          if (res != undefined) {
            var banlist = []
            for(var a = 0; a < res.length;a++){
              var item = res[a]
              if (item.name === 'plate'){
                banlist.push(item)
              }
            }
            commit("setPlateList", banlist)
            resolve({plateList: state.plateList})
          } else {
            reject(res.resultMsg || '获取失败')
          }
        }).catch(error => {
          reject(error)
        })
      }
    })
  },
  getAllUserListSaveInVuex({state,commit}){
    return new Promise((resolve,reject)=>{
      if (state.allUser != null && state.allUser.length > 0){
        resolve({allUser:state.allUser,danzheng:state.danzhengList,yewu:state.yewuList})
      }else {
        getAllUser().then(res => {
          if (res) {
            commit("setAllUser", res.list)
            commit("setDanzhengList", res.danzhengList)
            commit("setYewuList", res.listyewu)
            resolve({allUser:state.allUser,danzheng:state.danzhengList,yewu:state.yewuList})
          }else{
            reject(res.resultMsg || '获取失败')
          }
        }).catch(error => {
          reject(error)
        })
      }
    })
  },
  getAllUserListSaveInVuexByHs({state,commit}){
    return new Promise((resolve,reject)=>{
      if (state.allHsUser != null && state.allHsUser.length > 0){
        resolve({allUser:state.allHsUser,danzheng:state.hsDanzhengList,yewu:state.hsYewuList})
      }else {
        getAllUser("HNHS").then(res => {
          if (res) {
            commit("setAllHsUser", res.list)
            commit("setHsDanzhengList", res.danzhengList)
            commit("setHsYewuList", res.listyewu)
            resolve({allUser:state.allHsUser,danzheng:state.hsDanzhengList,yewu:state.hsYewuList})
          }else{
            reject(res.resultMsg || '获取失败')
          }
        }).catch(error => {
          reject(error)
        })
      }
    })
  },
  getCompanyListSaveInVuex({state,commit}){
    return new Promise((resolve,reject)=>{
      if (state.companyList != null && state.companyList.length > 0){
        resolve({companyList:state.companyList})
      }else {
        getAllCompanyAccount().then(res => {
          if (res!= undefined) {
            commit("setCompanyList", res.list)
            resolve({companyList:state.companyList})
          }else{
            reject(res.resultMsg || '获取失败')
          }
        }).catch(error => {
          reject(error)
        })
      }
    })
  },
}

export default {
  namespaced: true,
  state,
  mutations,
  actions
}
