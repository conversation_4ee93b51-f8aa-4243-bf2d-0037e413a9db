const getters = {
  deployUploadApi: state => state.api.deployUploadApi,
  databaseUploadApi: state => state.api.databaseUploadApi,
  size: state => state.app.size,
  sidebar: state => state.app.sidebar,
  device: state => state.app.device,
  token: state => state.user.token,
  visitedViews: state => state.tagsView.visitedViews,
  cachedViews: state => state.tagsView.cachedViews,
  roles: state => state.user.roles,
  user: state => state.user.user,
  loadMenus: state => state.user.loadMenus,
  permission_routers: state => state.permission.routers,
  addRouters: state => state.permission.addRouters,
  socketApi: state => state.api.socketApi,
  imagesUploadApi: state => state.api.imagesUploadApi,
  baseApi: state => state.api.baseApi,
  fileUploadApi: state => state.api.fileUploadApi,
  updateAvatarApi: state => state.api.updateAvatarApi,
  qiNiuUploadApi: state => state.api.qiNiuUploadApi,
  sqlApi: state => state.api.sqlApi,
  swaggerApi: state => state.api.swaggerApi,
  sidebarRouters: state => state.permission.sidebarRouters,
  fdbizHost:state=> state.settings.fdbizHost,
  noPageHost:state=> state.settings.noPageHost,
  fdbizCode:state=> state.settings.fdbizCode,
  noPageCode:state=> state.settings.noPageCode,
  supplierList:state=> state.data.supplierList,
  customerList:state=> state.data.customerList,
  fdbizCustomerList:state=> state.data.fdbizCustomerList,
  plateList:state=> state.data.plateList,
  allUser:state=> state.data.allUser,
  danzhengList:state=> state.data.danzhengList,
  yewuList:state=> state.data.yewuList,
  companyList: state => state.data.companyList,
  fcSelId: state => state.data.fcSelId,
  taxationCertificateMonth: state => state.data.taxationCertificateMonth,
  taxationCertificateMonthSales: state => state.data.taxationCertificateMonthSales,
}
export default getters
