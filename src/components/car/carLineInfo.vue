<template>
  <section>
    <el-dialog
      :title="ttle"
      :visible.sync="show"
      width="94%"
      :before-close="handleClose"
      :close-on-click-modal="false"
    >
      <div style="width: 100%" class="base">
        <div style="text-align: right;padding-right: 20px;margin-bottom: 10px">
          <el-button @click="printApply" type="primary" icon="el-icon-printer" size="mini">打印</el-button>
        </div>
        <div id="dataform">

          <div class="titile">汽运公司结算单</div>
          <div class="ship">
            <div>挂账号 : {{ publicFmt(car_line_info.onAccountNum) }}</div>
            <div>进港日期: {{ fmtDateTime(car_line_info.shipIntoTime, 'M月DD日') }}</div>
            <div>出港日期 : {{ fmtDateTime(car_line_info.shipOutTime, 'M月DD日') }}</div>
            <div>{{ car_line_info.shipName ? '船名 :' + publicFmt(car_line_info.shipName) : "汽运直取" }}</div>
          </div>
          <div>
            <table>
              <thead>
              <tr style="font-weight: bold;font-size: 14px">
                <td>起运地</td>
                <td>目的地</td>
                <td>车牌号</td>
                <td>车次</td>
                <td>货名</td>
                <td>订货方</td>
                <td>客户</td>
                <td>货量</td>
                <td>收入单价</td>
                <td>收入金额</td>
                <td>发票</td>
                <td>运输公司</td>
                <td>成本单价</td>
                <td>运输支出金额</td>
                <td>发票</td>
                <td v-if="cost_name">{{ cost_name }}</td>
                <td v-if="cost_name">发票</td>
                <td>汽运公司利润</td>
                <td>业务员</td>
              </tr>
              </thead>
              <tbody>
              <template v-for="item in tabedata">
                <tr>
                  <td :rowspan="item.shouruPriceB?'2':''">{{ publicFmt(item.loadAddress) }}</td>
                  <td :rowspan="item.shouruPriceB?'2':''">{{ publicFmt(item.unloadAddress) }}</td>
                  <td :rowspan="item.shouruPriceB?'2':''">{{ publicFmt(item.carNumber) }}</td>
                  <td :rowspan="item.shouruPriceB?'2':''">{{ publicFmt(item.carNo) }}</td>
                  <td :rowspan="item.shouruPriceB?'2':''">{{ publicFmt(item.goodsName) }}</td>
                  <td :rowspan="item.shouruPriceB?'2':''">{{ publicFmt(item.dinghuofang) }}</td>
                  <td :rowspan="item.shouruPriceB && !item.kehuB ?'2':''" v-if="!item.kehuB">{{
                      publicFmt(item.kehuA)
                    }}
                  </td>
                  <td v-if="item.kehuB">{{ publicFmt(item.kehuA) }}</td>
                  <td :rowspan="item.shouruPriceB?'2':''">{{ publicFmt(item.dunshu) }}</td>
                  <td>{{ publicFmt(item.shouruPriceA) }}</td>
                  <td>{{ publicFmt(item.shouruMoneyA) }}</td>
                  <td>{{ publicFmt(item.shouruBillA) }}</td>
                  <td :rowspan="item.shouruPriceB?'2':''">{{ publicFmt(item.shouruYunshuA) }}</td>
                  <td>{{ publicFmt(item.chengbenPriceA) }}</td>
                  <td>{{ publicFmt(item.chengbenMoneyA) }}</td>
                  <td>{{ publicFmt(item.chengbenBillA) }}</td>
                  <td v-if="cost_name">{{ publicFmt(item.costMoneyA) }}</td>
                  <td v-if="cost_name">{{ publicFmt(item.costBillA) }}</td>
                  <td>{{ item.liruiMoneyA }}</td>
                  <td>{{ publicFmt(item.yewuName) }}</td>
                </tr>
                <template v-if="item.shouruPriceB">
                  <tr>
                    <td v-if="item.kehuB">{{ publicFmt(item.kehuB) }}</td>
                    <td>{{ publicFmt(item.shouruPriceB) }}</td>
                    <td>{{ publicFmt(item.shouruMoneyB) }}</td>
                    <td>{{ publicFmt(item.shouruBillB) }}</td>
                    <td v-if="!item.shouruPriceB"></td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td v-if="cost_name"></td>
                    <td v-if="cost_name"></td>
                    <td>{{ item.liruiMoneyB }}</td>
                    <td></td>
                  </tr>
                </template>

              </template>

              <tr style="font-weight: bold">
                <td colspan="3">合计</td>
                <td>{{ sum_data.sum_checi }}</td>
                <td></td>
                <td></td>
                <td></td>
                <td>{{ sum_data.sum_dunshu }}</td>
                <td></td>
                <td>{{ sum_data.sum_income_money }}</td>
                <td></td>
                <td></td>
                <td></td>
                <td>{{ sum_data.sum_cost_money }}</td>
                <td></td>
                <td v-if="cost_name">{{ sum_data.sum_cost }}</td>
                <td v-if="cost_name"></td>
                <td>{{ sum_data.sum_automobile_money }}</td>
                <td></td>
              </tr>
              <tr>
                <td colspan="2">备注</td>
                <td colspan="17" style="text-align: left;padding-left: 20px">{{
                    publicFmt(car_line_info.auditRemarks)
                  }}
                </td>
              </tr>

              </tbody>

            </table>
          </div>
          <div class="audit" v-if="false">
            <div>制单:</div>
            <div>部门负责人：</div>
            <div>商务：</div>
            <div>运营：</div>
            <div>财务1：</div>
            <div>财务2:</div>
          </div>
        </div>
      </div>
      <div style="width: 100%;text-align: right;margin-top: 20px">
        <el-button v-if="false" type="primary" size="mini">挂 账</el-button>
        <el-button type="primary" size="mini" @click="handleClose">确 定</el-button>
      </div>
    </el-dialog>
  </section>
</template>
<script>

import {
  getCarLineIntegrationInfo
} from '@/api/system/onAccount.js'
import dayjs from 'dayjs'
const ERR_OK = '0'
export default {
  name: 'CarOnAccountApprove',
  props: {
    datas: {
      type: Object,
      default: () => ({})
    },
    carLineId: {
      type: String,
      default: ''
    },
    onAccountNum: {
      type: String,
      default: ''
    },
    guazhangNum: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      ttle: '',
      car_line_info: {},
      tabedata: {},
      sum_data: {},
      info: {},
      show: false,
      cost_name: null
    }
  },
  created() {

  },
  mounted() {

  },
  methods: {
    showView(car_line_id) {
      this.show = true
      this.getCarLineIntegrationInfo(car_line_id)
    },
    getCarLineIntegrationInfo(car_line_id) {
      if (this.shipLineId) {
        car_line_id = this.shipLineId
      }
      getCarLineIntegrationInfo({ 'car_line_id': car_line_id }).then(res => {
        if (res !== undefined && res.resultCode === ERR_OK) {
          this.car_line_info = res.car_line_info
          this.tabedata = res.tabedata
          this.sum_data = res.sum_data
          this.cost_name = res.cost_name
        }
      })
    },
    handleClose() {
      this.info = {}
      this.show = false
    },
    fmtDateTime(cellValue, fmtstr) {
      if (cellValue === undefined || cellValue == null || cellValue === '') {
        return '--'
      }
      return dayjs(cellValue).format(fmtstr)
    },
    publicFmt(cellValue, postfix) {
      var v = '--'
      if (cellValue !== undefined && cellValue != null && cellValue !== '') {
        v = cellValue
      } else {
        return v
      }
      console.log('v====>', v)
      var p = ''
      if (postfix) {
        p = postfix
      }
      return v + p
    },
    printApply() {
      var str = window.document.getElementById('dataform').innerHTML
      window.document.body.style.background = 'white'
      window.document.body.innerHTML = str
      window.print()
      // this.preview(1)
      window.location.reload()
    }
  }
}
</script>
<style scoped>
.sp_add_left {
  width: 45%;
  margin-right: 5%;
}

.sp_add_right {
  width: 45%;
}

.dialog-footer {
  margin-right: 5%;
}

.myform .myclass .el-input {
  width: 100%;
}

.chuandongjiesuan >>> .el-form-item__label {
  position: relative;
  right: 10px;
  white-space: nowrap;
}
table {
  width: 100%;
  margin-top: 15px;
}
table, td {
  border: 1px solid #000000;
  border-collapse: collapse;
  height: 34px;
  font-size: 12px;
  text-align: center;
}
.base{
  color: black;
}

.titile{
  width: 100%;
  text-align: center;
  font-size: 26px;
  font-weight: bold;

}
.ship{
  display: flex;
  justify-content: space-between;
  font-size: 14px;
  margin-top: 15px;
}
.audit{
  margin-top: 15px;
  display: flex;
  justify-content: space-between;
  font-size: 14px;
}

</style>
