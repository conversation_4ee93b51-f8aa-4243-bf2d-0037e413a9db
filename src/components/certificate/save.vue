<template>
<el-dialog
  :visible.sync="dialogYacc"
  title="生成凭证"
  width="80%"
  :before-close="handleYacc"
>
  <span>&nbsp;&nbsp;账期:&nbsp;<el-date-picker
    v-model="monthDateAcc"
    type="month"
    value-format="yyyyMM"
    placeholder="选择日期">
  </el-date-picker>
  </span>
  <Subacc :tableData.sync="tableDataAcc"   :accounts="ledgerAccountList" @ledgerAccount="v=> ledgerAccount = v"  :classList="kjClassList"></Subacc>
  <template #footer>
    <div class="dialog-footer">
      <el-button @click="dialogYacc = false">取消</el-button>
      <el-button type="primary" @click="saveYacc()">
        提交
      </el-button>
    </div>
  </template>
</el-dialog>
</template>
<script>
import {getAcctgTransListByCode, getHaokjSub,getInvoiceVocherListByCode,getIsCodeByPidAndCode,saveVoucher,getInvoiceVocherListByData} from '@/api/business/processapi'
import Subacc from '@/views/system/businessPayment/subacc.vue'
import dayjs from 'dayjs'
export default {
  name: 'CertificateSave',
  components: {Subacc},
  computed: {
  },
  data() {
    return {
      dialogYacc: false,
      monthDateAcc: dayjs().format('YYYYMM'),
      kjClassList: [],
      tableDataAcc: [],
      ledgerAccount: '',
      ledgerAccountList: [],
      code:''
    }
  },
  created() {
    this.loadKeMuList()
  },
  methods: {
    open(id,code='',dataType='',isJson=false) {
      this.code = code
      this.loadAccTable(id, code,dataType,isJson, () => {
        this.dialogYacc = true
      })
    },
    loadKeMuList() {
      getHaokjSub().then(res => {
        if (res && res.data) {
          this.kjClassList = res.data || []
        }
      })
      // this.kjClassList =  this.$store.dispatch('data/getSupplierListSaveInVuex')
    },
    loadAccTable(pid, type='1',dataType='',isJson=false, func=undefined) {
      const loading = this.$loading({
        lock: true,
        text: 'Loading',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.2)'
      })
      this.tableDataAcc = []
      this.tableProcessIdAcc = pid
      if(isJson){
         getInvoiceVocherListByData(pid, type,dataType).then((res) => {
            this.tableDataAccCode(res)
            func && func()
            loading.close()
          }).catch((err) => {
            this.$message.error(err.message||'暂不支持此类型')
            loading.close()
          })
          return
      }
         getInvoiceVocherListByCode(pid, type,dataType).then((res) => {
          this.tableDataAccCode(res)
          func && func()
          loading.close()
        }).catch((err) => {
          this.$message.error(err.message||'暂不支持此类型')
          loading.close()
        })
    },
    tableDataAccCode(res) {
        const list = res && res.data ? res.data : []
        this.voucherCode = res && res.code
        this.tableDataAcc = list || []
        this.ledgerAccountList = res && res.accounts
        this.upSubIdByFind()
    },
     upSubIdByFind() {
      if (this.tableDataAcc && this.tableDataAcc.length > 0 && this.kjClassList && this.kjClassList.length > 0) {
        for (let i = 0; i < this.tableDataAcc.length; i++) {
          if (this.tableDataAcc[i].findSub && !this.tableDataAcc[i].findSubId) {
            try {
              const fundsCode = this.kjClassList.find(item => item.treePath.startsWith(this.tableDataAcc[i].subject + '^')  && item.glAccountName == this.tableDataAcc[i].findSub)
              if (fundsCode) {
                this.tableDataAcc[i].findSubId = fundsCode.glAccountCode
                this.tableDataAcc[i].subject = fundsCode.glAccountCode
              }
            } catch (error) {
              console.log('error', error)
            }
          }
        }
      }
    },
    saveYacc() {
      this.saveAccTable(this.code, () => {
        this.dialogYacc = false
        this.$emit('refreshSave')
      })
    },
    saveAccTable(type, func=undefined) {
      const loading = this.$loading({
        lock: true,
        text: 'Loading',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.2)'
      })
      saveVoucher(this.tableProcessIdAcc, type, this.monthDateAcc,this.tableDataAcc,'',this.ledgerAccount).then((data) => {
        if (data.result ) {
          this.$message.success('保存成功')
          func && func()
        } else {
          this.$message.error(data.msg  || '保存失败')
        }
        loading.close()
      }).catch((err) => {
        this.$message.error('保存失败')
        loading.close()
      })
    },
    handleYacc() {
      this.dialogYacc=false
    },
  }
}
</script>
<style scoped>
</style>
