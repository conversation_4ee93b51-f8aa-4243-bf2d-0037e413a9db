<template>
    <div class="image-rotate">
      <div class="image-rotate__controls" style="display: flex;align-items: flex-start;gap: 16px;z-index: 2000">
        <i style="font-size: 30px;color: #19c760"  class="el-icon-refresh-left" @click="rotateImage(-90)"></i>
        <i style="font-size: 30px;color: #19c760" class="el-icon-refresh-right"  @click="rotateImage(90)"></i>
        <i style="font-size: 30px;color: #19c760" class="el-icon-zoom-in" @click="fangda()"></i>
        <i style="font-size: 30px;color: #19c760" class="el-icon-zoom-out" @click="suoxiao()"></i>
      </div>
      <img :src="src" :style="style" style="transform-origin: center;margin-top: 20px"/>
    </div>
</template>

<script>
export default {
  name: 'ImageRotate',
  props: {
    src: {
      type: String,
      required: true
    },
    width: {
      type: Number,
      default: 100
    },
    height: {
      type: Number,
      default: 100
    },
    rotate: {
      type: Number,
      default: 0
    }
  },
  watch: {
    src: function (val) {
      this.rotateData = 0;
    }
  },
  data() {
    return {
      rotateData: this.rotate,
      beishu: 1
    }
  },
  computed: {
    style() {
      return {
        width: `${this.width}%`,
        height: `${this.height}%`,
        transform: `rotate(${this.rotateData}deg) scale(${this.beishu})`
      }
    }
  },
  methods: {
    rotateImage(tye) {
      this.rotateData += tye
      // this.rotate = (this.rotate + 90) % 360;
    },
    fangda() {
      if (this.beishu < 5) {
        this.beishu += 0.1
      }
    },
    suoxiao() {
      if (this.beishu > 0.5) {
        this.beishu -= 0.1
      }
    }
  }
}
</script>

<style>
.image-rotate {
  /* position: relative; */
  width: 100%;
  height: 100%;
  /* overflow: hidden; */
}
.image-rotate img {
  /* position: absolute;
  top: 0;
  left: 0; */
  width: 100%;
  height: 100%;
  transform: rotate(0deg);
}
.image-rotate__controls {
  /* position: absolute;
  top: 0;
  left: 0; */
  position: relative;
  width: 100%;
  height: 100%;
  z-index: 1;
  display: flex;
  justify-content: center;
  align-items: center;
}
</style>
