<template>
  <div class="process-drawer">
    <el-drawer
      :title="title"
      :before-close="handleClose"
      :visible.sync="drawer"
      :size="componentsWidth"
      :append-to-body="true"
      direction="rtl"
    >
      <div
        id="drawerBody"
        v-loading="loadingComponent"
      >

        <!-- 审批状态图片 -->
        <!--        <div style="position: absolute;right: 20px;top: 50px;z-index: 99999">-->
        <!--          <img width="150" height="150" src="https://timgsa.baidu.com/timg?image&quality=80&size=b9999_10000&sec=1596003145760&di=5fd9d50ada578f10652a6d471e502bf8&imgtype=0&src=http%3A%2F%2Fku.90sjimg.com%2Felement_origin_min_pic%2F01%2F41%2F16%2F99573d2cce230db.jpg" alt="">-->
        <!--        </div>-->

        <!-- 表单渲染之前的插槽 -->
        <slot name="beforeFormSlot" />

        <!-- 审批内容 -->
        <div v-if="useComp">
          <component :is="comp" ref="componentRef" :datas="datas" />
        </div>
        <div v-else>
          <!-- 表单 -->
          <div v-if="showaorc">
            <el-radio-group v-model="aorc">
              <el-radio-button label="详情" />
              <el-radio-button label="单据" />
            </el-radio-group>
          </div>
          <div
            v-for="(item,index) in formSchema"
            v-if="formSchema.length > 0 && aorc === '详情'"
          >
            <div v-if="item.key === 'form'">
              <avue-form
                v-model="obj"
                :option="item.option"
              >
                <template slot="menuForm" slot-scope="scope">
                  <!--              <el-button @click="tip">自定义按钮</el-button>-->
                </template>
              </avue-form>
            </div>
            <div v-if="item.key === 'contract'">
              <div style=" width: 100%; display: inline-flex">
                <avue-form
                  v-model="obj"
                  :option="item.option"
                >
                  <template slot="menuForm" slot-scope="scope">
                  <!--              <el-button @click="tip">自定义按钮</el-button>-->
                  </template>
                </avue-form>
                <el-upload
                  disabled
                  class="upload-demo"
                  style="position: relative;top: -20px;left: -50px"
                  :action="upload"
                  :on-preview="handlePreview"
                  multiple
                  :limit="3"
                  :file-list="obj.contractimageList"
                />
                <el-dialog :visible.sync="dialogVisible" append-to-body>
                  <!-- <img width="100%" :src="dialogImageUrl" alt=""> -->
                  <image-rotate :src="dialogImageUrl"></image-rotate>
                </el-dialog>
              </div>
            </div>
            <div v-if="item.key === 'crud'">

              <avue-crud
                v-model="obj"
                :data="obj.cruddata[`data${index}`]"
                :option="item.option"
              >
              <template slot="menuRight">
                  <el-button size="mini" @click="downExcel(obj.cruddata[`data${index}`])">下载excel</el-button>
              </template>
            </avue-crud>
            <el-upload
                disabled
                class="upload-demo"
                :action="upload"
                :on-preview="handlePreview"
                multiple
                :limit="3"
                :file-list="obj.fileList"
              />
              <el-dialog :visible.sync="dialogVisible" append-to-body>
                <!-- <img width="100%" :src="dialogImageUrl" alt=""> -->
                <image-rotate :src="dialogImageUrl"></image-rotate>
              </el-dialog>
            </div>
            <div v-if="item.key === 'shui-crud'  && '有' == obj.invoice">
              <avue-crud
                v-model="obj"
                :data="obj.totalShuiList"
                :option="item.option"
              />
            </div>
            <!-- v-show="item.key === 'image'" -->
            <div v-if="obj.fileList && obj.fileList.length > 0" v-show="item.key === 'image'">
              <el-upload
                disabled
                class="upload-demo"
                :action="upload"
                :on-preview="handlePreview"
                multiple
                :limit="3"
                :file-list="obj.fileList"
              />
              <el-dialog :visible.sync="dialogVisible" append-to-body>
                <!-- <img width="100%" :src="dialogImageUrl" alt=""> -->
                <image-rotate :src="dialogImageUrl"></image-rotate>
              </el-dialog>
            </div>
            <div v-if="item.key === 'group-crud'">
              <div v-for="(items,indexs) in obj.groupsize">
                <div v-show="obj.grouptypelist[indexs] !== 'noshow'">
                  <avue-crud
                    ref="crud"
                    :data="obj.cruddata[`data${index}-${indexs}`]"
                    :option="item[`${obj.grouptypelist[indexs]}`]"
                  />
                  <el-upload
                    disabled
                    class="upload-demo"
                    :action="upload"
                    :on-preview="handlePreviewg"
                    multiple
                    :limit="3"
                    :file-list="obj.fileListg[indexs]"
                  />
                </div>
              </div>
              <el-dialog :visible.sync="dialogVisibleg" append-to-body>
                <!-- <img width="100%" :src="dialogImageUrlg" alt=""> -->
                <image-rotate :src="dialogImageUrlg"></image-rotate>
              </el-dialog>
            </div>

          </div>
          <div v-if="aorc === '单据'">
            <component :is="comp" ref="componentRef" :datas="datas" />
          </div>
        </div>

        <!-- 表单渲染之后的插槽 -->
        <slot name="afterFormSlot" />

        <!-- 审批流程 -->
        <el-divider content-position="left">审批流程</el-divider>
        <el-timeline id="timelineBody">
          <!-- 发起申请 -->
          <el-timeline-item
            :timestamp="timeline.start.content"
            color="#67C23A"
            size="large"
            placement="top"
          >
            <div style="display: flex;justify-content: space-between;">
              <div style="display: flex;justify-content: flex-start;align-items: center;align-content: center">
                <el-avatar shape="square" size="large" :src="timeline.start.item.avatar" />
                <div style="margin-left: 10px;color: #333333;">{{ timeline.start.item.name }}</div>
              </div>
              <div style="display: flex;align-items: flex-end;flex-direction: column">
                <div style="font-size: 12px;color: #909399;margin-bottom: 10px;">{{ timeline.start.item.sponsorTime }}</div>
                <el-tooltip v-if="!!timeline.start.reason && timeline.start.reason.length > 22" effect="dark" placement="top">
                  <div slot="content">
                    <div v-html="timeline.start.reason" />
                  </div>
                  <div style="font-size: 12px;color: #909399;cursor: default;">{{ timeline.start.reason.substr(0,20) }}...</div>
                </el-tooltip>
                <div v-else style="font-size: 12px;color: #909399">{{ timeline.start.reason }}</div>
              </div>
            </div>
          </el-timeline-item>

          <!-- 审批人 -->
          <el-timeline-item
            v-for="(activity, index) in splist"
            :key="index"
            :timestamp="activity.content"
            :color="activity.color"
            :size="activity.size"
            placement="top"
          >
            <div style="display: flex;justify-content: space-between;">
              <div style="display: flex;justify-content: flex-start;align-items: center;align-content: center">
                <!--                <el-avatar shape="square" size="large" :src="activity.item.avatar"></el-avatar>-->
                <el-avatar v-if="activity.item.sp_type=='0'" shape="square" size="large" :src="activity.item.avatar" />
                <span v-if="activity.item.sp_type=='1'" class="diy-avatar">{{ activity.item.avatar|strSubFil }}</span>
                <!--                <div style="margin-left: 10px;color: #333333;">{{activity.item.name}}</div>-->
                <div v-if="activity.item.userlist && !activity.item.name" style="margin-left: 10px;color: #333333;">{{ activity.item.sp_type=='0'?activity.item.name:activity.item.avatar }}  {{ '：'+ rolepeoples(activity.item.userlist) }}</div>
                <div v-else style="margin-left: 10px;color: #333333;">{{ activity.item.sp_type=='0'?activity.item.name:activity.item.avatar }} </div>

              </div>
              <div style="display: flex;align-items: flex-end;flex-direction: column">
                <div style="font-size: 12px;color: #909399;margin-bottom: 10px;">{{ activity.commentTime }}</div>
                <el-tooltip v-if="!!activity.comment && activity.comment.length > 22" effect="dark" placement="top">
                  <div slot="content">
                    <div v-html="activity.comment" />
                  </div>
                  <div style="font-size: 12px;color: #909399;cursor: default;">{{ activity.comment.substr(0,20) }}...</div>
                </el-tooltip>
                <div v-else style="font-size: 12px;color: #909399">{{ activity.comment }}</div>
              </div>
            </div>
          </el-timeline-item>

          <!-- 抄送人 -->
          <template v-if="timeline.status != 4">
          <el-timeline-item
            :timestamp="timeline.ccs.content"
            :color="timeline.ccs.color"
            :size="timeline.ccs.size"
            placement="top"
          >
            <el-row class="timelineContent">
              <div v-for="people in ccslist" style="margin-right:10px">
                <el-tooltip class="item" effect="dark" :content="people.name" placement="top">
                  <el-avatar shape="square" size="large" :src="people.avatar" />
                </el-tooltip>
              </div>
            </el-row>
          </el-timeline-item>
          </template>
        </el-timeline>

        <div v-if="innerSpCommentShow">
          <div style="margin-bottom: 10px;">{{ approveDialogTitle }}</div>
          <el-form ref="ruleForm" :model="approveDialogForm">
            <el-form-item prop="comment">
              <el-input v-model="approveDialogForm.comment" type="textarea" placeholder="请输入审批意见" />
            </el-form-item>
          </el-form>
          <div class="dialog-footer" style="text-align: right;">
            <el-button size="small" @click="cancelForm">取 消</el-button>
            <el-button size="small" type="success" @click="stagingHandle">暂 存</el-button>
            <el-button size="small" type="primary" :loading="loading" @click="submitForm">{{ loading ? '提交中 ...' : '提 交' }}</el-button>
          </div>
        </div>

        <div v-if="(btnIsShow && !innerSpCommentShow) || reminderBtnIsShow" class="timelineContent" style="text-align: right;display: block;">
          <el-button v-if="reminderBtnIsShow" size="small" type="warning" @click="reminderHandle">催 办</el-button>
          <template v-if="btnIsShow && !innerSpCommentShow">
            <el-button size="small" @click="jujueHandle">驳 回</el-button>
            <el-button  v-if="!btnIsFdbiz && Math.floor(statusId/10) !== 8 && Math.floor(statusId/10) !== 9" size="small" type="primary" @click="tongyiHandle">同 意</el-button>
            <el-button v-if="btnIsFdbiz || Math.floor(statusId/10) === 8 || Math.floor(statusId/10) === 9" size="small" type="primary" @click="openFdbiz">打开财务系统</el-button>
          </template>
        </div>

        <el-dialog :title="approveDialogTitle" :visible.sync="approveDialog" append-to-body>
          <el-form ref="ruleForm" :model="approveDialogForm" :rules="approveDialogFormRule">
            <el-form-item prop="comment">
              <el-input v-model="approveDialogForm.comment" type="textarea" placeholder="请输入审批意见" />
            </el-form-item>
          </el-form>
          <div slot="footer" class="dialog-footer">
            <el-button size="small" @click="cancelForm">取 消</el-button>
            <el-button size="small" type="primary" :loading="loading" @click="submitForm">{{ loading ? '提交中 ...' : '确 定' }}</el-button>
          </div>
        </el-dialog>
      </div>
    </el-drawer>
  </div>
</template>

<script>
import esForm from 'vue-easy-form'
import Vue from 'vue'
import store from '@/store'
import Avue from '@smallwei/avue'
import { UPLOAD_URL } from '@/utils/config'
import { taskGetPayment } from '../../api/business/processapi'
import ImageRotate from '@/components/ImageRotate/index'
import XLSX from 'xlsx';
import { saveAs } from 'file-saver';

const processApi = require('@/api/system/process')
Vue.use(esForm)
Vue.use(Avue)
const DEFAULT_APPROVAL_FORM = {
  type: null,
  comment: null
}

export default {
  components: {
    ImageRotate
  },
  name: 'WorkFlowProcessDrawer',
  filters: {
    strSubFil(s) {
      if (!s) {
        return ''
      }
      return s.substring(0, 1)
    }
  },
  props: {
    componentsWidth: {
      type: String,
      required: false,
      default: '65%'
    }
  },
  data() {
    return {
      upload: UPLOAD_URL,
      title: '审批',
      drawer: false,
      loading: false,
      loadingComponent: false,
      dialogImageUrlg: '',
      dialogVisibleg: false,
      timeline: {
        status: null,
        start: {
          content: '发起申请',
          item: {
            id: 0,
            name: '',
            avatar: ''
          }
        },
        sp: [],
        ccs: {
          content: '抄送(0人)',
          items: []
        }
      },
      data: [],
      obj: {},
      dialogImageUrl: '',
      dialogVisible: false,
      formValue: {},
      formSchema: undefined,
      flowCode: null,
      processId: undefined,
      statusId: undefined,
      showaorc: false,
      statusid: 0,
      aorc: '详情',
      btnIsShow: false,
      btnIsFdbiz: false,
      currentSpId: null,
      approveDialogTitle: '审批同意意见',
      approveDialog: false,
      approveDialogForm: Object.assign({}, DEFAULT_APPROVAL_FORM),
      innerSpCommentShow: false,
      reminderBtnIsShow: false,
      approveDialogFormRule: {
        comment: [
          { required: true, message: '请填写拒绝理由', trigger: 'blur' }
        ]
      },

      // 自定义显示组件
      comp: resolve => require([`@/components/approval/DefaultComponent.vue`], resolve),
      useComp: false,
      datas: {}

    }
  },
  computed: {
    splist() {
      return this.timeline.sp && this.timeline.status != 4 && this.timeline.sp || []
    },
    ccslist(){
      return this.timeline.ccs.items && this.timeline.status != 4 && this.timeline.ccs.items|| []
    }
  },
  methods: {
    downExcel(data) {
      // idx
      // 创建一个工作簿
      const ws = XLSX.utils.json_to_sheet(data);
      const wb = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(wb, ws, 'Sheet1');

      // 生成 Excel 文件
      const wbout = XLSX.write(wb, { bookType: 'xlsx', type: 'array' });
      const blob = new Blob([wbout], { type: 'application/octet-stream' });

      // 下载文件
      saveAs(blob, 'data.xlsx');
    },
    async reminderHandle() {
      const loading = this.$loading({
        lock: true,
        text: '操作中...',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      })
      try {
        const res = await processApi.reminder(this.processId)
        this.$message.success("催办成功")
      } catch (err) {
        this.$message.error('催办失败')
      } finally {
        loading.close()
      }
    },
    downloadFileByUrl(url, name) {
      if (name.indexOf('.pdf') !== -1) {
        window.openWindow(url, name, 800, 600)
        return
      }
      var that = this
      this.getBlob(url, function(blob) {
        that.saveAs(blob, name)
      })
    },
    getBlob(url, cb) {
      var xhr = new XMLHttpRequest()
      xhr.open('GET', url, true)
      xhr.responseType = 'blob'
      xhr.onload = function() {
        if (xhr.status === 200) {
          cb(xhr.response)
        }
      }
      xhr.send()
    },
    saveAs(blob, filename) {
      if (window.navigator.msSaveOrOpenBlob) {
        navigator.msSaveBlob(blob, filename)
      } else {
        var link = document.createElement('a')
        var body = document.querySelector('body')

        link.href = window.URL.createObjectURL(blob)
        link.download = filename

        // fix Firefox
        link.style.display = 'none'
        body.appendChild(link)

        link.click()
        body.removeChild(link)

        window.URL.revokeObjectURL(link.href)
      }
    },
    handlePreview(file) {
      // let { href } = this.$router.resolve({ path: file.response.url })
      console.log(file.response.url)
      if (file.response.url.indexOf('.png') !== -1 || file.response.url.indexOf('.jpeg') !== -1 || file.response.url.indexOf('.jpg') !== -1) {
        this.dialogImageUrl = file.response.url
        this.dialogVisible = true
      } else {
        // window.open(file.response.url)
        this.downloadFileByUrl(file.response.url, file.name)
      }
    },
    handlePreviewg(file) {
      // let { href } = this.$router.resolve({ path: file.response.url })
      console.log(file.response.url)
      if (file.response.url.indexOf('.png') !== -1 || file.response.url.indexOf('.jpeg') !== -1 || file.response.url.indexOf('.jpg') !== -1) {
        this.dialogImageUrlg = file.response.url
        this.dialogVisibleg = true
      } else {
        this.downloadFileByUrl(file.response.url, file.name)
      }
    },
    refrshDef() {
      this.comp = resolve => require([`@/components/approval/DefaultComponent.vue`], resolve)
      this.timeline = {
        status: null,
        start: {
          content: '发起申请',
          item: {
            id: 0,
            name: '',
            avatar: ''
          }
        },
        sp: [],
        ccs: {
          content: '抄送(0人)',
          items: []
        }
      }
    },
    wuLiuProcess(type){
      processApi.getWuLiuProcessById(this.processId).then(res => {
        if (res.data.formSchema) {
          console.log(res)
          this.statusId = res.data.nodeId
          console.log(this.statusId)
          this.formSchema = JSON.parse(res.data.formSchema)
          for (var a = 0; a < this.formSchema.length; a++) {
            var item = this.formSchema[a]
            if (item.key === 'form' || item.key === 'contract') {
              for (var b = 0; b < item.option.column.length; b++) {
                var items = item.option.column[b]
                if (res.data.flowName.indexOf('鹭盛船舶业务付款') !== -1 || res.data.flowName.indexOf('对外付款申请') !== -1 || res.data.flowName.indexOf('经营付款(油品)') !== -1 || res.data.flowName.indexOf('付款申请（财务专用）') !== -1 || res.data.flowName.indexOf('厦门和泰通船舶业务付款') !== -1 || res.data.flowName.indexOf('业务付款流程') !== -1 || res.data.flowName.indexOf('采购付款流程') !== -1 || res.data.flowName.indexOf('船员付款流程') !== -1 || res.data.flowName.indexOf('保险付款流程') !== -1) {
                  if (items.label === '对方单位') {
                    items.dicUrl = store.getters.baseApi + '/api/sysExternalAccount/getAllExternalAccount?id=' + res.data.formValue.fromvalue.opositeUnit
                  }
                  if (items.label === '开户行') {
                    items.dicUrl = store.getters.baseApi + '/api/sysExternalAccount/getAllBank?id=' + res.data.formValue.fromvalue.opositeUnit
                  }
                  if (items.label === '账户号') {
                    items.dicUrl = store.getters.baseApi + '/api/sysExternalAccount/getAllBankNum?id=' + res.data.formValue.fromvalue.opositeUnit + '&bankid=' + res.data.formValue.fromvalue.kaihu
                  }
                } else if (res.data.flowCode === 'CurrentAccountApplication') {
                  console.log(item)
                  if (item.showT === '付款账户信息') {
                    if (items.label === '开户行') {
                      items.dicUrl = store.getters.baseApi + '/api/sysAccount/showBank?id=' + res.data.formValue.fromvalue.paymentBank
                    }
                    if (items.label === '账户号') {
                      items.dicUrl = store.getters.baseApi + '/api/sysAccount/showNum?id=' + res.data.formValue.fromvalue.paymentAaccountNum
                    }
                  } else if (item.showT === '收款账户信息') {
                    if (items.label === '开户行') {
                      items.dicUrl = store.getters.baseApi + '/api/sysAccount/showBank?id=' + res.data.formValue.fromvalue.receiveBank
                    }
                    if (items.label === '账户号') {
                      items.dicUrl = store.getters.baseApi + '/api/sysAccount/showNum?id=' + res.data.formValue.fromvalue.receiveAccountNum
                    }
                  }
                }
                items.disabled = true
              }
            } else if (item.key === 'crud' || item.key === 'group-crud') {
              item.option.addBtn = false
              item.option.menu = false
              for (var i = 0; i < 10; i++) {
                if (!item[`option${i}`]) {
                  continue
                }
                item[`option${i}`].addBtn = false
                item[`option${i}`].menu = false
              }
            }
          }
        }
        this.flowCode = res.data.formValue.flowCode
        this.obj = res.data.formValue.fromvalue
        this.formValue = res.data.formValue.fromvalue
        this.timeline.status = res.data.statusId
        this.timeline.start = res.data.spFlow.start
        this.timeline.sp = res.data.spFlow.sp
        this.statusId = res.data.nodeId
        this.timeline.ccs = res.data.spFlow.ccs
        this.reminderBtnIsShow = res.data.reminder
        if (!type) {
          this.btnIsShow = res.data.process
        }
        // if(res.data.viewName == "PaymentApprove" && (res.data.nodeId == 80 || res.data.nodeId == 90)){
        //   this.btnIsFdbiz = true
        // }
        this.$emit('onOpenCom', (res) => {
          if (res) {
            this.btnIsFdbiz = true
          }
        })

        for (var i = 0; i < this.timeline.sp.length; i++) {
          if (this.timeline.sp[i].content.indexOf('审批中') != -1) {
            this.currentSpId = this.timeline.sp[i].item.id
          }
        }
        console.info('580',res.data)
        if (!res.data.useComponent && res.data.viewName) {
          this.showaorc = true
          this.datas = this.obj
          this.datas.processid = this.processId
          this.datas.flowName = res.data.flowName
          this.comp = resolve => require([`@/components/approval/${res.data.viewName}.vue`], resolve)
        }
        // 自定义显示组件
        if (!!res.data.useComponent && res.data.useComponent && !!res.data.viewName) {
          this.useComp = true
          if (res.data.globalParams) {
            this.datas = res.data.globalParams
            this.datas.processId = this.processId
            this.datas.currentSpId = this.currentSpId
            this.datas.sponsorId = res.data.spFlow.start.item.id
            this.datas.btnIsShow = this.btnIsShow
            this.datas.nodeId = this.statusId
            this.datas.formValue = this.formValue
          }
          this.comp = resolve => require([`@/components/approval/${res.data.viewName}.vue`], resolve)
        }
        this.$emit('onCallback', res.data.globalParams)
        this.$emit('returnForm', this.formValue)
        this.loadingComponent = true
      })
    },
    doInit(type) {
      if (!this.processId) {
        return
      }
      if(type==='nopage'){
        this.wuLiuProcess(type)
        return
      }
      // console.log('doInit', this.processId)
      this.showaorc = false
      this.formSchema = [ ]
      processApi.taskGet(this.processId).then(res => {
        if (res.data.formSchema) {
          console.log(res)
          this.statusId = res.data.nodeId
          console.log(this.statusId)
          this.formSchema = JSON.parse(res.data.formSchema)
          for (var a = 0; a < this.formSchema.length; a++) {
            var item = this.formSchema[a]
            if (item.key === 'form' || item.key === 'contract') {
              for (var b = 0; b < item.option.column.length; b++) {
                var items = item.option.column[b]
                if (res.data.flowName.indexOf('鹭盛船舶业务付款') !== -1 || res.data.flowName.indexOf('对外付款申请') !== -1 || res.data.flowName.indexOf('经营付款(油品)') !== -1 || res.data.flowName.indexOf('付款申请（财务专用）') !== -1 || res.data.flowName.indexOf('厦门和泰通船舶业务付款') !== -1 || res.data.flowName.indexOf('业务付款流程') !== -1 || res.data.flowName.indexOf('采购付款流程') !== -1 || res.data.flowName.indexOf('船员付款流程') !== -1 || res.data.flowName.indexOf('保险付款流程') !== -1) {
                  if (items.label === '对方单位') {
                    items.dicUrl = store.getters.baseApi + '/api/sysExternalAccount/getAllExternalAccount?id=' + res.data.formValue.fromvalue.opositeUnit
                  }
                  if (items.label === '开户行') {
                    items.dicUrl = store.getters.baseApi + '/api/sysExternalAccount/getAllBank?id=' + res.data.formValue.fromvalue.opositeUnit
                  }
                  if (items.label === '账户号') {
                    items.dicUrl = store.getters.baseApi + '/api/sysExternalAccount/getAllBankNum?id=' + res.data.formValue.fromvalue.opositeUnit + '&bankid=' + res.data.formValue.fromvalue.kaihu
                  }
                } else if (res.data.flowCode === 'CurrentAccountApplication') {
                  console.log(item)
                  if (item.showT === '付款账户信息') {
                    if (items.label === '开户行') {
                      items.dicUrl = store.getters.baseApi + '/api/sysAccount/showBank?id=' + res.data.formValue.fromvalue.paymentBank
                    }
                    if (items.label === '账户号') {
                      items.dicUrl = store.getters.baseApi + '/api/sysAccount/showNum?id=' + res.data.formValue.fromvalue.paymentAaccountNum
                    }
                  } else if (item.showT === '收款账户信息') {
                    if (items.label === '开户行') {
                      items.dicUrl = store.getters.baseApi + '/api/sysAccount/showBank?id=' + res.data.formValue.fromvalue.receiveBank
                    }
                    if (items.label === '账户号') {
                      items.dicUrl = store.getters.baseApi + '/api/sysAccount/showNum?id=' + res.data.formValue.fromvalue.receiveAccountNum
                    }
                  }
                }
                items.disabled = true
              }
            } else if (item.key === 'crud' || item.key === 'group-crud') {
              item.option.addBtn = false
              item.option.menu = false
              for (var i = 0; i < 10; i++) {
                if (!item[`option${i}`]) {
                  continue
                }
                item[`option${i}`].addBtn = false
                item[`option${i}`].menu = false
              }
            }
          }
        }
        this.flowCode = res.data.formValue.flowCode
        this.obj = res.data.formValue.fromvalue
        this.formValue = res.data.formValue.fromvalue
        this.timeline.status = res.data.statusId
        this.timeline.start = res.data.spFlow.start
        this.timeline.sp = res.data.spFlow.sp
        this.statusId = res.data.nodeId
        this.timeline.ccs = res.data.spFlow.ccs
        this.reminderBtnIsShow = res.data.reminder
        if (!type) {
          this.btnIsShow = res.data.process
        }
        // if(res.data.viewName == "PaymentApprove" && (res.data.nodeId == 80 || res.data.nodeId == 90)){
        //   this.btnIsFdbiz = true
        // }
        this.$emit('onOpenCom', (res) => {
          if (res) {
            this.btnIsFdbiz = true
          }
        })

        for (var i = 0; i < this.timeline.sp.length; i++) {
          if (this.timeline.sp[i].content.indexOf('审批中') != -1) {
            this.currentSpId = this.timeline.sp[i].item.id
          }
        }
        console.info('580',res.data)
        if (!res.data.useComponent && res.data.viewName) {
          this.showaorc = true
          this.datas = this.obj
          this.datas.processid = this.processId
          this.datas.flowName = res.data.flowName
          this.comp = resolve => require([`@/components/approval/${res.data.viewName}.vue`], resolve)
        }
        // 自定义显示组件
        if (!!res.data.useComponent && res.data.useComponent && !!res.data.viewName) {
          this.useComp = true
          if (res.data.globalParams) {
            this.datas = res.data.globalParams
            this.datas.processId = this.processId
            this.datas.currentSpId = this.currentSpId
            this.datas.sponsorId = res.data.spFlow.start.item.id
            this.datas.btnIsShow = this.btnIsShow
            this.datas.nodeId = this.statusId
            this.datas.formValue = this.formValue
          }
          this.comp = resolve => require([`@/components/approval/${res.data.viewName}.vue`], resolve)
        }
        this.$emit('onCallback', res.data.globalParams)
        this.$emit('returnForm', this.formValue)
        this.loadingComponent = true
      }).catch(e => {

      }).finally(() => {
        this.loadingComponent = false
      })
    },
    handleClose(done) {
      done()
    },
    cancelForm() {
      this.approveDialog = false
      this.innerSpCommentShow = false
    },
    rolepeoples(item) {
      // console.log('fornames',item)
      if (item == null) {
        return
      }
      var peoples = ''
      for (var a = 0; a < item.length; a++) {
        var res = item[a]
        if (res !== null) {
          peoples = peoples + res + ' '
        }
      }
      return peoples
    },
    submitForm() {
      if (this.approveDialogForm.type === 2) {
        // 同意
        this.reqSp()
        return
      }
      this.$refs['ruleForm'].validate((valid) => {
        if(!valid) {
          return false
        }
        this.reqSp()
      })
    },
    reqSp() {
    // 判断拒绝 并且未添加原因
    if (this.approveDialogForm.type == 3 && !this.approveDialogForm.comment) {
        this.$message({ showClose: true, message: '请输入拒绝原因', type: 'error' })
        return
      }
      this.loading = true
      this.approveDialogForm.processId = this.processId
      this.approveDialogForm.nodeId = this.statusId
      this.approveDialogForm.formValue = this.formValue
      if (this.formValue.accountId != 'undefined') {
        this.$nextTick(function() {
          this.formValue.accountId = this.$refs.componentRef.currentRowId ? this.$refs.componentRef.currentRowId : null
        })
      }
      processApi.handleProcess(this.approveDialogForm).then(res => {
        console.info(res.data)
        // if (res.data.resultCode != '0') {
        //   this.msgError(res.data.resultMsg)
        //   return
        // }
        this.$message.success('提交成功')
        this.approveDialog = false
        this.drawer = false
        this.$emit('refresh')
      }).finally(() => {
        this.loading = false
      })
    },
    jujueHandle() {
      this.approveDialogTitle = '审批拒绝意见'
      this.approveDialogForm = Object.assign(this.approveDialogForm, DEFAULT_APPROVAL_FORM)
      this.approveDialogForm.type = 3
      this.initComment()
    },
    tongyiHandle() {
      this.approveDialogTitle = '审批同意意见'
      this.approveDialogForm = Object.assign(this.approveDialogForm, DEFAULT_APPROVAL_FORM)
      this.approveDialogForm.comment = '审批通过'
      this.approveDialogForm.type = 2
      this.initComment()
    },
    stagingHandle() {
      if (this.approveDialogForm.comment) {
        try {
          localStorage.setItem('sp_comment_' + this.processId, JSON.stringify({
            ts: new Date().getTime(),
            value: this.approveDialogForm.comment
          }))
        } catch (e) {
          console.error(e)
        }
      }
      this.innerSpCommentShow = false
    },
    initComment() {
      if (this.flowCode !== 'contractFlow') {
        this.approveDialog = true
        return
      }
      this.innerSpCommentShow = true
      const s = localStorage.getItem('sp_comment_' + this.processId)
      if (s) {
        try {
          const v = JSON.parse(s)
          this.approveDialogForm.comment = v.value
        } catch (e) {
          localStorage.removeItem('sp_comment_' + this.processId)
        }
      }
    },
    openFdbiz() {
      window.open('https://caiwu.successhetai.com/dashboard')
    }
  }
}
</script>
<style>
  .el-drawer{
    overflow: scroll
  }
  .el-drawer :focus{
    outline:0;
  }
</style>
<style rel="stylesheet/scss" lang="scss" scoped>
.my-dialog{
  position: fixed;
  top: 40%;
  min-width: 300px;
  min-height: 200px;
  background: #FFF;
  border-radius: 10px;
  box-shadow: 0 0 8px #DCDCDC;
  overflow: hidden;
  .my-dialog-header{
    // height: 50px;
    padding: 20px;
    // background: #000;
    border-bottom: 1px solid #DCDCDC;
  }
  .my-dialog-body{
    background: #00ff00;
  }
  .my-dialog-footer{
    background: #ff0000;
  }
  >div{
    padding: 20px;
  }
}
>>>.el-drawer{
  &::-webkit-scrollbar {
    display: none;
  }
}
>>>.el-drawer__body{
  &::-webkit-scrollbar {
    width: 5px;
    background: #f1f1f1;
    border-radius: 5px;
  }
  &::-webkit-scrollbar-thumb{
    background: #c1c1c1;
    border-radius: 5px;
  }
}
</style>
<style scoped>
  .process-drawer{
    text-align: left;
  }
  #drawerBody{
    padding: 10px;
  }
  #timelineBody > * {
    text-align: left !important;
  }
  .wh40{
    width: 40px;
    height: 42px;
    padding: 0 !important;
    margin: 0 !important;
  }
  .timelineContent {
    display: flex;
    flex-wrap: wrap;
    justify-content: flex-start;
    align-items: center;
    align-content: space-around;
  }
  .diy-avatar{
    display: inline-block;
    box-sizing: border-box;
    text-align: center;
    color: #fff;
    background: #C0C4CC;
    width: 40px;
    height: 40px;
    line-height: 40px;
    font-size: 14px;
    border-radius: 4px;
    overflow: hidden;
    margin-right: 5px;
  }
</style>
